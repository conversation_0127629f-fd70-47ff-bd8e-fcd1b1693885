# Sisense Integration Setup

This document explains how to configure the application to work with your Sisense instance.

## Environment Configuration

### Development Mode (Placeholder Widgets)
When developing without a Sisense connection, the app will show placeholder widgets that simulate the real dashboard appearance.

**No configuration needed** - just run the app and you'll see placeholder widgets.

### Production Mode (Real Sisense Widgets)
To connect to your actual Sisense instance, set the following environment variable:

```bash
# .env.local or .env
VITE_INSTANCE_URL=https://your-sisense-instance.com
```

## Widget Configuration

The dashboard uses the `WidgetById` component from `@sisense/sdk-ui` with the following structure:

```tsx
<WidgetById
  dashboardOid="5defb5024faf0a20b09a2141"  // Dashboard ID
  widgetOid="5defb5024faf0a20b09a2146"     // Widget ID
  includeDashboardFilters={true}            // Include dashboard filters
  styleOptions={{
    height: 380,                            // Dynamic height based on grid
    width: '100%',
  }}
/>
```

## Dashboard & Widget IDs

The Contract Awards Dashboard uses:
- **Dashboard ID**: `5defb5024faf0a20b09a2141`
- **Widget IDs**: Various IDs from your JSON configuration

### Key Widgets:
- Monthly Value: `5df106fc1d1da61c08f09137`
- YTD Value: `5defb5024faf0a20b09a214a`
- TTM Value: `5defb5024faf0a20b09a2155`
- YTD Value by State Table: `5defb5024faf0a20b09a214f`
- YTD Value by Mode: `5defb5024faf0a20b09a214b`

## Features

### Automatic Mode Detection
The app automatically detects whether to use:
- **Placeholder widgets** (development without Sisense)
- **Real Sisense widgets** (when VITE_INSTANCE_URL is configured)

### Dynamic Styling
- Widgets automatically size based on grid position
- Responsive height calculation: `position.h * 120px`
- Dashboard filters are included by default
- Error boundaries protect against widget loading issues

### Grid Layout
- 12-column grid system
- Widget positions defined as: `{ x, y, w, h }`
- Automatic spacing and responsive design

## Testing

1. **Development**: Run without VITE_INSTANCE_URL to see placeholders
2. **Production**: Set VITE_INSTANCE_URL to test with real Sisense widgets
3. **Hybrid**: Mix of placeholder and real widgets based on configuration

## Troubleshooting

### Widgets Not Loading
- Check VITE_INSTANCE_URL is correctly set
- Verify dashboard and widget IDs match your Sisense instance
- Ensure Sisense SDK is properly installed: `@sisense/sdk-ui`

### Layout Issues
- Verify widget positions in configuration
- Check grid system (12 columns max)
- Ensure proper height calculations

### Authentication
- Configure Sisense authentication as per SDK documentation
- Set up proper CORS settings in Sisense
