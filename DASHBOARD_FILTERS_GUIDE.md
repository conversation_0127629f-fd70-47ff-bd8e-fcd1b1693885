# Dashboard Filters Implementation Guide

## Overview

This guide explains how to implement custom dashboard filters in your Sisense-powered React application. We've created a flexible filter system that allows users to filter dashboard data using custom UI components while maintaining integration with Sisense widgets.

## Architecture

### 1. Filter Components

#### `DashboardFilters` Component
- **Location**: `src/components/filters/DashboardFilters.tsx`
- **Purpose**: Renders filter UI components (dropdowns, text inputs, date pickers)
- **Features**:
  - Supports multiple filter types: `select`, `text`, `dateRange`
  - Horizontal and vertical layout options
  - Glass morphism styling to match your design
  - Clear all filters functionality

#### `FilterContext` Provider
- **Location**: `src/contexts/FilterContext.tsx`
- **Purpose**: Manages filter state across the application
- **Features**:
  - Dashboard-specific filter state management
  - Filter persistence during navigation
  - Centralized filter state updates

#### `FilteredSisenseWidget` Component
- **Location**: `src/components/widgets/FilteredSisenseWidget/FilteredSisenseWidget.tsx`
- **Purpose**: Enhanced Sisense widget that accepts external filters
- **Features**:
  - Converts custom filters to Sisense filter format
  - Maintains all original WidgetById functionality
  - Seamless integration with existing widgets

### 2. Filter Types

#### Select Filters
```typescript
{
  id: 'state-filter',
  title: 'State',
  type: 'select',
  options: ['All States', 'Alabama', 'Alaska', ...],
  defaultValue: 'All States',
}
```

#### Text Filters
```typescript
{
  id: 'search-filter',
  title: 'Search',
  type: 'text',
  defaultValue: '',
}
```

#### Date Range Filters
```typescript
{
  id: 'year-filter',
  title: 'Year',
  type: 'dateRange',
  defaultValue: null,
}
```

## Implementation Steps

### Step 1: Add Filter Provider to Your App

The `FilterProvider` is already added to `src/main.tsx`:

```tsx
<SisenseContextProvider>
  <FilterProvider>
    <App />
  </FilterProvider>
</SisenseContextProvider>
```

### Step 2: Add Filters to Your Dashboard

```tsx
import { DashboardFilters } from '../../../components/filters/DashboardFilters';

export const YourDashboard: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<any[]>([]);

  return (
    <PageLayout>
      {/* Add filters before your widgets */}
      <DashboardFilters
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Your existing widgets */}
      {widgets.map((widget) => 
        createWidget(widget, dashboardId, activeTab, dashboardFilters)
      )}
    </PageLayout>
  );
};
```

### Step 3: Configure Custom Filters

You can override the default filters by passing `availableFilters`:

```tsx
<DashboardFilters
  onFiltersChange={setDashboardFilters}
  availableFilters={[
    {
      id: 'custom-state-filter',
      title: 'Select State',
      type: 'select',
      options: ['All', 'CA', 'NY', 'TX'],
      defaultValue: 'All',
    },
    {
      id: 'project-search',
      title: 'Project Name',
      type: 'text',
    },
  ]}
  variant="horizontal"
/>
```

## Filter Integration with Sisense

### Automatic Filter Conversion

The `FilteredSisenseWidget` automatically converts your custom filters to Sisense-compatible filters:

```typescript
// Custom filter
{ id: 'state-filter', value: 'California' }

// Converted to Sisense filter
filterFactory.members(DM.ARTBAEconomics.awards.State, ['California'])
```

### Supported Sisense Filter Types

1. **Member Filters**: For categorical data (states, modes, categories)
2. **Date Range Filters**: For date/time filtering
3. **Numeric Range Filters**: For value ranges

## Styling and Theming

Filters automatically inherit your application's theme:

- **Glass morphism effects**: Backdrop blur and transparency
- **Consistent spacing**: Uses theme spacing values
- **Color scheme**: Matches your primary/secondary colors
- **Responsive design**: Adapts to different screen sizes

## Usage Examples

### Basic Implementation
```tsx
// Simple filters with default configuration
<DashboardFilters
  onFiltersChange={setDashboardFilters}
  variant="horizontal"
/>
```

### Advanced Implementation
```tsx
// Custom filters with specific options
<DashboardFilters
  onFiltersChange={setDashboardFilters}
  availableFilters={customFilters}
  variant="vertical"
/>
```

### Filter State Management
```tsx
const [dashboardFilters, setDashboardFilters] = useState<any[]>([]);

// Handle filter changes
const handleFiltersChange = (filters: any[]) => {
  setDashboardFilters(filters);
  // Optional: Save to localStorage, send to analytics, etc.
};
```

## Benefits

1. **User Experience**: Intuitive filtering interface
2. **Performance**: Filters are applied at the Sisense level for optimal performance
3. **Flexibility**: Easy to add new filter types and configurations
4. **Consistency**: Maintains design system across all dashboards
5. **Integration**: Seamless integration with existing Sisense widgets

## Next Steps

1. **Test the filters**: Navigate to the Contract Awards dashboard to see filters in action
2. **Customize filter options**: Update the default filters to match your data
3. **Add to other dashboards**: Implement filters on additional dashboards
4. **Extend functionality**: Add date pickers, multi-select, or custom filter types

The filter system is now ready to use and can be easily extended for additional functionality!
