{"title": "6. State Legislative Initiatives_SDK", "desc": "", "source": "61e84f9771137213003b977f", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "6644E-88F6-9B", "type": "columnar", "columns": [{"width": 100, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 821, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a31", "height": "100px"}], "width": 100, "stretchable": false, "pxlWidth": 1896, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "36px", "defaultWidth": 128, "widgetid": "68655309099a11833ea60a30"}], "width": 100, "stretchable": false, "pxlWidth": 1896, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a2e", "height": "48px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 0}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a34", "height": "48px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 1}, {"elements": [{"minHeight": 68, "maxHeight": 821, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a2f", "height": "48px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 2}, {"elements": [{"minHeight": 68, "maxHeight": 860, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a38", "height": 40}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 3}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a2b", "height": "132px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a2c", "height": "132px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 1}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a33", "height": "132px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 2}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a35", "height": "132px"}], "width": 25, "stretchable": false, "pxlWidth": 474, "index": 3}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "476px", "defaultWidth": 512, "widgetid": "68655309099a11833ea60a2d"}], "width": 50, "stretchable": false, "pxlWidth": 948, "index": 0}, {"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "476px", "defaultWidth": 512, "widgetid": "68655309099a11833ea60a32"}], "width": 50, "stretchable": false, "pxlWidth": 948, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 256, "maxHeight": 2048, "minWidth": 256, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655309099a11833ea60a36", "height": "624px", "autoHeight": "864px"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "32px", "defaultWidth": 128, "widgetid": "68655309099a11833ea60a37"}], "width": 100, "stretchable": false, "pxlWidth": 1896, "index": 0}]}], "pxlWidth": 1896, "index": 0}]}, "original": null, "previewLayout": [], "oid": "68655309099a11833ea60a2a", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "E0434-B4A2-31", "isCascading": false}], "editing": false, "filterToDatasourceMapping": {}, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') .css('background-color','#ffffff');\n\n$('.dashboard-layout-subcell-vertical-divider') .css('background-color','#ffffff');\n\t\n$(\".dashboard-layout-cell:first-of-type\").css('max-height','60px !important');\n\n})\n\n\n", "allowChangeSubscription": false, "isPublic": null, "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "E0434-B4A2-31", "isCascading": false}, {"jaql": {"column": "status_major", "table": "dim_status.csv", "dim": "[dim_status.csv.status_major]", "datatype": "text", "title": "Status", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "36A2C-7C47-12", "isCascading": false, "disabled": false}], "parentFolder": "68654e16099a11833ea60a10", "settings": {"autoUpdateOnFiltersChange": true}, "lastOpened": null, "defaultFilterRelations": null, "filterRelations": [], "widgets": [{"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655309099a11833ea60a2b", "desc": null, "source": "65b192f215b06800405d4d04", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": ["BC822-4089-B6"]}, "panels": [{"name": "value", "items": [{"jaql": {"table": "dim_id.csv", "column": "id", "dim": "[dim_id.csv.id]", "datatype": "text", "merged": true, "agg": "count", "title": "Bills Introduced:"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"color": "#00cee6", "type": "color"}}, "instanceid": "27B5C-E337-2E"}]}, {"name": "secondary", "items": []}, {"name": "min", "items": [{"jaql": {"formula": "0", "title": "0 (default)"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}}, "instanceid": "F6470-A2DA-AB"}]}, {"name": "max", "items": [{"jaql": {"formula": "100", "title": "100 (default)"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}}, "instanceid": "05885-3D7A-40"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "FD1B4-A527-BB", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "prevSortObjects": [], "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655309099a11833ea60a2c", "desc": null, "source": "65b192f215b06800405d4d05", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"table": "dim_id.csv", "column": "id", "dim": "[dim_id.csv.id]", "datatype": "text", "merged": true, "agg": "count", "title": "Passed a Chamber:"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"color": "#00cee6", "type": "color"}}}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "dim_status.csv", "column": "status", "dim": "[dim_status.csv.status]", "datatype": "text", "merged": true, "title": "status", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["Introduced", "Signed"]}}, "collapsed": false, "datasource": {"title": "tiac_legislation", "fullname": "LocalHost/tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "address": "LocalHost", "database": "atiacXwAalegislation"}}}]}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "status", "title": "status", "singular": "status", "plural": "status"}]}}, "instanceid": "FD1B4-A527-BB", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "lastOpened": null}, {"title": "Revenue Increases Proposed in Legislation*", "type": "chart/pie", "subtype": "pie/classic", "oid": "68655309099a11833ea60a2d", "desc": null, "source": "65b192f215b06800405d4d06", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": ["BC822-4089-B6"]}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "fact_category.csv", "column": "category", "dim": "[fact_category.csv.category]", "datatype": "text", "merged": true, "title": "Type of Bill"}, "format": {"members": {"Electric Vehicle Fee": {"color": "#00cee6", "title": "Electric Vehicle Fee", "sortData": "Electric Vehicle Fee", "inResultset": true}, "Local Funding": {"color": "#9b9bd7", "title": "Local Funding", "sortData": "Local Funding", "inResultset": true}, "Motor Fuel Tax": {"color": "#6EDA55", "title": "Motor Fuel Tax", "sortData": "Motor Fuel Tax", "inResultset": true}, "Nonfunding": {"color": "#fc7570", "title": "Nonfunding", "sortData": "Nonfunding", "inResultset": true}, "One-Time Funding": {"color": "#fbb755", "title": "One-Time Funding", "sortData": "One-Time Funding", "inResultset": true}, "Recurring Revenue": {"color": "#218A8C", "title": "Recurring Revenue", "sortData": "Recurring Revenue", "inResultset": true}, "Road Usage Charge": {"color": "#06e5ff", "title": "Road Usage Charge", "sortData": "Road Usage Charge", "inResultset": true}}}, "instanceid": "16E3F-9A43-38"}]}, {"name": "values", "items": [{"jaql": {"table": "fact_category.csv", "column": "id", "dim": "[fact_category.csv.id]", "datatype": "text", "merged": true, "agg": "count", "title": "Number of Bills"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}}, "instanceid": "1103F-1E8F-03"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "types": {"count": false, "percentage": false, "relative": false, "totals": false}, "stacked": false, "stackedPercentage": false}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": false, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "hideMinMax": false, "isIntervalEnabled": false, "intervalJumps": 1}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "hideMinMax": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_bill", "title": "Type of Bill", "singular": "Type of Bill", "plural": "Type of Bill"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "White", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "C14CF-D352-3E", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "68655309099a11833ea60a2a", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n\t$(element).find(\".widget-toolbar-btn .btn__text\").style('color', 'white', 'important');\n});", "prevSortObjects": [], "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "68655309099a11833ea60a2e", "desc": null, "source": "65b192f215b06800405d4d07", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "stname", "title": "stname", "singular": "stname", "plural": "stname"}]}, "title": {"titleMenuEnabled": false, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#117899", "titleAlign": "center"}}, "instanceid": "D1D56-009D-A4", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655309099a11833ea60a2a", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68655309099a11833ea60a2f", "desc": null, "source": "65b192f215b06800405d4d08", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "TextBlock", "text": "", "style": {"text-align": "center", "font-weight": "bold", "font-size": "24px", "padding": 0, "margin": 0}}]}], "actions": [{"type": "Action.OpenUrl", "title": "Legislation History", "url": "https://transportationinvestment.org/research/reports/"}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 800, "light": 500, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#fff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "white", "backgroundColor": "#b31622", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 8, "style": {"padding": "10", "font-weight": "bold"}}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 40}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "2448A-6E79-BC", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "68655309099a11833ea60a2a", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('ul.action-set', element).css('margin','0');\n\t   $('ul.action-set', element).css('padding','0');\n\t   $('.btn__text', element).css('padding','8px 20px');\n\t   $('.btn__text', element).css('font-weight','bold');\n\t$(element).parent().height('80');\n});", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655309099a11833ea60a30", "desc": null, "source": "65b192f215b06800405d4d09", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<span style=\"font-size: 32px;\"><b>2025 State Transportation Funding Measures</b></span>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "A1978-3E8B-21", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655309099a11833ea60a2a", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().css('max-height','185px');\n\t$(element).parent().find(\"#editor-3\").css('max-height','185px').css('vertical-align','top');\n\t$(element).parent().find(\"font\").style('font-size', '30px', 'important');\n})", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68655309099a11833ea60a31", "desc": null, "source": "65b192f215b06800405d4d0a", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "Container", "spacing": "none", "items": [{"type": "Image", "url": "https://transportationinvestment.org/wp-content/uploads/2019/11/tiac-dashboard.png", "size": "auto", "spacing": "none", "style": {"width": "100%", "height": "100%", "margin": 0, "padding": 0}}]}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 14, "medium": 20, "large": 30, "extraLarge": 50}, "fontWeights": {"default": 500, "light": 100, "bold": 700}, "containerStyles": {"default": {"backgroundColor": "#ffffff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#3A4356"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}, "pink": {"normal": "#F3879F"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 5, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#EAE9E9"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 57}, "currentCardName": "Vertical Card", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "7C7C0-02AA-9D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "68655309099a11833ea60a2a", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().height('80');\n})", "lastOpened": null}, {"title": "Number of Measures Introduced in 2025", "type": "map/area", "subtype": "areamap/usa", "oid": "68655309099a11833ea60a32", "desc": null, "source": "65b192f215b06800405d4d0b", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "437F1-381B-1B"}]}, {"name": "color", "items": [{"jaql": {"table": "dim_id.csv", "column": "id", "dim": "[dim_id.csv.id]", "datatype": "text", "merged": true, "agg": "count", "title": "Number of Bills"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color": {"type": "color", "color": "#218a8c", "isHandPickedColor": true}}, "instanceid": "FA98C-16FB-86"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "CC2B7-1D8C-D3", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "prevSortObjects": [], "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655309099a11833ea60a33", "desc": null, "source": "65b192f215b06800405d4d0c", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"table": "dim_id.csv", "column": "id", "dim": "[dim_id.csv.id]", "datatype": "text", "merged": true, "agg": "count", "title": "Signed Into Law:"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"color": "#00cee6", "type": "color"}}}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "dim_status.csv", "column": "status", "dim": "[dim_status.csv.status]", "datatype": "text", "merged": true, "title": "status", "filter": {"explicit": true, "multiSelection": true, "members": ["Signed"]}, "collapsed": false, "datasource": {"title": "tiac_legislation", "fullname": "LocalHost/tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "address": "LocalHost", "database": "atiacXwAalegislation"}}}]}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "status", "title": "status", "singular": "status", "plural": "status"}]}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "FD1B4-A527-BB", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "68655309099a11833ea60a34", "desc": null, "source": "65b192f215b06800405d4d0d", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_status.csv", "column": "status_major", "dim": "[dim_status.csv.status_major]", "datatype": "text", "merged": true, "title": "Status"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "dim_status.csv", "column": "status_major", "dim": "[dim_status.csv.status_major]", "datatype": "text", "merged": true, "title": "Status", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["N\\A"]}}, "collapsed": false, "datasource": {"title": "tiac_legislation", "fullname": "LocalHost/tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "address": "LocalHost", "database": "atiacXwAalegislation"}}}]}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "status", "title": "Status", "singular": "Status", "plural": "Status"}]}}, "instanceid": "CB139-0C1C-1B", "displayMenu": false, "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "fullResults": [["2018-01-01T00:00:00"], ["2017-01-01T00:00:00"], ["2016-01-01T00:00:00"], ["2015-01-01T00:00:00"], ["2014-01-01T00:00:00"], ["2013-01-01T00:00:00"], ["2012-01-01T00:00:00"], ["2011-01-01T00:00:00"], ["2010-01-01T00:00:00"], ["2009-01-01T00:00:00"], ["2008-01-01T00:00:00"], ["2007-01-01T00:00:00"], ["2006-01-01T00:00:00"]], "listOpen": false, "zIndex": 100, "AllMembersSelected": true, "allFilteredMembersSelected": true, "multiSelect": true, "membersSelected": ["2018-01-01T00:00:00", "2017-01-01T00:00:00", "2016-01-01T00:00:00", "2015-01-01T00:00:00", "2014-01-01T00:00:00", "2013-01-01T00:00:00", "2012-01-01T00:00:00", "2011-01-01T00:00:00", "2010-01-01T00:00:00", "2009-01-01T00:00:00", "2008-01-01T00:00:00", "2007-01-01T00:00:00", "2006-01-01T00:00:00"], "excludeMembers": [], "AllMemberSelected": false, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655309099a11833ea60a2a", "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655309099a11833ea60a35", "desc": null, "source": "65b192f215b06800405d4d0e", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": ["BC822-4089-B6"]}, "panels": [{"name": "value", "items": [{"jaql": {"table": "dim_id.csv", "column": "revenue", "dim": "[dim_id.csv.revenue]", "datatype": "numeric", "agg": "sum", "title": "Revenue Approved:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#00cee6", "type": "color"}}, "instanceid": "2F85E-B424-24"}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "dim_status.csv", "column": "status", "dim": "[dim_status.csv.status]", "datatype": "text", "merged": true, "title": "status", "filter": {"explicit": false, "multiSelection": true, "all": true}, "collapsed": false, "datasource": {"title": "tiac_legislation", "fullname": "LocalHost/tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "address": "LocalHost", "database": "atiacXwAalegislation"}}, "instanceid": "1627D-C4BF-7E"}]}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "status", "title": "status", "singular": "status", "plural": "status"}]}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "FD1B4-A527-BB", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "prevSortObjects": [], "lastOpened": null}, {"title": "Legislation Introduced", "type": "tablewidget", "subtype": "tablewidget", "oid": "68655309099a11833ea60a36", "desc": null, "source": "65b192f215b06800405d4d0f", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[detail_charts.csv.category]", "[table_legislation.csv.status]"], "all": false, "ids": []}, "panels": [{"name": "columns", "items": [{"jaql": {"table": "dim_id.csv", "column": "state", "dim": "[dim_id.csv.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "6FBE1-1CC9-<PERSON>"}, {"jaql": {"table": "dim_id.csv", "column": "bill", "dim": "[dim_id.csv.bill]", "datatype": "text", "merged": true, "title": "<PERSON>"}, "instanceid": "7A9BD-8C69-4C"}, {"jaql": {"table": "dim_id.csv", "column": "category_sum", "dim": "[dim_id.csv.category_sum]", "datatype": "text", "merged": true, "title": "Type(s) of Measure"}, "instanceid": "8F527-4227-99"}, {"jaql": {"table": "dim_status.csv", "column": "status_major", "dim": "[dim_status.csv.status_major]", "datatype": "text", "merged": true, "title": "Status"}, "instanceid": "08F3F-29C8-D6"}, {"jaql": {"table": "dim_id.csv", "column": "description", "dim": "[dim_id.csv.description]", "datatype": "text", "merged": true, "title": "Description"}, "instanceid": "6AAE7-F4A4-1D"}, {"jaql": {"table": "dim_id.csv", "column": "revenue", "dim": "[dim_id.csv.revenue]", "datatype": "numeric", "title": "Revenue Approved", "sort": "desc"}, "instanceid": "7E925-8AC2-BD", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"borders/all": true, "borders/grid": false, "borders/rows": false, "borders/columns": false, "width/content": true, "width/window": false, "colors/headers": true, "colors/rows": true, "colors/columns": false, "wordwrap/headers": true, "wordwrap/rows": true, "scroll": false, "pageSize": 20, "tableState": {"time": 1726255191601, "start": 0, "length": 20, "order": [[0, "asc"]], "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}, "columns": [{"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}], "colResize": {"columns": ["103.688px", "78.4375px", "267.172px", "128.266px", "985px", "89.3125px"], "tableSize": 1651.88}, "childRows": [], "iScrollerTopRow": 0, "iScroller": 0, "headers": [{"title": "State", "dim": "[dim_id.csv.state]", "datatype": "text"}, {"title": "<PERSON>", "dim": "[dim_id.csv.bill]", "datatype": "text"}, {"title": "Type(s) of Measure", "dim": "[dim_id.csv.category_sum]", "datatype": "text"}, {"title": "Status", "dim": "[dim_status.csv.status_major]", "datatype": "text"}, {"title": "Description", "dim": "[dim_id.csv.description]", "datatype": "text"}, {"title": "Revenue Approved", "dim": "[dim_id.csv.revenue]", "datatype": "numeric"}]}, "automaticHeight": false, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "bill_number", "title": "<PERSON>", "singular": "<PERSON>", "plural": "<PERSON>"}, {"id": "type(s)_of_measure", "title": "Type(s) of Measure", "singular": "Type(s) of Measure", "plural": "Type(s) of Measure"}, {"id": "status", "title": "Status", "singular": "Status", "plural": "Status"}, {"id": "description", "title": "description", "singular": "description", "plural": "description"}, {"id": "revenue", "title": "revenue", "singular": "revenue", "plural": "revenue"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}}, "instanceid": "A168F-52E6-5E", "displayMenu": false, "currentPage": 0, "custom": {"barcolumnchart": {"type": "tablewidget", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "disallowSelector": true, "triggersDomready": true, "selector": false, "dataTypes": {"dimensions": true, "measures": false, "filter": false}, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655309099a11833ea60a2a", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655309099a11833ea60a37", "desc": null, "source": "65b192f215b06800405d4d10", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"2\"><div style=\"text-align: left;\">*Nonfunding- Measures that support transportation investment without directly providing new revenue. Including (but not limited to) infrastructure banks, lockboxes, ending diversions, and studies or tasks forces.&nbsp;</div></font>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "CDA0E-F717-EA", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655309099a11833ea60a2a", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68655309099a11833ea60a38", "desc": null, "source": "65b192f215b06800405d4d11", "datasource": {"address": "LocalHost", "title": "tiac_legislation", "id": "aLOCALHOST_aTIACXwAaLEGISLATION", "database": "atiacXwAalegislation", "fullname": "LocalHost/tiac_legislation"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "TextBlock", "text": "", "style": {"text-align": "center", "font-weight": "bold", "font-size": "24px", "padding": 0, "margin": 0}}]}], "actions": [{"type": "Action.OpenUrl", "title": "View Analysis", "url": "https://transportationinvestment.org/research/state-legislation/"}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 800, "light": 500, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#fff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "white", "backgroundColor": "#b31622", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 8, "style": {"padding": "10", "font-weight": "bold"}}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 40}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "2448A-6E79-BC", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('ul.action-set', element).css('margin','0');\n\t   $('ul.action-set', element).css('padding','0');\n\t   $('.btn__text', element).css('padding','8px 20px');\n\t   $('.btn__text', element).css('font-weight','bold');\n\t$(element).parent().height('80');\n});", "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "68655309099a11833ea60a2a", "lastOpened": null}], "hierarchies": []}