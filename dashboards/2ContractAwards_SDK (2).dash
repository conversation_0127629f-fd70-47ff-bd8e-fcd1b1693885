{"title": "2. Contract Awards_SDK", "desc": "", "source": "5defb5024faf0a20b09a215a", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "0951E-C8BC-C9", "type": "columnar", "columns": [{"width": 32.59207783182766, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 888, "minWidth": 128, "maxWidth": 2048, "height": 234.297, "defaultWidth": 512, "widgetid": "68654950099a11833ea60950"}], "width": 100, "stretchable": false, "pxlWidth": 411.633, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "52px", "defaultWidth": 128, "widgetid": "68654950099a11833ea60951"}], "width": 50, "stretchable": false, "pxlWidth": 205.812, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "defaultWidth": 128, "widgetid": "68654950099a11833ea60952", "height": "52px"}], "width": 50, "stretchable": false, "pxlWidth": 205.812, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "624px", "defaultWidth": 128, "widgetid": "68654950099a11833ea60938"}], "width": 100, "stretchable": false, "pxlWidth": 411.633, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": 384, "defaultWidth": 512, "widgetid": "68654950099a11833ea6095a"}], "width": 100, "stretchable": false, "pxlWidth": 411.633, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": 128, "defaultWidth": 128, "widgetid": "68654950099a11833ea6095b"}], "width": 100, "stretchable": false, "pxlWidth": 411.633, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": 128, "defaultWidth": 128, "widgetid": "68654950099a11833ea6095c"}], "width": 100, "stretchable": false, "pxlWidth": 411.633, "index": 0}]}], "pxlWidth": 411.633, "index": 0}, {"width": 41.19993897489502, "pxlWidth": 520.352, "index": 1, "cells": [{"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "height": "156px", "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68654950099a11833ea60936"}], "width": 50, "stretchable": false, "pxlWidth": 260.172, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68654950099a11833ea60937", "height": "156px"}], "width": 50, "stretchable": false, "pxlWidth": 260.172, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 32, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": 32, "defaultWidth": 512, "widgetid": "68654950099a11833ea60949"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60947", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094d", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60948", "height": "372px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60944", "height": "798px", "autoHeight": "798px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6093f", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094c", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60940", "height": "372px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "178px", "defaultWidth": 512, "widgetid": "68654950099a11833ea60939", "autoHeight": "178px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60953", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094b", "height": "178px", "autoHeight": "178px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60954", "height": "160px", "autoHeight": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6093a", "height": "364px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60959", "height": "364px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "798px", "defaultWidth": 512, "widgetid": "68654950099a11833ea6093e", "autoHeight": "798px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60958", "height": "798px", "autoHeight": "798px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60942", "height": "798px", "autoHeight": "798px"}], "width": 100, "stretchable": false, "pxlWidth": 520.352, "index": 0}]}]}, {"width": 26.20798319327732, "pxlWidth": 331, "index": 2, "cells": [{"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68654950099a11833ea60941", "height": "156px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094f", "height": "320px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094e", "height": "320px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60955", "height": "320px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "320px", "defaultWidth": 512, "widgetid": "68654950099a11833ea6093c"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6094a", "height": "420px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60956", "height": "420px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6093d", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60957", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea6093b", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60943", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60945", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68654950099a11833ea60946", "height": "452px"}], "width": 100, "stretchable": false, "pxlWidth": 331, "index": 0}]}]}]}, "original": null, "previewLayout": [], "oid": "68654950099a11833ea60935", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filters": [{"jaql": {"table": "executive_summary.csv", "column": "mode", "dim": "[executive_summary.csv.mode]", "datatype": "text", "title": "mode", "merged": true, "isDashboardFilter": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "locale": "en-us", "filter": {"explicit": true, "multiSelection": true, "members": ["Bridge & Tunnel", "Highway & Pavement"]}, "collapsed": true}, "instanceid": "3FF0A-B1B1-7A", "isCascading": false}, {"jaql": {"table": "awards", "column": "state", "dim": "[awards.state]", "datatype": "text", "title": "state", "merged": true, "isDashboardFilter": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "locale": "en-us", "filter": {"explicit": false, "multiSelection": true, "all": true}, "collapsed": true}, "instanceid": "8E7E7-71AF-FE", "isCascading": false}], "editing": false, "filterToDatasourceMapping": {}, "parentFolder": "68654e16099a11833ea60a10", "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "collapsed": true, "merged": true, "isDashboardFilter": true, "locale": "en-us", "filter": {"explicit": true, "multiSelection": true, "members": ["Bridge & Tunnel", "Highway & Pavement"]}}, "instanceid": "45EC6-28AE-D6", "isCascading": false, "disabled": false}, {"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "7210F-211F-AE", "isCascading": false, "disabled": false}], "allowChangeSubscription": false, "isPublic": null, "lastOpened": null, "settings": {"autoUpdateOnFiltersChange": true}, "defaultFilterRelations": null, "filterRelations": [], "subscription": {"executionPerDay": 1, "schedule": "* * * * * *", "isDataChange": true, "type": "onUpdate", "reportType": {"inline": true}, "emailSettings": {"isEmail": true, "isPdf": false}, "context": {"dashboardid": "68654950099a11833ea60935"}, "startAt": "2025-07-02", "tzName": "America/New_York", "timezone": 240, "active": false, "subscribe": false}, "widgets": [{"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68654950099a11833ea60936", "desc": null, "source": "68634ceb099a11833ea5feee", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "(([A95D1-A0F], [8D914-D86], [685F8-4B9]))", "context": {"[8D914-D86]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[A95D1-A0F]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[685F8-4B9]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "locale": "en-us"}}, "title": "Monthly Value"}, "format": {"color": {"colorIndex": 0, "type": "color"}, "mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "2ADE7-786D-8F"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "SUM(([57DE6-F5C], [327AF-E0E],[1F720-ADF]))", "context": {"[327AF-E0E]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[57DE6-F5C]": {"table": "awards", "column": "numpro", "dim": "[awards.numpro]", "datatype": "numeric", "agg": "sum", "title": "Total numpro"}, "[1F720-ADF]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "locale": "en-us"}}, "title": "Number of Projects:"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}}, "instanceid": "F9922-0672-C3"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d80e0c49498202f4029d46b", "caption": "_drill_stateawards_month"}}, "dashboardid": "68654950099a11833ea60935", "drilledDashboardDisplay": {}, "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\n/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "realTimeRefreshing": false, "lastOpened": null, "viewState": {"activeTab": "filters"}}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68654950099a11833ea60937", "desc": null, "source": "68634ceb099a11833ea5feef", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Dates.Date (Calendar)]", "[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": true, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "(( [66042-946], [A20F3-D62]))", "context": {"[66042-946]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[A20F3-D62]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "locale": "en-us"}}, "title": "YTD Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "instanceid": "C10AA-B6C9-C1"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "SUM(([03A75-969], [38637-588], [23BB1-641]))", "context": {"[03A75-969]": {"table": "awards", "column": "numpro", "dim": "[awards.numpro]", "datatype": "numeric", "agg": "sum", "title": "Total numpro"}, "[23BB1-641]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "locale": "en-us"}, "[38637-588]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}}, "title": "Number of Projects:"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}}, "instanceid": "A64A6-D130-81"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d9360795355a1223c5cf550", "caption": "_drill_award_ytd", "folder": null}}, "dashboardid": "68654950099a11833ea60935", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "drilledDashboardDisplay": {}, "lastOpened": null, "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "realTimeRefreshing": false, "viewState": {"activeTab": "filters"}}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68654950099a11833ea60938", "desc": null, "source": "68634ceb099a11833ea5fef0", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<p style=\"margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\" align=\"center\"><font face=\"Open Sans, serif\"><b style=\"font-size: 16px;\">Contract Awards Total $13.3B in May,&nbsp;</b></font></p><p style=\"margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\" align=\"center\"><font face=\"Open Sans, serif\"><span style=\"font-size: 16px;\"><b>YTD 2025 Value of Awards Up 11%</b></span></font></p><p style=\"margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\" align=\"center\"><font face=\"Open Sans, serif\"><span style=\"font-size: 16px;\"><b><br></b></span></font></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">The total value of state and local government contract awards was $13.3 billion in May, up from $11.3 billion in&nbsp;</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">May&nbsp;</span><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">2024, an increase of 18 percent</span><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">.&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\"><br></span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"font-size: 13.3333px; font-family: &quot;Open Sans&quot;, serif;\">The value of awards is up 11 percent through the first five months of 2025, compared to the same time period last year.</span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"font-size: 13.3333px; font-family: &quot;Open Sans&quot;, serif;\">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">Monthly award totals can vary significantly from state to state, based on planned projects and programs.&nbsp; &nbsp;</span></font></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\"><br></span></font></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">Overall awards are a leading indicator of future construction activity expected to break ground in the next 30 to 60 days - depending on the size and scope of a project, actual construction work can take place over a multi-year period.&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></font></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"></p><ul><ul><ul><li style=\"text-align: left;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">24 states&nbsp; increased the value of&nbsp;</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">transportation</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">&nbsp;project awards in&nbsp;<span style=\"font-size: 13.3333px;\">May&nbsp;</span>compared to&nbsp;<span style=\"font-size: 13.3333px;\"><span style=\"font-size: 13.3333px;\">May&nbsp;</span>2024</span>.&nbsp; &nbsp;</span>&nbsp;&nbsp;</li><li style=\"text-align: left;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">States with the highest value of awards in&nbsp;<span style=\"font-size: 13.3333px;\">May&nbsp;</span>include: California, Florida, Illinois, Texas, Missouri, and Minnesota.</span></font></li><li style=\"text-align: left;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">States with major monthly percentage gains included Vermont, Missouri, Delaware, Connecticut, and California.&nbsp;&nbsp;</span></font></li><li style=\"text-align: left;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">Top markets in 2024 were: Texas ($18B), California ($10.3B), Florida ($9.9B), New York ($6.6B), and&nbsp;<span style=\"font-size: 13.3333px;\">Illinois ($5.8B)</span>.&nbsp;&nbsp;</span></font></li></ul></ul></ul><div style=\"text-align: left;\"><br></div><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"text-align: center;\">In 2024, the value of all state and local transportation contract awards was up 5 percent, above record levels in 2022 and 2023.&nbsp; Compared to 2021, the value of awards was up by $42 billion, or nearly 47 percent.&nbsp; &nbsp;&nbsp;<font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">&nbsp;</span></font></span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"text-align: center;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\"><br></span></font></span></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">Total values do not take into account increases in project costs, materials, or inflation, which can vary significantly by project or region.</span></font></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">&nbsp;</span></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\">\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n</p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><i><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif; border: 1pt none windowtext; padding: 0in;\">Updated May 28, 2025</span></i></p><p style=\"text-align: left; margin-bottom: 0in; line-height: normal;\" class=\"MsoNormal\"><br></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">&nbsp;</span></p><div style=\"text-align: left;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\"><br></span></p><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><br></p></div>", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "ED390-C848-2B", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68654950099a11833ea60935", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60939", "desc": null, "source": "68634ceb099a11833ea5fef1", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "0F3E4-06C3-75", "panel": "rows", "format": {"width": 134.3125}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM(([530CA-1BA], [6E9F8-930]))", "context": {"[54AAA-AAB]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[6E9F8-930]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[530CA-1BA]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Value", "datatype": "numeric"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "color": {"type": "color"}}, "field": {"id": "SUM(([530CA-1BA], [6E9F8-930]))", "index": 2}, "instanceid": "90184-A098-2E", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "5AC10-48CD-6A", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in MonthStart", "level": "years", "filter": {"last": {"count": 5, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "instanceid": "A50B7-1174-B7", "panel": "scope", "title": "Years in MonthStart"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "script_old": "", "realTimeRefreshing": false, "lastOpened": null}, {"title": "Monthly Value by State", "type": "map/area", "subtype": "areamap/usa", "oid": "68654950099a11833ea6093a", "desc": null, "source": "68634ceb099a11833ea5fef2", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "7EE43-5D41-4A"}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "sum(([61FCA-AAC], [0C93D-B21], [9FF3E-66C]))", "context": {"[9FF3E-66C]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[61FCA-AAC]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[0C93D-B21]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "Value of Awards:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}, "instanceid": "4A480-194E-CC"}]}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Year", "dim": "[Dates.Year]", "datatype": "numeric", "title": "Year1", "filter": {"explicit": true, "multiSelection": true, "members": ["2019"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "title": "Year1", "disabled": true, "instanceid": "6C2CD-DBDC-BB"}]}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "year1", "title": "Year1", "singular": "Year1", "plural": "Year1"}]}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "0D174-E2CD-B3", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "lastOpened": null, "realTimeRefreshing": false}, {"title": "YTD Value", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea6093b", "desc": null, "source": "68634ceb099a11833ea5fef3", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "instanceid": "DC74F-76F0-E6", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([22E0B-DE8], [F5A62-F2E]))\n", "context": {"[22E0B-DE8]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[F5A62-F2E]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[32719-532]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd"}}, "title": "YTD Value of Awards"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "color": {"colorIndex": 0, "type": "color"}}, "regressionType": "none", "instanceid": "26ACF-0FBB-5D", "valueLabelSettings": {"none": false, "first": false, "last": false, "min": false, "max": false}, "y2": false, "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "disabled": true, "instanceid": "11F18-DC23-A6", "panel": "scope"}, {"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"level": "years", "explicit": false, "multiSelection": true, "all": true}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "A505D-23AF-9C", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": false, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false, "hideMinMax": false, "templateMainYHasGridLines": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}, {"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "calendar end", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false}}, "prevSortObjects": [], "lastOpened": null}, {"title": "YTD Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68654950099a11833ea6093c", "desc": null, "source": "68634ceb099a11833ea5fef4", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[awards.year]", "[awards.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": ["C3CFE-4890-C4"]}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "format": {"members": {}}, "instanceid": "7745C-D9D1-05"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([DEDD4-8F7], [E126E-8B5], [709C9-61B]))", "context": {"[DEDD4-8F7]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[709C9-61B]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}, "[E126E-8B5]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}}, "title": "Value of Awards"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}}, "instanceid": "5A559-7F18-68"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "lastOpened": null, "realTimeRefreshing": false}, {"title": "Monthly Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea6093d", "desc": null, "source": "68634ceb099a11833ea5fef5", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "instanceid": "683B1-C4D7-FC", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([C1DAE-038], [1031A-318]))", "context": {"[1031A-318]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[C1DAE-038]": {"table": "awards", "column": "numpro", "dim": "[awards.numpro]", "datatype": "numeric", "agg": "sum", "title": "Total numpro"}}, "title": "Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "colorIndex": 0}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "477A7-DF53-17"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea6093e", "desc": null, "source": "68634ceb099a11833ea5fef6", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State", "sort": null}, "field": {"id": "[Geography.state]", "index": 0}, "instanceid": "6F566-173E-6D", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM(([20273-937], [98748-8E6]))", "context": {"[98748-8E6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[20273-937]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "sort": "desc", "datatype": "numeric", "title": "Monthly Value", "sortDetails": {"field": 2, "dir": "desc", "sortingLastDimension": true, "measurePath": {"1": "1/1/2025 12:00:00 AM"}, "initialized": true, "isLastApplied": true}}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "SUM(([20273-937], [98748-8E6]))", "index": 2}, "instanceid": "43FD5-19C4-B4", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "sort": null}, "instanceid": "12EF0-59EB-E8", "panel": "columns", "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "sort": null, "filter": {"last": {"count": 5, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "FB004-A842-3D", "panel": "scope", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": true, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "scroll": false}, "instanceid": "B3A5D-325C-EB", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": null, "script": null, "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea6093f", "desc": null, "source": "68634ceb099a11833ea5fef7", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[awards.type]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "7C2DF-6E42-B2", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([7053A-6F9], [3FAC6-349]))", "context": {"[7053A-6F9]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[3FAC6-349]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[F54E4-70F]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd"}}, "datatype": "numeric", "title": "YTD Value"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "(([7053A-6F9], [3FAC6-349]))", "index": 2}, "instanceid": "073CB-4341-90", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "EDFB2-BC57-C6", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"level": "years", "explicit": false, "multiSelection": true, "exclude": {"members": ["2020-01-01T00:00:00"]}}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "B114C-894F-46", "panel": "scope", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false, "imageColumns": []}, "dashboardid": "68654950099a11833ea60935", "script_old": null, "script": null, "lastOpened": null}, {"title": "YTD Value by State", "type": "map/area", "subtype": "areamap/usa", "oid": "68654950099a11833ea60940", "desc": null, "source": "68634ceb099a11833ea5fef8", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "9A573-D578-DA"}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "SUM(([4ED05-C47], [0253F-A80], [AC659-E53]))", "context": {"[4ED05-C47]": {"table": "awards", "column": "ytd_value", "dim": "[awards.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[AC659-E53]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[0253F-A80]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "YTD Value:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "BFEF2-371A-6B"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}]}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "B3D1F-8121-9E", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null, "realTimeRefreshing": false}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68654950099a11833ea60941", "desc": null, "source": "68634ceb099a11833ea5fef9", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "(([67639-792], [13069-875], [96C2C-943]))", "context": {"[13069-875]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[67639-792]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}, "[96C2C-943]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "TTM Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "instanceid": "6F755-70DF-D8"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([70758-87D], [CCA90-454], [94B6A-C50]))", "context": {"[70758-87D]": {"table": "awards", "column": "ttm_numpro", "dim": "[awards.ttm_numpro]", "datatype": "numeric", "agg": "sum", "title": "Total ttm_numpro"}, "[CCA90-454]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[94B6A-C50]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "Number of Projects:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 0, "number": {"separated": true}}}, "instanceid": "07A5C-DCD9-11"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d9362f45355a1223c5cf588", "caption": "_drill_award_ttm", "folder": null}}, "dashboardid": "68654950099a11833ea60935", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "drilledDashboardDisplay": {}, "lastOpened": null, "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "realTimeRefreshing": false}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60942", "desc": null, "source": "68634ceb099a11833ea5fefa", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State", "sort": null}, "field": {"id": "[Geography.state]", "index": 0}, "instanceid": "35978-A95B-73", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([A6AD1-706], [977B8-934]))", "context": {"[A6AD1-706]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[977B8-934]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[368DA-2BF]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd"}}, "title": "YTD Value", "sort": "desc", "datatype": "numeric", "sortDetails": {"field": 2, "dir": "desc", "sortingLastDimension": true, "measurePath": {"1": "2025"}, "initialized": true, "isLastApplied": true}}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "(([A6AD1-706], [977B8-934]))", "index": 2}, "instanceid": "ADD02-BC0F-FC", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "sort": null}, "field": {"id": "[awards.year]", "index": 1}, "instanceid": "26B79-3F54-63", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2020"]}}, "collapsed": false, "sort": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "title": "year", "instanceid": "5B862-92D2-FC", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": true, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "rowsGrandTotal": true, "scroll": false}, "instanceid": "B3A5D-325C-EB", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "YTD Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea60943", "desc": null, "source": "68634ceb099a11833ea5fefb", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "instanceid": "9E9F9-8BAC-B0", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([C90B0-24A], [772AF-E73]))", "context": {"[C90B0-24A]": {"table": "awards", "column": "numpro", "dim": "[awards.numpro]", "datatype": "numeric", "agg": "sum", "title": "Total numpro"}, "[772AF-E73]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[BEE56-25B]": {"table": "awards", "column": "ytd", "dim": "[awards.ytd]", "datatype": "numeric", "title": "ytd"}}, "title": "Number of Awards"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "colorIndex": 0}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "CB0CC-D0F5-F8"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "prevSortObjects": [], "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60944", "desc": null, "source": "68634ceb099a11833ea5fefc", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State", "sortDetails": {"field": 0, "dir": "asc", "sortingLastDimension": true, "measurePath": null, "initialized": true}, "sort": "asc"}, "field": {"id": "[Geography.state]", "index": 0}, "instanceid": "E5166-5A99-C0", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "[CF1BB-3D7]", "context": {"[CF1BB-3D7]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "Trailing 12-Month Totals", "datatype": "numeric", "sort": null}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "[CF1BB-3D7]", "index": 2}, "instanceid": "9314B-C798-56", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart", "sort": null}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}, "width": 129}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_months", "index": 1}, "instanceid": "2ED67-D509-E1", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months", "filter": {"last": {"count": 12, "offset": 0}, "rankingMessage": "", "custom": true}, "collapsed": true, "sort": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "title": "Months", "instanceid": "38262-B6D2-92", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": true, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "months_in_monthstart", "title": "Months in MonthStart", "singular": "Months in MonthStart", "plural": "Months in MonthStart"}, {"id": "months", "title": "months", "singular": "months", "plural": "months"}]}, "rowsGrandTotal": true, "scroll": false}, "instanceid": "B3A5D-325C-EB", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "TTM Value", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea60945", "desc": null, "source": "68634ceb099a11833ea5fefd", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}, "continuous": true}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM([F703C-3BF])\n", "context": {"[F703C-3BF]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "Trailing 12-Month Total Value of Awards"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#00cee6", "type": "color"}}, "regressionType": "linear"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 15, "offset": 0}, "custom": true}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}, "title": "Years in MonthStart"}]}]}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": true}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months", "title": "months", "singular": "months", "plural": "months"}, {"id": "years", "title": "years", "singular": "years", "plural": "years"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": 1577854800000, "max": 1743480000000}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "showNulls": {"Months in MonthStart": false, "Trailing 12-Month Total Value of Awards": false, "Years in MonthStart": false, "State": false, "Mode": false}, "dateLevel": "months", "lastOpened": null}, {"title": "TTM Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea60946", "desc": null, "source": "68634ceb099a11833ea5fefe", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart"}, "format": {"continuous": false, "mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "A338F-ADB2-76", "wpanel": "xAxis", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([86533-346])", "context": {"[86533-346]": {"table": "awards", "column": "ttm_numpro", "dim": "[awards.ttm_numpro]", "datatype": "numeric", "agg": "sum", "title": "Total ttm_numpro"}}, "title": "Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color": {"colorIndex": 0, "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "3F622-82C5-13", "wpanel": "series", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 5, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart", "instanceid": "A29CB-3D16-09", "wpanel": "filters", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_monthstart", "title": "Months in MonthStart", "singular": "Months in MonthStart", "plural": "Months in MonthStart"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": 0, "max": 52}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "showNulls": {"Months in MonthStart": false, "Value": false, "Years in MonthStart": false, "type": false, "state": false, "year": false}, "dateLevel": "months", "prevSortObjects": [], "realTimeRefreshing": false, "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60947", "desc": null, "source": "68634ceb099a11833ea5feff", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "67153-D1E3-B8", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "[0EA6A-284]", "context": {"[0EA6A-284]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "TTM Value", "datatype": "numeric"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "[0EA6A-284]", "index": 2}, "instanceid": "CC2BC-943E-C5", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_months", "index": 1}, "instanceid": "27382-55EF-37", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart", "filter": {"last": {"count": 8, "offset": 1}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Months in MonthStart", "instanceid": "7DFA1-E881-BB", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "months_in_monthstart", "title": "Months in MonthStart", "singular": "Months in MonthStart", "plural": "Months in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "prevSortObjects": [], "lastOpened": null}, {"title": "TTM Value of Contract Awards by State", "type": "map/area", "subtype": "areamap/usa", "oid": "68654950099a11833ea60948", "desc": null, "source": "68634ceb099a11833ea5ff00", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": true, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "08EFD-4107-08"}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "SUM(([5FE39-965], [2A671-043], [DC32C-036]))", "context": {"[DC32C-036]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[5FE39-965]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}, "[2A671-043]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "TTM Value:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "E64AC-52FB-A2"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}]}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "B3D1F-8121-9E", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null, "realTimeRefreshing": false, "viewState": {"activeTab": "filters"}}, {"title": "", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "oid": "68654950099a11833ea60949", "desc": null, "source": "68634ceb099a11833ea5ff01", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": []}, "style": {"activeTab": "0", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "showTitle": false, "showSeparators": true, "useSelectedBkg": false, "useUnselectedBkg": false}, "instanceid": "CEB9A-204E-D9", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "autoUpdateOnEveryChange": true}, "dashboardid": "68654950099a11833ea60935", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*       This version of <PERSON><PERSON><PERSON> uses Design Panel to configure the widget.       *\n*               Configuration via widget script is deprecated                   *\n*                                                                               *\n*********************************************************************************\n*/\n\n\nwidget.tabs = [\n  {\n    title: \"Month\", \n    displayWidgetIds : [\"68654950099a11833ea6094a\",\"68654950099a11833ea6094e\",\"68654950099a11833ea60939\", \"68654950099a11833ea6094b\", \"68654950099a11833ea6093a\", \"68654950099a11833ea6093e\", \"68654950099a11833ea6093d\", \"5defb5024faf0a20b09a2150\"], \n    hideWidgetIds : [\"68654950099a11833ea60953\", \"68654950099a11833ea60954\", \"68654950099a11833ea60955\", \"68654950099a11833ea60956\", \"68654950099a11833ea60957\", \"68654950099a11833ea60958\",\"68654950099a11833ea60959\",\"68654950099a11833ea6094f\", \"68654950099a11833ea6093c\",\"68654950099a11833ea6093f\", \"68654950099a11833ea6094d\", \"68654950099a11833ea6094c\", \"68654950099a11833ea60940\", \"68654950099a11833ea60942\", \"68654950099a11833ea60943\", \"68654950099a11833ea6093b\", \"68654950099a11833ea60947\", \"68654950099a11833ea60948\", \"68654950099a11833ea60944\", \"68654950099a11833ea60946\", \"68654950099a11833ea60945\"],\n  },\n  { \n    title: \"YTD\", \n    displayWidgetIds : [\"68654950099a11833ea6093c\",\"68654950099a11833ea6093f\", \"68654950099a11833ea6094c\", \"68654950099a11833ea60940\", \"68654950099a11833ea60942\", \"68654950099a11833ea60943\", \"68654950099a11833ea6093b\"], \n    hideWidgetIds : [\"68654950099a11833ea60953\", \"68654950099a11833ea60954\", \"68654950099a11833ea60955\", \"68654950099a11833ea60956\", \"68654950099a11833ea60957\", \"68654950099a11833ea60958\",\"68654950099a11833ea60959\",\"68654950099a11833ea6094f\",\"68654950099a11833ea6094e\",\"68654950099a11833ea6094a\", \"68654950099a11833ea6094b\", \"68654950099a11833ea6094d\",\"68654950099a11833ea60939\", \"68654950099a11833ea6093a\", \"68654950099a11833ea6093e\", \"68654950099a11833ea6093d\", \"5defb5024faf0a20b09a2150\", \"68654950099a11833ea60947\", \"68654950099a11833ea60948\", \"68654950099a11833ea60944\", \"68654950099a11833ea60946\", \"68654950099a11833ea60945\"],\n  },\n { \n    title: \"TTM\", \n    displayWidgetIds : [\"68654950099a11833ea60947\", \"68654950099a11833ea6094d\", \"68654950099a11833ea60948\", \"68654950099a11833ea60944\", \"68654950099a11833ea60946\", \"68654950099a11833ea60945\",\"68654950099a11833ea6094f\"], \n    hideWidgetIds : [\"68654950099a11833ea60953\", \"68654950099a11833ea60954\", \"68654950099a11833ea60955\", \"68654950099a11833ea60956\", \"68654950099a11833ea60957\", \"68654950099a11833ea60958\",\"68654950099a11833ea60959\",\"68654950099a11833ea6094e\", \"68654950099a11833ea6093c\",\"68654950099a11833ea6094a\", \"68654950099a11833ea6094b\", \"68654950099a11833ea6094c\", \"68654950099a11833ea6093f\", \"68654950099a11833ea60940\", \"68654950099a11833ea60942\", \"68654950099a11833ea60943\", \"68654950099a11833ea6093b\", \"68654950099a11833ea60939\", \"68654950099a11833ea6093a\", \"68654950099a11833ea6093e\", \"68654950099a11833ea6093d\", \"5defb5024faf0a20b09a2150\" ]\n  },\n{ \n    title: \"Annual\", \n    displayWidgetIds : [\"68654950099a11833ea60953\", \"68654950099a11833ea60954\", \"68654950099a11833ea60955\", \"68654950099a11833ea60956\", \"68654950099a11833ea60957\", \"68654950099a11833ea60958\",\"68654950099a11833ea60959\"], \n    hideWidgetIds : [\"68654950099a11833ea60947\", \"68654950099a11833ea6094d\", \"68654950099a11833ea60948\", \"68654950099a11833ea60944\", \"68654950099a11833ea60946\", \"68654950099a11833ea60945\",\"68654950099a11833ea6094f\",\"68654950099a11833ea6094e\", \"68654950099a11833ea6093c\",\"68654950099a11833ea6094a\", \"68654950099a11833ea6094b\", \"68654950099a11833ea6094c\", \"68654950099a11833ea6093f\", \"68654950099a11833ea60940\", \"68654950099a11833ea60942\", \"68654950099a11833ea60943\", \"68654950099a11833ea6093b\", \"68654950099a11833ea60939\", \"68654950099a11833ea6093a\", \"68654950099a11833ea6093e\", \"68654950099a11833ea6093d\", \"5defb5024faf0a20b09a2150\" ]\n \n}\n];\n\n\n", "custom": {"barcolumnchart": {"type": "WidgetsTabber", "isTypeValid": false}}, "tabs": [{"title": "Month", "displayWidgetIds": ["68654950099a11833ea6094a", "68654950099a11833ea6094e", "68654950099a11833ea60939", "68654950099a11833ea6094b", "68654950099a11833ea6093a", "68654950099a11833ea6093e", "68654950099a11833ea6093d", "5defb5024faf0a20b09a2150"], "hideWidgetIds": ["68654950099a11833ea6094f", "68654950099a11833ea6093c", "68654950099a11833ea6093f", "68654950099a11833ea6094d", "68654950099a11833ea6094c", "68654950099a11833ea60940", "68654950099a11833ea60942", "68654950099a11833ea60943", "68654950099a11833ea6093b", "68654950099a11833ea60947", "68654950099a11833ea60948", "68654950099a11833ea60944", "68654950099a11833ea60946", "68654950099a11833ea60945"]}, {"title": "YTD", "displayWidgetIds": ["68654950099a11833ea6093c", "68654950099a11833ea6093f", "68654950099a11833ea6094c", "68654950099a11833ea60940", "68654950099a11833ea60942", "68654950099a11833ea60943", "68654950099a11833ea6093b"], "hideWidgetIds": ["68654950099a11833ea6094f", "68654950099a11833ea6094e", "68654950099a11833ea6094a", "68654950099a11833ea6094b", "68654950099a11833ea6094d", "68654950099a11833ea60939", "68654950099a11833ea6093a", "68654950099a11833ea6093e", "68654950099a11833ea6093d", "5defb5024faf0a20b09a2150", "68654950099a11833ea60947", "68654950099a11833ea60948", "68654950099a11833ea60944", "68654950099a11833ea60946", "68654950099a11833ea60945"]}, {"title": "TTM", "displayWidgetIds": ["68654950099a11833ea60947", "68654950099a11833ea6094d", "68654950099a11833ea60948", "68654950099a11833ea60944", "68654950099a11833ea60946", "68654950099a11833ea60945", "68654950099a11833ea6094f"], "hideWidgetIds": ["68654950099a11833ea6094e", "68654950099a11833ea6093c", "68654950099a11833ea6094a", "68654950099a11833ea6094b", "68654950099a11833ea6094c", "68654950099a11833ea6093f", "68654950099a11833ea60940", "68654950099a11833ea60942", "68654950099a11833ea60943", "68654950099a11833ea6093b", "68654950099a11833ea60939", "68654950099a11833ea6093a", "68654950099a11833ea6093e", "68654950099a11833ea6093d", "5defb5024faf0a20b09a2150"]}], "lastOpened": null}, {"title": "Monthly Value", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea6094a", "desc": null, "source": "68634ceb099a11833ea5ff02", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year"}, "instanceid": "D45EC-1974-02"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([693F9-557], [9A44D-8D7]))", "context": {"[9A44D-8D7]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[693F9-557]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#00cee6", "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "3B00D-E61D-C4"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea6094b", "desc": null, "source": "68634ceb099a11833ea5ff03", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "2750B-A4E5-38", "panel": "rows", "format": {"width": 142.3125}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(GrowthPastYear([EA1F4-E0B]), [7B822-5B6])", "context": {"[7B822-5B6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[EA1F4-E0B]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "datatype": "numeric", "title": "Monthly % Change"}, "format": {"color": {"type": "color"}, "mask": {"decimals": 1, "percent": true}}, "field": {"id": "(GrowthPastYear([EA1F4-E0B]), [7B822-5B6])", "index": 2}, "originalJaql": {"type": "measure", "formula": "(([F6046-ED4], [5A652-9C6])-([F6046-ED4], [24ADB-1AC], [5A652-9C6]))/([F6046-ED4], [24ADB-1AC], [5A652-9C6])", "context": {"[24ADB-1AC]": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 1, "offset": 1}}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[5A652-9C6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[F6046-ED4]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Percent Change"}, "quickFunction": "DiffPastYear", "instanceid": "10E2F-F34A-CF", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}, "width": 79.734375}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "0FABD-C31F-8C", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in MonthStart", "level": "years", "filter": {"last": {"count": 5, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "instanceid": "97E46-4923-62", "panel": "scope", "title": "Years in MonthStart"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea6094c", "desc": null, "source": "68634ceb099a11833ea5ff04", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "14B5F-BD19-06", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(GrowthPastYear([F000E-BFC]), [824BF-6E5])", "context": {"[F000E-BFC]": {"table": "awards", "column": "ytd_value", "dim": "[awards.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[824BF-6E5]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}}, "title": "YTD % Change", "datatype": "numeric"}, "format": {"color": {"type": "color"}, "mask": {"decimals": 1, "percent": true}}, "field": {"id": "(GrowthPastYear([F000E-BFC]), [824BF-6E5])", "index": 2}, "originalJaql": {"type": "measure", "formula": "(([F6046-ED4], [5A652-9C6])-([F6046-ED4], [24ADB-1AC], [5A652-9C6]))/([F6046-ED4], [24ADB-1AC], [5A652-9C6])", "context": {"[24ADB-1AC]": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 1, "offset": 1}}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[5A652-9C6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[F6046-ED4]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Percent Change"}, "quickFunction": "DiffPastYear", "instanceid": "6FDFA-5AAF-99", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "C1C54-46B2-7F", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in MonthStart", "level": "years", "filter": {"last": {"count": 5, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart", "disabled": false, "instanceid": "6D0D3-D93F-1D", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea6094d", "desc": null, "source": "68634ceb099a11833ea5ff05", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "72865-C066-E1", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(<PERSON><PERSON><PERSON><PERSON><PERSON>([AA444-F80]), [CBF48-CC9])", "context": {"[CBF48-CC9]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[AA444-F80]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "datatype": "numeric", "title": "TTM % Change from Same Month Last Year"}, "format": {"color": {"type": "color"}, "mask": {"decimals": 1, "percent": true}}, "field": {"id": "(<PERSON><PERSON><PERSON><PERSON><PERSON>([AA444-F80]), [CBF48-CC9])", "index": 2}, "originalJaql": {"type": "measure", "formula": "(([F6046-ED4], [5A652-9C6])-([F6046-ED4], [24ADB-1AC], [5A652-9C6]))/([F6046-ED4], [24ADB-1AC], [5A652-9C6])", "context": {"[24ADB-1AC]": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 1, "offset": 1}}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[5A652-9C6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[F6046-ED4]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Percent Change"}, "quickFunction": "DiffPastYear", "instanceid": "A4DFA-95A8-8A", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}, "width": 81}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "7236D-1937-0D", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"level": "years", "explicit": false, "multiSelection": true, "exclude": {"members": ["2020-01-01T00:00:00"]}}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "84F6F-13BB-BC", "panel": "scope", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "Monthly Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68654950099a11833ea6094e", "desc": null, "source": "68634ceb099a11833ea5ff06", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[awards.year]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "format": {"members": {}}, "instanceid": "17623-340A-55"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([574D5-C39], [5D185-547], [D32FF-29C]))", "context": {"[5D185-547]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[574D5-C39]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[D32FF-29C]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "Value of Awards"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color_bkp": {"type": "color", "colorIndex": 0}}, "instanceid": "86985-151E-A5"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null, "realTimeRefreshing": false}, {"title": "TTM Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68654950099a11833ea6094f", "desc": null, "source": "68634ceb099a11833ea5ff07", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[awards.year]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "format": {"members": {}}, "instanceid": "59BAB-3623-FD"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum(([A7FFB-1AD], [985EC-816], [C092F-D4E]))", "context": {"[985EC-816]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[A7FFB-1AD]": {"table": "awards", "column": "ttm", "dim": "[awards.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}, "[C092F-D4E]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "Value of Awards"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color_bkp": {"type": "color", "colorIndex": 0}}, "instanceid": "DB299-FB46-8A"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null, "realTimeRefreshing": false}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68654950099a11833ea60950", "desc": null, "source": "68634ceb099a11833ea5ff08", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"currentCard": {"script": "", "title": "", "titleStyle": [{"backgroundColor": "", "backgroundImage": "", "display": "none"}], "showCarousel": true, "body": [{"type": "Container", "style": {"background-color": "#246482", "margin": "0%", "min-height": "5em"}, "items": [{"type": "Image", "style": {"width": "100%"}, "spacing": 0, "size": "large", "horizontalAlignment": "center", "url": "https://www.artba.org/wp-content/uploads/2024/03/economist.jpg"}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 12, "medium": 22, "large": 32, "extraLarge": 50}, "fontWeights": {"default": 400, "light": 200, "bold": 800}, "containerStyles": {"default": {"backgroundColor": "#ffffff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#dcdcdc"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#93c0c0"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": "100%"}, "imageSet": {"imageSize": "large", "maxImageHeight": 200}, "actions": {"color": "", "backgroundColor": "", "maxActions": 5, "spacing": "default", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 5, "medium": 10, "large": 0, "extraLarge": 0, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 0}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 234.297}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "E4A9B-88D5-45", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true, "triggersDomready": true}, "dashboardid": "68654950099a11833ea60935", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68654950099a11833ea60951", "desc": null, "source": "68634ceb099a11833ea5ff09", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<b style=\"\"><font size=\"3\">State &amp; Local Govt Contract Awards</font></b>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "7A924-CEE7-06", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68654950099a11833ea60952", "desc": null, "source": "68634ceb099a11833ea5ff0a", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<b style=\"\"><font face=\"Open Sans, serif\"><span style=\"font-size: 16px;\">May&nbsp;</span></font><font size=\"3\">2025 Data<br></font></b>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "7A924-CEE7-06", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60953", "desc": null, "source": "68634ceb099a11833ea5ff0b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode", "sort": "asc", "sortDetails": {"field": 0, "dir": "asc", "sortingLastDimension": true, "initialized": true, "isLastApplied": true}}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "C2573-106B-93", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM([E0195-B5D])", "context": {"[E0195-B5D]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Annual Value", "datatype": "numeric", "sort": null}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "SUM([E0195-B5D])", "index": 2}, "instanceid": "4E833-1542-A2", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "sort": null}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "21348-4C3F-34", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in MonthStart", "level": "years", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2020-01-01T00:00:00", "2021-01-01T00:00:00", "2022-01-01T00:00:00", "2023-01-01T00:00:00", "2024-01-01T00:00:00"]}, "collapsed": true, "sort": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart", "instanceid": "B6103-A383-91", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60954", "desc": null, "source": "68634ceb099a11833ea5ff0c", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "field": {"id": "[awards.major_mode]", "index": 0}, "instanceid": "165D9-CA6A-03", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "GrowthPastYear([F0F2C-254])", "context": {"[F0F2C-254]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Annual % Change", "datatype": "numeric"}, "format": {"color": {"type": "color"}, "mask": {"decimals": 1, "percent": true}}, "field": {"id": "GrowthPastYear([F0F2C-254])", "index": 2}, "originalJaql": {"type": "measure", "formula": "(([F6046-ED4], [5A652-9C6])-([F6046-ED4], [24ADB-1AC], [5A652-9C6]))/([F6046-ED4], [24ADB-1AC], [5A652-9C6])", "context": {"[24ADB-1AC]": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 1, "offset": 1}}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[5A652-9C6]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[F6046-ED4]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Percent Change"}, "quickFunction": "DiffPastYear", "instanceid": "140BC-4796-8C", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[awards.MonthStart (Calendar)]_years", "index": 1}, "instanceid": "16495-1331-5D", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "MonthStart", "dim": "[awards.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in MonthStart", "level": "years", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2020-01-01T00:00:00", "2021-01-01T00:00:00", "2022-01-01T00:00:00", "2023-01-01T00:00:00", "2024-01-01T00:00:00"]}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years in MonthStart", "instanceid": "EB430-5639-AB", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "years_in_monthstart", "title": "Years in MonthStart", "singular": "Years in MonthStart", "plural": "Years in MonthStart"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "2024 Annual Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68654950099a11833ea60955", "desc": null, "source": "68634ceb099a11833ea5ff0d", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[awards.year]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "major_mode", "dim": "[awards.major_mode]", "datatype": "text", "merged": true, "title": "Mode"}, "format": {"members": {"Bridge &amp; Tunnel": {"color": "#218A8C", "title": "Bridge &amp; Tunnel", "sortData": "Bridge &amp; Tunnel", "isHandPickedColor": true}, "Highway &amp; Pavement": {"color": "#06e5ff", "title": "Highway &amp; Pavement", "sortData": "Highway &amp; Pavement", "isHandPickedColor": true}, "Rail &amp; Transit": {"color": "#b2b2f7", "title": "Rail &amp; Transit", "sortData": "Rail &amp; Transit", "isHandPickedColor": true}}}, "instanceid": "023F2-1A24-4F"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([9EAB7-957])", "context": {"[9EAB7-957]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Value of Awards"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color_bkp": {"type": "color", "colorIndex": 0}}, "instanceid": "47971-5A4E-D1"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "4261E-C18C-31"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "Mode", "singular": "Mode", "plural": "Mode"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "Annual Value", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea60956", "desc": null, "source": "68634ceb099a11833ea5ff0e", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year"}, "instanceid": "16C8B-462A-F7", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([76DC1-2C4])", "context": {"[76DC1-2C4]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "A8B36-9069-55", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2025"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "63A18-EC95-85", "panel": "scope", "title": "year"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "Annual Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "oid": "68654950099a11833ea60957", "desc": null, "source": "68634ceb099a11833ea5ff0f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year"}, "instanceid": "445BD-A4D3-29", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([8A94F-902])", "context": {"[8A94F-902]": {"table": "awards", "column": "numpro", "dim": "[awards.numpro]", "datatype": "numeric", "agg": "sum", "title": "Total numpro"}}, "title": "Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color": {"colorIndex": 0, "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "1824A-C1FE-9A", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2025"]}}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "instanceid": "7ADC2-3B90-46", "panel": "scope", "title": "year"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "pivot2", "subtype": "pivot", "oid": "68654950099a11833ea60958", "desc": null, "source": "68634ceb099a11833ea5ff10", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State", "sort": null}, "field": {"id": "[Geography.state]", "index": 0}, "instanceid": "EC0E4-5040-38", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM([322DE-FB7])", "context": {"[322DE-FB7]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "sort": "desc", "datatype": "numeric", "title": "Annual Value", "sortDetails": {"field": 2, "dir": "desc", "sortingLastDimension": true, "measurePath": {"1": "2024"}, "initialized": true, "isLastApplied": true}}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "SUM([322DE-FB7])", "index": 2}, "instanceid": "697D6-8673-4E", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "sort": null}, "field": {"id": "[awards.year]", "index": 1}, "instanceid": "A14FB-F7B3-DC", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "Year", "filter": {"explicit": true, "multiSelection": true, "members": ["2020", "2021", "2022", "2023", "2024"]}, "collapsed": false, "sort": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "title": "Year", "instanceid": "79872-4F66-C8", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": true, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "rowsGrandTotal": true, "scroll": false}, "instanceid": "B3A5D-325C-EB", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "2024 Value by State", "type": "map/area", "subtype": "areamap/usa", "oid": "68654950099a11833ea60959", "desc": null, "source": "68634ceb099a11833ea5ff11", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "Geography", "column": "state", "dim": "[Geography.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "B9AE6-7599-F5"}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "sum(([2861F-7A8], [3AF29-79C]))", "context": {"[2861F-7A8]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[3AF29-79C]": {"table": "awards", "column": "year", "dim": "[awards.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}}, "title": "Value of Awards:"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}, "instanceid": "61FA4-5A91-4E"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}]}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "0D174-E2CD-B3", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "filter", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "", "type": "chart/bar", "subtype": "bar/classic", "oid": "68654950099a11833ea6095a", "desc": null, "source": "68634ceb099a11833ea5feee", "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": ["[vpip.csv.avpipLgAacsv.amajorXwAamode]", "[vpip.csv.major_mode]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": []}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([5A34A-BC1], [EE01C-819], [E8017-BCD]))", "context": {"[EE01C-819]": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}}, "[5A34A-BC1]": {"table": "awards", "column": "value", "dim": "[awards.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[E8017-BCD]": {"table": "awards", "column": "yeardum", "dim": "[awards.yeardum]", "datatype": "numeric", "title": "yeardum", "isDashboardFilter": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}}}, "title": "# of unique Major Mode"}, "instanceid": "6D829-9AD8-D3", "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color": {"colorIndex": 0, "type": "color"}}, "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "types": {"count": false, "percentage": false, "relative": false, "totals": false}, "stacked": false, "stackedPercentage": false}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "58A7B-6CD2-A1", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true}, "displayMenu": true, "custom": {"barcolumnchart": {"type": "chart/bar", "isTypeValid": true}}, "drilledDashboardDisplay": {}, "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\n/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "realTimeRefreshing": false, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": null, "max": null}}, "dashboardid": "68654950099a11833ea60935", "viewState": {"activeTab": "filters"}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68654950099a11833ea6095b", "desc": null, "source": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [], "usedFormulasMapping": {}}, "style": {"content": {"html": "", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "B9697-BAB8-95", "realTimeRefreshing": false, "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68654950099a11833ea6095c", "desc": null, "source": null, "datasource": {"address": "LocalHost", "title": "ARTBA Economics_SDK", "id": "localhost_aARTBAIAAaEconomicsXwAaSDK", "database": "aARTBAIAAaEconomicsXwAaSDK", "fullname": "localhost/ARTBA Economics_SDK", "live": false}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [], "usedFormulasMapping": {}}, "style": {"content": {"html": "", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "95E04-08F6-3A", "realTimeRefreshing": false, "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68654950099a11833ea60935", "lastOpened": null}], "hierarchies": []}