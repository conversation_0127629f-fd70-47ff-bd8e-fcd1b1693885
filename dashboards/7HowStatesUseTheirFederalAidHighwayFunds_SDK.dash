{"title": "7. How States Use Their Federal Aid Highway Funds_SDK", "desc": "", "source": "621d39289c3b3e351c940c85", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "6644E-88F6-9B", "type": "columnar", "columns": [{"width": 100, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 821, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bee", "height": 57}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "36px", "defaultWidth": 128, "widgetid": "686554bc099a11833ea60bec"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "defaultWidth": 128, "widgetid": "686554bc099a11833ea60bed", "height": "36px"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60be7", "height": "80px"}], "width": 20, "stretchable": false, "pxlWidth": 163.195, "index": 0}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf4", "height": "80px"}], "width": 20, "stretchable": false, "pxlWidth": 163.195, "index": 1}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bea", "height": "80px"}], "width": 20, "stretchable": false, "pxlWidth": 163.195, "index": 2}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "height": "80px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60be6"}], "width": 20, "stretchable": false, "pxlWidth": 163.195, "index": 3}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60beb", "height": "80px"}], "width": 20, "stretchable": false, "pxlWidth": 163.195, "index": 4}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "defaultWidth": 128, "widgetid": "686554bc099a11833ea60bf3", "height": "68px"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "528px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf6"}], "width": 50, "stretchable": false, "pxlWidth": 408, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "528px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60be5"}], "width": 50, "stretchable": false, "pxlWidth": 408, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "424px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bef"}], "width": 50, "stretchable": false, "pxlWidth": 408, "index": 0}, {"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf5", "height": "424px"}], "width": 50, "stretchable": false, "pxlWidth": 408, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf1", "height": "394px", "autoHeight": "394px"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "528px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60be8"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 271.992, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "528px", "defaultWidth": 512, "widgetid": "686554bc099a11833ea60be9"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 271.992, "index": 1}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf0", "height": "528px"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 271.992, "index": 2}]}, {"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 770, "minWidth": 128, "maxWidth": 2048, "height": 136, "defaultWidth": 512, "widgetid": "686554bc099a11833ea60bf7"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "44px", "defaultWidth": 128, "widgetid": "686554bc099a11833ea60bf2"}], "width": 100, "stretchable": false, "pxlWidth": 816, "index": 0}]}], "pxlWidth": 816, "index": 0}]}, "original": null, "previewLayout": [], "oid": "686554bc099a11833ea60be4", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "0B92C-3ECC-F3", "isCascading": false}, {"jaql": {"column": "stname", "table": "dim_location.csv", "dim": "[dim_location.csv.stname]", "datatype": "text", "title": "State", "collapsed": true, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "217EB-D420-B5", "isCascading": false, "disabled": false}, {"jaql": {"column": "date", "table": "dim_date.csv", "dim": "[dim_date.csv.date (Calendar)]", "datatype": "datetime", "level": "years", "title": "Fiscal Year Work Started:", "collapsed": true, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020", "lastBuildTime": "2022-02-28T15:45:00"}, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BAEFF-FC0C-FA", "isCascading": false, "disabled": false}, {"jaql": {"column": "system", "table": "dim_system.csv", "dim": "[dim_system.csv.system]", "datatype": "text", "title": "Federal-Aid System", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "F8A04-7AED-D4", "isCascading": false, "disabled": false}, {"jaql": {"column": "work_type", "table": "dim_worktype.csv", "dim": "[dim_worktype.csv.work_type]", "datatype": "text", "title": "Type of Work", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "3DBFF-5EAE-79", "isCascading": false, "disabled": false}, {"jaql": {"column": "c<PERSON><PERSON>", "table": "dim_location.csv", "dim": "[dim_location.csv.ctyname]", "datatype": "text", "title": "County", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BF06A-7DE7-83", "isCascading": false}, {"jaql": {"datatype": "text", "dim": "[dim_mode.csv.mode]", "title": "Mode", "column": "mode", "table": "dim_mode.csv", "collapsed": true, "merged": true, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020"}, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BF43C-9A20-80", "isCascading": false, "disabled": false}], "editing": false, "filterToDatasourceMapping": {}, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') .css('background-color','#ffffff');\n\n$('.dashboard-layout-subcell-vertical-divider') .css('background-color','#ffffff');\n\t\n$(\".dashboard-layout-cell:first-of-type\").css('max-height','60px !important');\n\n})\n\n\n", "allowChangeSubscription": false, "isPublic": null, "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "0B92C-3ECC-F3", "isCascading": false}, {"jaql": {"column": "stname", "table": "dim_location.csv", "dim": "[dim_location.csv.stname]", "datatype": "text", "title": "State", "collapsed": true, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "217EB-D420-B5", "isCascading": false, "disabled": false}, {"jaql": {"column": "date", "table": "dim_date.csv", "dim": "[dim_date.csv.date (Calendar)]", "datatype": "datetime", "level": "years", "title": "Fiscal Year Work Started:", "collapsed": true, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020", "lastBuildTime": "2022-02-28T15:45:00"}, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BAEFF-FC0C-FA", "isCascading": false, "disabled": false}, {"jaql": {"column": "system", "table": "dim_system.csv", "dim": "[dim_system.csv.system]", "datatype": "text", "title": "Federal-Aid System", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "F8A04-7AED-D4", "isCascading": false, "disabled": false}, {"jaql": {"column": "work_type", "table": "dim_worktype.csv", "dim": "[dim_worktype.csv.work_type]", "datatype": "text", "title": "Type of Work", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "3DBFF-5EAE-79", "isCascading": false, "disabled": false}, {"jaql": {"column": "c<PERSON><PERSON>", "table": "dim_location.csv", "dim": "[dim_location.csv.ctyname]", "datatype": "text", "title": "County", "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BF06A-7DE7-83", "isCascading": false}, {"jaql": {"datatype": "text", "dim": "[dim_mode.csv.mode]", "title": "Mode", "column": "mode", "table": "dim_mode.csv", "collapsed": true, "merged": true, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020"}, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "BF43C-9A20-80", "isCascading": false, "disabled": false}], "parentFolder": "68654e16099a11833ea60a10", "settings": {"autoUpdateOnFiltersChange": true}, "lastOpened": null, "defaultFilterRelations": null, "widgets": [{"title": "Type of Work Performed", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554bc099a11833ea60be5", "desc": null, "source": "621d39289c3b3e351c940c87", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "dim_worktype.csv", "column": "work_type", "dim": "[dim_worktype.csv.work_type]", "datatype": "text", "merged": true, "title": "Type of Work:"}, "format": {"members": {"Added Capacity": {"color": "#124c87", "title": "Added Capacity", "sortData": "Added Capacity", "inResultset": true}, "Bike/Pedestrian Facilities": {"color": "#9b9bd7", "title": "Bike/Pedestrian Facilities", "sortData": "Bike/Pedestrian Facilities", "inResultset": true}, "Debt Service": {"color": "#6EDA55", "title": "Debt Service", "sortData": "Debt Service", "inResultset": true}, "Inspection": {"color": "#fc7570", "title": "Inspection", "sortData": "Inspection", "inResultset": true}, "New Construction": {"color": "#fbb755", "title": "New Construction", "sortData": "New Construction", "inResultset": true}, "Other": {"color": "#218A8C", "title": "Other", "sortData": "Other", "inResultset": true}, "Planning, Design & Construction Engineering": {"color": "#06e5ff", "title": "Planning, Design & Construction Engineering", "sortData": "Planning, Design & Construction Engineering", "inResultset": true}, "Planning, Environmental, Research & Administration": {"color": "#b2b2f7", "title": "Planning, Environmental, Research & Administration", "sortData": "Planning, Environmental, Research & Administration", "inResultset": true}, "Reconstruction & Repair": {"color": "#7efb62", "title": "Reconstruction & Repair", "sortData": "Reconstruction & Repair", "inResultset": true}, "Right of Way Purchases": {"color": "#ff8a86", "title": "Right of Way Purchases", "sortData": "Right of Way Purchases", "inResultset": true}, "Safety (Non Construction)": {"color": "#ffc26a", "title": "Safety (Non Construction)", "sortData": "Safety (Non Construction)", "inResultset": true}, "Utilities": {"color": "#269fa1", "title": "Utilities", "sortData": "Utilities", "inResultset": true}}}}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "totalcost", "dim": "[fact_dataprojects.csv.totalcost]", "datatype": "numeric", "agg": "sum", "title": "Total Project Cost"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color_bkp": {"color": "#00cee6", "type": "color"}}}]}, {"name": "filters", "items": []}]}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_work:", "title": "Type of Work:", "singular": "Type of Work:", "plural": "Type of Work:"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}}, "instanceid": "5E8B0-D297-6E", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n\t$(element).find(\".widget-toolbar-btn .btn__text\").style('color', 'white', 'important');\n});", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554bc099a11833ea60be6", "desc": null, "source": "621d39289c3b3e351c940c88", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_worktype.csv", "column": "work_type", "dim": "[dim_worktype.csv.work_type]", "datatype": "text", "merged": true, "title": "Type of Work"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "work_type", "title": "work_type", "singular": "work_type", "plural": "work_type"}]}, "title": {"titleMenuEnabled": false, "fontColorEnabled": true, "titleAlign": "center", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontSizeEnabled": true, "titleFontSize": "18", "fontTitleBackgroundEnabled": true, "titleBackground": "#117899", "titleFontColor": "white"}}, "instanceid": "D1D56-009D-A4", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554bc099a11833ea60be7", "desc": null, "source": "621d39289c3b3e351c940c89", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_location.csv", "column": "stname", "dim": "[dim_location.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "data_projects", "column": "c<PERSON><PERSON>", "dim": "[data_projects.ctyname]", "datatype": "text", "merged": true, "title": "c<PERSON><PERSON>", "filter": {"explicit": false, "multiSelection": true, "all": true}, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020"}, "collapsed": true}}]}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "stname", "title": "stname", "singular": "stname", "plural": "stname"}, {"id": "c<PERSON><PERSON>", "title": "c<PERSON><PERSON>", "singular": "c<PERSON><PERSON>", "plural": "c<PERSON><PERSON>"}]}, "title": {"titleMenuEnabled": false, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#117899", "titleAlign": "center"}}, "instanceid": "D1D56-009D-A4", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}, {"title": "Value by Mode", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554bc099a11833ea60be8", "desc": null, "source": "621d39289c3b3e351c940c8a", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "dim_mode.csv", "column": "mode", "dim": "[dim_mode.csv.mode]", "datatype": "text", "merged": true, "title": "Mode"}, "format": {"members": {"Bridge": {"color": "#00cee6", "title": "Bridge", "sortData": "Bridge", "inResultset": true}, "Highway": {"color": "#9b9bd7", "title": "Highway", "sortData": "Highway", "inResultset": true}, "Highway & Bridge": {"color": "#6EDA55", "title": "Highway & Bridge", "sortData": "Highway & Bridge", "inResultset": true}, "Other": {"color": "#fc7570", "title": "Other", "sortData": "Other", "inResultset": true}, "Transit": {"color": "#fbb755", "title": "Transit", "sortData": "Transit", "inResultset": true}, "Tunnel": {"color": "#218A8C", "title": "Tunnel", "sortData": "Tunnel", "inResultset": true}}}}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "totalcost", "dim": "[fact_dataprojects.csv.totalcost]", "datatype": "numeric", "agg": "sum", "title": "Total Project Cost"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}}]}, {"name": "filters", "items": []}]}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode", "title": "mode", "singular": "mode", "plural": "mode"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "DCA1D-5A9A-C0", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "lastOpened": null}, {"title": "Value by Federal-Aid System", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554bc099a11833ea60be9", "desc": null, "source": "621d39289c3b3e351c940c8b", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "dim_system.csv", "column": "system", "dim": "[dim_system.csv.system]", "datatype": "text", "merged": true, "title": "Federal-Aid System"}, "format": {"members": {"N\\A": {"color": "#00cee6", "title": "N\\A", "sortData": "N\\A"}, "Interstate": {"color": "#9b9bd7", "title": "Interstate", "sortData": "Interstate", "inResultset": true}, "National Highway System (Not Interstate)": {"color": "#6EDA55", "title": "National Highway System (Not Interstate)", "sortData": "National Highway System (Not Interstate)", "inResultset": true}, "Not on Federal Aid System": {"color": "#fc7570", "title": "Not on Federal Aid System", "sortData": "Not on Federal Aid System", "inResultset": true}, "Other Federal Aid Highway": {"color": "#fbb755", "title": "Other Federal Aid Highway", "sortData": "Other Federal Aid Highway", "inResultset": true}}}}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "totalcost", "dim": "[fact_dataprojects.csv.totalcost]", "datatype": "numeric", "agg": "sum", "title": "Total Project Cost"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}}]}, {"name": "filters", "items": []}]}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "federal-aid_system", "title": "Federal-Aid System", "singular": "Federal-Aid System", "plural": "Federal-Aid System"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}}, "instanceid": "B7F22-E310-2D", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554bc099a11833ea60bea", "desc": null, "source": "621d39289c3b3e351c940c8c", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_date.csv", "column": "date", "dim": "[dim_date.csv.date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Fiscal Year Work Started:", "sort": "desc"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": false, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years", "title": "years", "singular": "years", "plural": "years"}]}}, "instanceid": "CB139-0C1C-1B", "displayMenu": false, "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "noSelectionText": "", "autoUpdateOnEveryChange": true}, "dashboardid": "686554bc099a11833ea60be4", "fullResults": [["2018-01-01T00:00:00"], ["2017-01-01T00:00:00"], ["2016-01-01T00:00:00"], ["2015-01-01T00:00:00"], ["2014-01-01T00:00:00"], ["2013-01-01T00:00:00"], ["2012-01-01T00:00:00"], ["2011-01-01T00:00:00"], ["2010-01-01T00:00:00"], ["2009-01-01T00:00:00"], ["2008-01-01T00:00:00"], ["2007-01-01T00:00:00"], ["2006-01-01T00:00:00"]], "listOpen": false, "zIndex": 100, "AllMembersSelected": true, "allFilteredMembersSelected": true, "multiSelect": true, "membersSelected": ["2018-01-01T00:00:00", "2017-01-01T00:00:00", "2016-01-01T00:00:00", "2015-01-01T00:00:00", "2014-01-01T00:00:00", "2013-01-01T00:00:00", "2012-01-01T00:00:00", "2011-01-01T00:00:00", "2010-01-01T00:00:00", "2009-01-01T00:00:00", "2008-01-01T00:00:00", "2007-01-01T00:00:00", "2006-01-01T00:00:00"], "excludeMembers": [], "AllMemberSelected": false, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554bc099a11833ea60beb", "desc": null, "source": "621d39289c3b3e351c940c8d", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_system.csv", "column": "system", "dim": "[dim_system.csv.system]", "datatype": "text", "merged": true, "title": "Federal-Aid System"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "system", "title": "system", "singular": "system", "plural": "system"}]}}, "instanceid": "CB139-0C1C-1B", "displayMenu": false, "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "fullResults": [["2018-01-01T00:00:00"], ["2017-01-01T00:00:00"], ["2016-01-01T00:00:00"], ["2015-01-01T00:00:00"], ["2014-01-01T00:00:00"], ["2013-01-01T00:00:00"], ["2012-01-01T00:00:00"], ["2011-01-01T00:00:00"], ["2010-01-01T00:00:00"], ["2009-01-01T00:00:00"], ["2008-01-01T00:00:00"], ["2007-01-01T00:00:00"], ["2006-01-01T00:00:00"]], "listOpen": false, "zIndex": 100, "AllMembersSelected": true, "allFilteredMembersSelected": true, "multiSelect": true, "membersSelected": ["2018-01-01T00:00:00", "2017-01-01T00:00:00", "2016-01-01T00:00:00", "2015-01-01T00:00:00", "2014-01-01T00:00:00", "2013-01-01T00:00:00", "2012-01-01T00:00:00", "2011-01-01T00:00:00", "2010-01-01T00:00:00", "2009-01-01T00:00:00", "2008-01-01T00:00:00", "2007-01-01T00:00:00", "2006-01-01T00:00:00"], "excludeMembers": [], "AllMemberSelected": false, "options": {"dashboardFiltersMode": "select", "selector": true, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554bc099a11833ea60bec", "desc": null, "source": "621d39289c3b3e351c940c8e", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<b style=\"\"><font size=\"6\">How States Use Their Federal Highway Funds</font></b>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "A1978-3E8B-21", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554bc099a11833ea60be4", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().css('max-height','185px');\n\t$(element).parent().find(\"#editor-3\").css('max-height','185px').css('vertical-align','top');\n\t$(element).parent().find(\"font\").style('font-size', '30px', 'important');\n})", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554bc099a11833ea60bed", "desc": null, "source": "621d39289c3b3e351c940c8f", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"5\">The federal government has provided $1.3 trillion in funding for over 1 million projects since 1950.</font><br>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "A1978-3E8B-21", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554bc099a11833ea60be4", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().css('max-height','195px');\n\t$(element).parent().find(\"#editor-12\").css('max-height','195px').css('vertical-align','top');\n\t$(element).parent().find(\"font\").style('font-size', '28px', 'important');\n});", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "686554bc099a11833ea60bee", "desc": null, "source": "621d39289c3b3e351c940c90", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "Container", "spacing": "none", "items": [{"type": "Image", "url": "https://transportationinvestment.org/wp-content/uploads/2019/11/tiac-dashboard.png", "size": "auto", "spacing": "none", "style": {"width": "100%", "height": "100%", "margin": 0, "padding": 0}}]}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 14, "medium": 20, "large": 30, "extraLarge": 50}, "fontWeights": {"default": 500, "light": 100, "bold": 700}, "containerStyles": {"default": {"backgroundColor": "#ffffff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#3A4356"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}, "pink": {"normal": "#F3879F"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 5, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#EAE9E9"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 57}, "currentCardName": "Vertical Card", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "7C7C0-02AA-9D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "686554bc099a11833ea60be4", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().height('80');\n})", "lastOpened": null}, {"title": "Federal Aid Highway Funds by State", "type": "map/area", "subtype": "areamap/usa", "oid": "686554bc099a11833ea60bef", "desc": null, "source": "621d39289c3b3e351c940c91", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "dim_location.csv", "column": "stname", "dim": "[dim_location.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "color", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "federalfunds", "dim": "[fact_dataprojects.csv.federalfunds]", "datatype": "numeric", "agg": "sum", "title": "Federal Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"rangeMode": "auto", "type": "range", "steps": 9}}}]}, {"name": "filters", "items": []}]}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}}, "instanceid": "CC2B7-1D8C-D3", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "lastOpened": null}, {"title": "Value by Area", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554bc099a11833ea60bf0", "desc": null, "source": "621d39289c3b3e351c940c92", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "area", "dim": "[fact_dataprojects.csv.area]", "datatype": "text", "merged": true, "title": "Area"}, "format": {"members": {"Rural": {"color": "#00cee6", "title": "Rural", "sortData": "Rural", "inResultset": true}, "Statewide": {"color": "#9b9bd7", "title": "Statewide", "sortData": "Statewide", "inResultset": true}, "Urban": {"color": "#6EDA55", "title": "Urban", "sortData": "Urban", "inResultset": true}, "Urban & Rural": {"color": "#fc7570", "title": "Urban & Rural", "sortData": "Urban & Rural", "inResultset": true}}}}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "totalcost", "dim": "[fact_dataprojects.csv.totalcost]", "datatype": "numeric", "agg": "sum", "title": "Total Project Cost"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}}]}, {"name": "filters", "items": []}]}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "area", "title": "area", "singular": "area", "plural": "area"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}}, "instanceid": "B7F22-E310-2D", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "Source of Funding", "type": "pivot", "subtype": "pivot", "oid": "686554bc099a11833ea60bf1", "desc": null, "source": "621d39289c3b3e351c940c94", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "dim_worktype.csv", "column": "work_type", "dim": "[dim_worktype.csv.work_type]", "datatype": "text", "merged": true, "title": "Type of Work"}, "field": {"id": "[dim_worktype.csv.work_type]", "index": 0}}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "federalfunds", "dim": "[fact_dataprojects.csv.federalfunds]", "datatype": "numeric", "agg": "sum", "title": "Federal Funds"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[fact_dataprojects.csv.federalfunds]_sum", "index": 1}}, {"jaql": {"table": "fact_dataprojects.csv", "column": "statefunds", "dim": "[fact_dataprojects.csv.statefunds]", "datatype": "numeric", "agg": "sum", "title": "State Funds"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[fact_dataprojects.csv.statefunds]_sum", "index": 2}}, {"jaql": {"table": "fact_dataprojects.csv", "column": "localfunds", "dim": "[fact_dataprojects.csv.localfunds]", "datatype": "numeric", "agg": "sum", "title": "Local Funds"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[fact_dataprojects.csv.localfunds]_sum", "index": 3}}, {"jaql": {"table": "fact_dataprojects.csv", "column": "privatefunds", "dim": "[fact_dataprojects.csv.privatefunds]", "datatype": "numeric", "agg": "sum", "title": "Private & Other Funds"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[fact_dataprojects.csv.privatefunds]_sum", "index": 4}}, {"jaql": {"table": "fact_dataprojects.csv", "column": "acfunds", "dim": "[fact_dataprojects.csv.acfunds]", "datatype": "numeric", "agg": "sum", "title": "Advance Construction Funds"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[fact_dataprojects.csv.acfunds]_sum", "index": 5}}, {"jaql": {"type": "measure", "formula": "sum([39158-A9C]+[6A091-AF1]+[2C5FA-0F5]+[62B54-83B]+[BFF4C-03F])", "context": {"[2C5FA-0F5]": {"table": "fact_dataprojects.csv", "column": "privatefunds", "dim": "[fact_dataprojects.csv.privatefunds]", "datatype": "numeric", "agg": "sum", "title": "Total privatefunds"}, "[39158-A9C]": {"table": "fact_dataprojects.csv", "column": "federalfunds", "dim": "[fact_dataprojects.csv.federalfunds]", "datatype": "numeric", "agg": "sum", "title": "Total federalfunds"}, "[62B54-83B]": {"table": "fact_dataprojects.csv", "column": "statefunds", "dim": "[fact_dataprojects.csv.statefunds]", "datatype": "numeric", "agg": "sum", "title": "Total statefunds"}, "[6A091-AF1]": {"table": "fact_dataprojects.csv", "column": "localfunds", "dim": "[fact_dataprojects.csv.localfunds]", "datatype": "numeric", "agg": "sum", "title": "Total localfunds"}, "[BFF4C-03F]": {"table": "fact_dataprojects.csv", "column": "acfunds", "dim": "[fact_dataprojects.csv.acfunds]", "datatype": "numeric", "agg": "sum", "title": "Total acfunds"}}, "title": "Total Costs"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "sum([39158-A9C]+[6A091-AF1]+[2C5FA-0F5]+[62B54-83B]+[BFF4C-03F])", "index": 6}}]}, {"name": "columns", "items": []}, {"name": "filters", "items": []}]}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_work", "title": "Type of Work", "singular": "Type of Work", "plural": "Type of Work"}]}, "title": {"titleMenuEnabled": true, "titleAlign": "center", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "White", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": ""}, "rowsGrandTotal": true}, "instanceid": "B2005-898A-AB", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554bc099a11833ea60bf2", "desc": null, "source": "621d39289c3b3e351c940c95", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"3\" style=\"font-weight: bold;\">About the Data:&nbsp; </font><font style=\"font-size: 12.5px;\">ARTBA conducted a data analysis of federal-aid projects.&nbsp; Data was provided by the U.S. Federal Highway Administration (FHWA) and is from the Federal Management Information System (FMIS).&nbsp; For more details visit&nbsp;</font><a href=\"https://www.artba.org/economics/federal-investment/about\">https://www.artba.org/economics/federal-investment/about</a>.&nbsp;&nbsp;", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "left"}}, "instanceid": "D5D1E-979F-EC", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554bc099a11833ea60bf3", "desc": null, "source": "621d39289c3b3e351c940c96", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<div class=\"text-block font-size-small font-weight-bold color-default spacing-none horizontal-left wrap\" style=\"padding: 0px; overflow: hidden; text-overflow: ellipsis; color: rgb(0, 0, 0); margin-top: 0px; text-align: left;\"><div class=\"text-block font-size-small font-weight-bold color-default spacing-none horizontal-left wrap\" style=\"font-weight: 1000; padding: 0px; overflow: hidden; text-overflow: ellipsis; margin-top: 0px;\"><br></div><div class=\"text-block font-size-small font-weight-bold color-default spacing-none horizontal-left wrap\" style=\"font-weight: 1000; padding: 0px; overflow: hidden; text-overflow: ellipsis; margin-top: 0px;\">Federal Aid Highway Funds -&nbsp;<font style=\"font-weight: normal;\" size=\"2\">The federal aid highway program provides over 50 percent of funding for state highway and bridge construction outlays, including construction, planning and design work and right of way purchases.&nbsp;&nbsp;</font><font style=\"font-weight: normal;\" size=\"2\">Federal transportation investment has helped lay the foundation of our modern economy.&nbsp; &nbsp;Over half of this investment has been on the highways and bridges that are part of the National Highway System, a network that includes the Interstate Highway System and other major roads that connect our airports and intermodal facilities.&nbsp; Individual project details are at the bottom of the page.&nbsp; For more details, visit \"About the Data.\"</font><span style=\"font-weight: normal; color: rgb(255, 255, 255);\">e the table below.&nbsp;</span></div></div>", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "A1978-3E8B-21", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', ()=>{\n    $(element).parent().css('max-height','195px');\n\t$(element).parent().find(\"#editor-12\").css('max-height','195px').css('vertical-align','top');\n\t$(element).parent().find(\"font\").style('font-size', '28px', 'important');\n});", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554bc099a11833ea60bf4", "desc": null, "source": "621d39289c3b3e351c940c97", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_location.csv", "column": "c<PERSON><PERSON>", "dim": "[dim_location.csv.ctyname]", "datatype": "text", "merged": true, "title": "County"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "data_projects", "column": "c<PERSON><PERSON>", "dim": "[data_projects.ctyname]", "datatype": "text", "merged": true, "title": "c<PERSON><PERSON>", "filter": {"explicit": false, "multiSelection": true, "all": true}, "datasource": {"title": "FHWA_Data_2020", "fullname": "LocalHost/FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "address": "LocalHost", "database": "aFHWAXwAaDataXwAa2020"}, "collapsed": true}}]}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "ctyname1", "title": "ctyname1", "singular": "ctyname1", "plural": "ctyname1"}, {"id": "c<PERSON><PERSON>", "title": "c<PERSON><PERSON>", "singular": "c<PERSON><PERSON>", "plural": "c<PERSON><PERSON>"}]}, "title": {"titleMenuEnabled": false, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#117899", "titleAlign": "center"}}, "instanceid": "D1D56-009D-A4", "displayMenu": false, "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "Federal Funds as a % of State Highway & Bridge Program Capital Outlays", "type": "map/area", "subtype": "areamap/usa", "oid": "686554bc099a11833ea60bf5", "desc": null, "source": "621d39289c3b3e351c940c98", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "dim_location.csv", "column": "stname", "dim": "[dim_location.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "Avg([0C8EE-BAD])", "context": {"[C49CE-686]": {"table": "dim_pcnt.csv", "column": "fed_cap", "dim": "[dim_pcnt.csv.fed_cap]", "datatype": "numeric", "agg": "sum", "title": "Total fed_cap"}, "[0C8EE-BAD]": {"table": "dim_pcnt.csv", "column": "fed_cap", "dim": "[dim_pcnt.csv.fed_cap]", "datatype": "numeric", "title": "fed_cap"}}, "title": "Fed Cap"}, "format": {"mask": {"decimals": 0, "percent": true}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}}]}, {"name": "filters", "items": []}]}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}}, "instanceid": "CC2B7-1D8C-D3", "displayMenu": false, "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "lastOpened": null}, {"title": "Value of Project by Fiscal Year Major Work Began", "type": "chart/column", "subtype": "column/stackedcolumn", "oid": "686554bc099a11833ea60bf6", "desc": null, "source": "621d39289c3b3e351c940c99", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "dim_date.csv", "column": "date", "dim": "[dim_date.csv.date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Year"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "5CBE8-EE8C-6A", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "fact_dataprojects.csv", "column": "federalfunds", "dim": "[fact_dataprojects.csv.federalfunds]", "datatype": "numeric", "agg": "sum", "title": "Federal Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "instanceid": "0EFEA-E301-D5", "panel": "measures"}, {"jaql": {"table": "fact_dataprojects.csv", "column": "acfunds", "dim": "[fact_dataprojects.csv.acfunds]", "datatype": "numeric", "agg": "sum", "title": "Advanced Construction Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 1, "type": "color"}}, "instanceid": "00E69-5BC0-B9", "panel": "measures"}, {"jaql": {"table": "fact_dataprojects.csv", "column": "statefunds", "dim": "[fact_dataprojects.csv.statefunds]", "datatype": "numeric", "agg": "sum", "title": "State Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#fc7570", "type": "color", "isHandPickedColor": true}}, "instanceid": "47FAE-D080-0B", "panel": "measures"}, {"jaql": {"table": "fact_dataprojects.csv", "column": "localfunds", "dim": "[fact_dataprojects.csv.localfunds]", "datatype": "numeric", "agg": "sum", "title": "Local Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#fbb755", "type": "color", "isHandPickedColor": true}}, "instanceid": "152B3-E2ED-A3", "panel": "measures"}, {"jaql": {"table": "fact_dataprojects.csv", "column": "privatefunds", "dim": "[fact_dataprojects.csv.privatefunds]", "datatype": "numeric", "agg": "sum", "title": "Private & Other Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#6EDA55", "type": "color", "isHandPickedColor": true}}, "instanceid": "45BC8-0F81-AC", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": true, "stacked": true, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years", "title": "years", "singular": "years", "plural": "years"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "14", "fontColorEnabled": true, "titleFontColor": "white", "fontFamilyEnabled": true, "titleAlign": "center", "fontWeightEnabled": true, "titleFontWeight": "bold"}}, "instanceid": "FBF85-44EA-8B", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 0, "max": 71}, "selectorLocked": false}, "dashboardid": "686554bc099a11833ea60be4", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true}}, "prevSortObjects": [], "lastOpened": null, "realTimeRefreshing": false}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "686554bc099a11833ea60bf7", "desc": null, "source": "621d39289c3b3e351c940c9a", "datasource": {"address": "LocalHost", "title": "FHWA_Data_2020", "id": "aLOCALHOST_aFHWAXwAaDATAXwAa2020", "database": "aFHWAXwAaDataXwAa2020", "fullname": "LocalHost/FHWA_Data_2020"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": {"background": "white"}, "script": "", "title": "", "titleStyle": [{"backgroundColor": "#005880", "backgroundImage": "", "display": "none"}], "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "TextBlock", "text": "️List of Federal Aid Projects", "style": {"text-align": "center", "font-weight": "bold", "font-size": "24px", "margin": "10px", "color": "white"}}]}], "actions": [{"type": "JTD", "title": "Click Here to View the List", "data": {"dashboardId": "5e97534fe153db3beca9c959", "panelsToInclude": [], "args": {"drilledDashboardDisplayType": "<PERSON> for This", "displayDashboardsPane": false, "displayFilterPane": false}}}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 500, "light": 100, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#005880", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "white", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 136}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "D0E53-7236-3A", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": "<PERSON> for This", "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "custom": true}, "drilledDashboardDisplay": {"self": "_blank"}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "5e97534fe153db3beca9c959", "autoUpdateOnEveryChange": true}, "dashboardid": "686554bc099a11833ea60be4", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}], "hierarchies": []}