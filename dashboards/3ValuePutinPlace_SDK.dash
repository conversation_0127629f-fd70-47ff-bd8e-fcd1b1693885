{"title": "3. Value Put in Place_SDK", "desc": "", "source": "5defbf594faf0a20b09a2189", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "0951E-C8BC-C9", "type": "columnar", "columns": [{"width": 30.576789437109106, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 888, "minWidth": 128, "maxWidth": 2048, "height": 234.297, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab8"}], "width": 100, "stretchable": false, "pxlWidth": 777.859, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "44px", "defaultWidth": 128, "widgetid": "68655384099a11833ea60aa8"}], "width": 50, "stretchable": false, "pxlWidth": 388.922, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "44px", "defaultWidth": 128, "widgetid": "68655384099a11833ea60aa9"}], "width": 50, "stretchable": false, "pxlWidth": 388.922, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "716px", "defaultWidth": 128, "widgetid": "68655384099a11833ea60aaa"}], "width": 100, "stretchable": false, "pxlWidth": 777.859, "index": 0}]}], "pxlWidth": 777.859, "index": 0}, {"width": 45.72619874913135, "pxlWidth": 1163.27, "index": 1, "cells": [{"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "height": "160px", "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aa5"}], "width": 50, "stretchable": false, "pxlWidth": 581.625, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aa7", "height": "160px"}], "width": 50, "stretchable": false, "pxlWidth": 581.625, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 32, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "40px", "defaultWidth": 512, "widgetid": "68655384099a11833ea60aaf"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab4", "height": "404px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab5", "height": "452px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab0", "height": "400px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab9", "height": "396px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "396px", "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab3"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aba", "height": "472px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aae", "height": "472px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab1", "height": "452px"}], "width": 100, "stretchable": false, "pxlWidth": 1163.27, "index": 0}]}]}, {"width": 23.69701181375955, "pxlWidth": 602.844, "index": 2, "cells": [{"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aa6", "height": "160px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60abc", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "468px", "defaultWidth": 512, "widgetid": "68655384099a11833ea60aab"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60abb", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "472px", "defaultWidth": 512, "widgetid": "68655384099a11833ea60aad"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab2", "height": "476px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab7", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60aac", "height": "472px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655384099a11833ea60ab6", "height": "468px"}], "width": 100, "stretchable": false, "pxlWidth": 602.844, "index": 0}]}]}]}, "original": null, "previewLayout": [], "oid": "68655384099a11833ea60aa4", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "45EC6-28AE-D6", "isCascading": false}], "editing": false, "filterToDatasourceMapping": {}, "parentFolder": "68654e16099a11833ea60a10", "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "isDashboardFilter": true, "locale": "en-us", "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "45EC6-28AE-D6", "isCascading": false, "disabled": false}], "allowChangeSubscription": false, "isPublic": null, "lastOpened": null, "settings": {"autoUpdateOnFiltersChange": true}, "defaultFilterRelations": null, "filterRelations": [], "widgets": [{"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655384099a11833ea60aa5", "desc": null, "source": "65982bc5de67d0004306a789", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]", "[vpip.csv.year]", "[Mode.major_mode]", "[executive_summary.csv.mode]", "[executive_summary.csv.MonthStart (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "(([9BBF4-205], [FDF13-9CC], [9E6B6-D1C]))", "context": {"[9E6B6-D1C]": {"table": "vpip.csv", "column": "MonthStart", "dim": "[vpip.csv.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2025-01-01T00:00:00"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "[FDF13-9CC]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[9BBF4-205]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Monthly Value"}, "format": {"color": {"colorIndex": 0, "type": "color"}, "mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "614FA-2A71-C8"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([0107B-418], [CCBD8-893], [F77F9-A9F])-([0107B-418], [CCBD8-893], [7BADA-8C0]))/([0107B-418], [CCBD8-893], [7BADA-8C0])", "context": {"[CCBD8-893]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[0107B-418]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[7BADA-8C0]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "[F77F9-A9F]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}}, "title": "% Change from Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}, "instanceid": "0C94D-AAE0-56"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d80df67855c880d2c24f5bc", "caption": "_drill_vpip_month"}}, "dashboardid": "68655384099a11833ea60aa4", "drilledDashboardDisplay": {}, "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\n/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655384099a11833ea60aa6", "desc": null, "source": "65982bc5de67d0004306a78a", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]", "[Mode.major_mode]", "[vpip.csv.year]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "Sum([5619C-DAC])", "context": {"[5619C-DAC]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "Trailing 12-Month (TTM) Total"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "instanceid": "4897D-96D1-9F"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([9E9CD-B35], [AEEA5-5D4], [D9A8E-BE0])-([9E9CD-B35], [AEEA5-5D4], [11224-3C5]))/([9E9CD-B35], [AEEA5-5D4], [11224-3C5])", "context": {"[AEEA5-5D4]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[9E9CD-B35]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}, "[11224-3C5]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "[D9A8E-BE0]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}}, "title": "% Change from Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}, "instanceid": "9CABD-6EE6-BE"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "instanceid": "45B8B-2B0A-D6"}, {"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "instanceid": "5EEEE-C8B9-D4"}]}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}, {"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}]}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d93687f32789805e424eed6", "caption": "_drill_VPIP_TTM", "folder": null}}, "dashboardid": "68655384099a11833ea60aa4", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "drilledDashboardDisplay": {}, "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655384099a11833ea60aa7", "desc": null, "source": "65982bc5de67d0004306a78b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]", "[Mode.major_mode]", "[vpip.csv.year]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "(([84B75-326], [4F211-BDF], [2B77F-08B]))", "context": {"[84B75-326]": {"table": "vpip.csv", "column": "ytd_value", "dim": "[vpip.csv.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[4F211-BDF]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[2B77F-08B]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}}, "title": "Year to Date (YTD) Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "instanceid": "34007-9B88-F6"}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([FA76F-B0E], [7D0B2-011], [CD409-2C0])-([FA76F-B0E], [7D0B2-011], [B3368-05C]))/([FA76F-B0E], [7D0B2-011], [B3368-05C])", "context": {"[FA76F-B0E]": {"table": "vpip.csv", "column": "ytd_value", "dim": "[vpip.csv.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[7D0B2-011]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[B3368-05C]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "[CD409-2C0]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}}, "title": "% Change from Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}, "instanceid": "A2448-3A20-7A"}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d9367a55355a1223c5cf74d", "caption": "_drill_VPIP_YTD", "folder": null}}, "dashboardid": "68655384099a11833ea60aa4", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*      Jump To Dashboard is now configured in the widget Edit mode.             *\n*      Configuration via widget script is not recommended and is disabled       *\n*      by default.                                                              *\n*                                                                               *\n*********************************************************************************\n*/\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})\n\nprism.jumpToDashboard(widget, {drilledDashboardDisplayType:2, \ndisplayFilterPane:false, displayDashboardsPane:false, displayToolbarRow:false, displayHeaderRow:false, hideDrilledDashboard: false });", "drilledDashboardDisplay": {}, "_drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": false, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "hideDrilledDashboard": false, "custom": true, "dirty": true}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655384099a11833ea60aa8", "desc": null, "source": "65982bc5de67d0004306a78c", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<div style=\"text-align: center;\"><b style=\"\"><font size=\"3\">Value of Construction Put in Place</font></b><br></div>", "vAlign": "valign-middle", "bgColor": "#ffffff", "textAlign": "center"}}, "instanceid": "DE60D-D481-FC", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655384099a11833ea60aa4", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655384099a11833ea60aa9", "desc": null, "source": "65982bc5de67d0004306a78d", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<p class=\"MsoNormal\"><b style=\"\"><font size=\"3\">April 2025 Data</font></b></p>", "vAlign": "valign-middle", "bgColor": "#ffffff", "textAlign": "center"}}, "instanceid": "0859A-5D6C-5D", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655384099a11833ea60aa4", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655384099a11833ea60aaa", "desc": null, "source": "65982bc5de67d0004306a78e", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<div style=\"text-align: left;\"><p style=\"margin-bottom: 0.0001pt; text-align: center; line-height: normal;\" align=\"center\" class=\"MsoNormal\"><br></p><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0in; text-align: center; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><b><span style=\"font-size: 12pt; font-family: &quot;Open Sans&quot;, serif; border: 1pt none windowtext; padding: 0in;\">April Transportation Construction Activity&nbsp;</span></b><b style=\"font-size: 13px;\"><span style=\"font-size: 12pt; font-family: &quot;Open Sans&quot;, serif; border: 1pt none windowtext; padding: 0in;\">$16.8B</span></b></p><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0in; text-align: center; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><b style=\"font-size: 13px;\"><span style=\"font-size: 12pt; font-family: &quot;Open Sans&quot;, serif; border: 1pt none windowtext; padding: 0in;\">Monthly Gains in Airport, Bridge, and Waterway Construction</span></b></p><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0in; text-align: center; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><b><span style=\"font-size: 12pt; font-family: &quot;Open Sans&quot;, serif; border: 1pt none windowtext; padding: 0in;\"><br></span></b></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">Contractors performed $16.8 billion\nin work in </span><span style=\"font-size: 13.3333px;\">April</span></font><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">, up from&nbsp; $16.3 billion performed in&nbsp;</span></font><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">April&nbsp;</span><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">2024</span></font><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">, according\nto the latest data from the U.S. Census Bureau.&nbsp;</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">&nbsp;&nbsp;</span></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\"><br></span></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">Highlights by mode:&nbsp;</span></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\"><br></span></p></div><div style=\"text-align: left;\"><ul><ul><li style=\"text-align: left;\"><span style=\"text-align: -webkit-match-parent;\">Highway pavement and related work was</span><span style=\"text-align: -webkit-match-parent; font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">&nbsp;down slightly at $8.7 billion in&nbsp;<span style=\"font-size: 13.3333px;\">April 2025 compared to $8.8 billion in&nbsp;<span style=\"font-size: 13.3333px;\">April&nbsp;</span>2024</span>.&nbsp;&nbsp;</span></li><li style=\"text-align: left;\"><span style=\"text-align: -webkit-match-parent;\">Bridge work was $2.3 billion in&nbsp;<span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">April</span>, up 10 percent for the month compared to&nbsp;<span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">April&nbsp;</span>2024.&nbsp; &nbsp;</span></li><li style=\"text-align: left;\"><span style=\"text-align: -webkit-match-parent;\">Airport terminal and runway work was up 14 percent in&nbsp;<span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 13.3333px;\">April&nbsp;</span>compared to last year.&nbsp;&nbsp;</span></li><li style=\"text-align: left;\">Construction on water terminals, docks, piers, and wharfs was up 23 percent in April compared to last year and is up 17 percent through the first four months of 2025.</li></ul></ul></div><div style=\"text-align: left;\"><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">&nbsp;&nbsp;</span><br></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">The value of transportation construction put in place measures\nthe amount of work completed on a project in a given time period, regardless of\nthe size of the contract or when it was awarded.&nbsp;&nbsp;</span></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><br></p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><span style=\"font-size: 12pt; font-family: &quot;Times New Roman&quot;, serif;\">&nbsp;</span><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\"></span></p><p class=\"MsoNormal\" align=\"center\" style=\"margin-bottom: 0.0001pt; text-align: center; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\">\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n</p><p class=\"MsoNormal\" style=\"margin-bottom: 0in; line-height: normal; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\"><i><span style=\"font-size: 10pt; font-family: &quot;Times New Roman&quot;, serif; border: 1pt none windowtext; padding: 0in;\">June 2, 2025</span></i></p><p class=\"MsoNormal\" align=\"center\" style=\"text-align: center; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\">\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n</p><p style=\"background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial;\" class=\"MsoNormal\"><span style=\"color:black;mso-color-alt:windowtext\">&nbsp;</span></p></div>", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "ED390-C848-2B", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655384099a11833ea60aa4", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true}, "lastOpened": null}, {"title": "Monthly Value", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60aab", "desc": null, "source": "65982bc5de67d0004306a78f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}, "instanceid": "180A0-60EF-33"}]}, {"name": "values", "items": [{"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#00cee6", "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "31B88-6F4D-25"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "8F539-EEA9-E8"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}, {"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "lastOpened": null}, {"title": "YTD Value", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60aac", "desc": null, "source": "65982bc5de67d0004306a790", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "MonthStart", "dim": "[vpip.csv.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "2E5B0-D9B9-16"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM(([868B7-F82], [B395D-C53]))\n", "context": {"[868B7-F82]": {"table": "vpip.csv", "column": "ytd_value", "dim": "[vpip.csv.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[B395D-C53]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}}, "title": "YTD Value of Construction Work"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"color": "#00cee6", "type": "color"}}, "regressionType": "linear", "instanceid": "80204-29E7-8D"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "MonthStart", "dim": "[vpip.csv.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in MonthStart", "filter": {"last": {"count": 15, "offset": 0}, "custom": true}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "disabled": false, "title": "Years in MonthStart", "instanceid": "75D74-50C9-FC"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years", "title": "years", "singular": "years", "plural": "years"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false, "regression": {"enabled": false, "predictionMethod": "none", "periodsAdded": false}}, "dashboardid": "68655384099a11833ea60aa4", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true}}, "lastOpened": null}, {"title": "Monthly Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68655384099a11833ea60aad", "desc": null, "source": "65982bc5de67d0004306a791", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Mode Detail"}, "format": {"members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#218a8c", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#b2b2f7", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#7efb62", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#ffc26a", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}}}, "instanceid": "2AFD9-7ACD-48"}]}, {"name": "values", "items": [{"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "6D449-6B99-33"}]}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "type", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["Air Transportation Total", "Land Transportation Total", "Mass Transit Total", "Total Transportation", "Water Transportation Total", "Highways & streets"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "74CB8-2242-D1"}, {"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "2905F-4E13-21"}, {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "898C4-72BB-75"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode_detail", "title": "Mode Detail", "singular": "Mode Detail", "plural": "Mode Detail"}, {"id": "type", "title": "type", "singular": "type", "plural": "type"}, {"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "lastOpened": null}, {"title": "% Change in Monthly Value by Mode", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60aae", "desc": null, "source": "5e541073b882c5081c3cac04", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": []}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([2C866-C2E], [33FEF-D71], [51DAB-024])-([2C866-C2E], [33FEF-D71], [86D79-72D]))/([2C866-C2E], [33FEF-D71], [86D79-72D])", "context": {"[33FEF-D71]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[2C866-C2E]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[86D79-72D]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "[51DAB-024]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}}, "title": "% Change in Value"}, "format": {"mask": {"decimals": 1, "percent": true, "percentMultiplier": 100}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "33902-8E01-9C", "wpanel": "series", "panel": "measures"}]}, {"name": "break by", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203, "members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#218a8c", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#06e5ff", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#7efb62", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#ff8a86", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Bridges &amp; Tunnels": {"color": "#269fa1", "title": "Bridges &amp; Tunnels", "sortData": "Bridges &amp; Tunnels", "isHandPickedColor": true}, "Rail/Transit: Subway &amp; Light Rail": {"color": "#00cee6", "title": "Rail/Transit: Subway &amp; Light Rail", "sortData": "Rail/Transit: Subway &amp; Light Rail", "isHandPickedColor": true}, "Water: Docks, Piers &amp; Wharfs": {"color": "#9b9bd7", "title": "Water: Docks, Piers &amp; Wharfs", "sortData": "Water: Docks, Piers &amp; Wharfs", "isHandPickedColor": true}}}, "disabled": false, "instanceid": "526A2-BD65-C8", "wpanel": "series", "panel": "columns"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": false, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true, "text": "Percent Change"}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}]}}, "instanceid": "159B1-CDF7-85", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "prevSortObjects": [], "realTimeRefreshing": false, "viewState": {"activeTab": "filters"}, "lastOpened": null}, {"title": "", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "oid": "68655384099a11833ea60aaf", "desc": null, "source": "65982bc5de67d0004306a793", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"activeTab": "3", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "showTitle": false, "showSeparators": true, "useSelectedBkg": false, "useUnselectedBkg": false}, "instanceid": "7401C-F073-D8", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "autoUpdateOnEveryChange": true}, "dashboardid": "68655384099a11833ea60aa4", "script": "widget.tabs = [\n  {\n    title: \"Month\", \n    displayWidgetIds : [\"68655384099a11833ea60aad\",\"68655384099a11833ea60ab3\",\"68655384099a11833ea60aae\", \"68655384099a11833ea60aab\"], \n    hideWidgetIds : [\"68655384099a11833ea60aba\",\"68655384099a11833ea60ab9\", \"68655384099a11833ea60abb\", \"68655384099a11833ea60abc\",\"68655384099a11833ea60ab7\",\"68655384099a11833ea60ab6\",\"68655384099a11833ea60ab0\", \"68655384099a11833ea60ab1\", \"68655384099a11833ea60aac\", \"68655384099a11833ea60ab4\", \"68655384099a11833ea60ab5\", \"68655384099a11833ea60ab2\"],\n  },\n  { \n    title: \"YTD\", \n    displayWidgetIds : [\"68655384099a11833ea60ab6\",\"68655384099a11833ea60ab0\", \"68655384099a11833ea60ab1\", \"68655384099a11833ea60aac\"], \n    hideWidgetIds : [\"68655384099a11833ea60aba\",\"68655384099a11833ea60ab9\", \"68655384099a11833ea60abb\", \"68655384099a11833ea60abc\",\"68655384099a11833ea60aad\",\"68655384099a11833ea60ab7\",\"68655384099a11833ea60ab4\", \"68655384099a11833ea60ab5\", \"68655384099a11833ea60ab2\", \"68655384099a11833ea60ab3\",\"68655384099a11833ea60aae\", \"68655384099a11833ea60aab\"],\n  },\n\t  { \n    title: \"TTM\", \n    displayWidgetIds : [\"68655384099a11833ea60ab7\",\"68655384099a11833ea60ab4\", \"68655384099a11833ea60ab5\", \"68655384099a11833ea60ab2\"], \n    hideWidgetIds : [\"68655384099a11833ea60aba\",\"68655384099a11833ea60ab9\", \"68655384099a11833ea60abb\", \"68655384099a11833ea60abc\",\"68655384099a11833ea60aad\",\"68655384099a11833ea60ab6\",\"68655384099a11833ea60ab0\", \"68655384099a11833ea60ab1\", \"68655384099a11833ea60aac\", \"68655384099a11833ea60ab3\",\"68655384099a11833ea60aae\", \"68655384099a11833ea60aab\"] \n  },\n\t { \n    title: \"Annual\", \n    displayWidgetIds : [\"68655384099a11833ea60aba\",\"68655384099a11833ea60ab9\", \"68655384099a11833ea60abb\", \"68655384099a11833ea60abc\"], \n    hideWidgetIds : [\"68655384099a11833ea60aad\",\"68655384099a11833ea60ab6\",\"68655384099a11833ea60ab0\", \"68655384099a11833ea60ab1\", \"68655384099a11833ea60aac\", \"68655384099a11833ea60ab3\",\"68655384099a11833ea60aae\", \"68655384099a11833ea60aab\",\"68655384099a11833ea60ab7\",\"68655384099a11833ea60ab4\", \"68655384099a11833ea60ab5\", \"68655384099a11833ea60ab2\"] \n  }\n\t\n];\n", "custom": {"barcolumnchart": {"type": "WidgetsTabber", "isTypeValid": false}}, "tabs": [{"title": "Month", "displayWidgetIds": ["68655384099a11833ea60aad", "68655384099a11833ea60ab3", "68655384099a11833ea60aae", "68655384099a11833ea60aab"], "hideWidgetIds": ["68655384099a11833ea60ab7", "68655384099a11833ea60ab6", "68655384099a11833ea60ab0", "68655384099a11833ea60ab1", "68655384099a11833ea60aac", "68655384099a11833ea60ab4", "68655384099a11833ea60ab5", "68655384099a11833ea60ab2"]}, {"title": "YTD", "displayWidgetIds": ["68655384099a11833ea60ab6", "68655384099a11833ea60ab0", "68655384099a11833ea60ab1", "68655384099a11833ea60aac"], "hideWidgetIds": ["68655384099a11833ea60aad", "68655384099a11833ea60ab7", "68655384099a11833ea60ab4", "68655384099a11833ea60ab5", "68655384099a11833ea60ab2", "68655384099a11833ea60ab3", "68655384099a11833ea60aae", "68655384099a11833ea60aab"]}, {"title": "TTM", "displayWidgetIds": ["68655384099a11833ea60ab7", "68655384099a11833ea60ab4", "68655384099a11833ea60ab5", "68655384099a11833ea60ab2"], "hideWidgetIds": ["68655384099a11833ea60aad", "68655384099a11833ea60ab6", "68655384099a11833ea60ab0", "68655384099a11833ea60ab1", "68655384099a11833ea60aac", "68655384099a11833ea60ab3", "68655384099a11833ea60aae", "68655384099a11833ea60aab"]}], "lastOpened": null}, {"title": "YTD Value", "type": "pivot2", "subtype": "pivot", "oid": "68655384099a11833ea60ab0", "desc": null, "source": "65982bc5de67d0004306a794", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 230}, "disabled": false, "instanceid": "B1CE0-DE87-36", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM(([89184-201], [BCF6E-7A7]))", "context": {"[89184-201]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[62FC6-575]": {"table": "vpip.csv", "column": "ytd", "dim": "[vpip.csv.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[BCF6E-7A7]": {"table": "vpip.csv", "column": "ytd", "dim": "[vpip.csv.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}}, "title": "YTD Value", "datatype": "numeric"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "SUM(([89184-201], [BCF6E-7A7]))", "index": 2}, "instanceid": "EBF00-223E-60", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year"}, "field": {"id": "[vpip.csv.year]", "index": 1}, "instanceid": "3695F-C496-3C", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in Date", "level": "years", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2023-01-01T00:00:00", "2022-01-01T00:00:00", "2021-01-01T00:00:00", "2024-01-01T00:00:00", "2025-01-01T00:00:00"]}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "instanceid": "17FE8-2FB1-23", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": false, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}, {"id": "years_in_date", "title": "Years in Date", "singular": "Years in Date", "plural": "Years in Date"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "script_old": null, "script": null, "lastOpened": null}, {"title": "% Change in YTD Value by Mode", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60ab1", "desc": null, "source": "5e541073b882c5081c3cac07", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": []}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([3A4AC-0F5], [F553B-5E1], [A5E3A-E43])-([3A4AC-0F5], [F553B-5E1], [6BACB-D20]))/([3A4AC-0F5], [F553B-5E1], [6BACB-D20])", "context": {"[3A4AC-0F5]": {"table": "vpip.csv", "column": "ytd_value", "dim": "[vpip.csv.ytd_value]", "datatype": "numeric", "agg": "sum", "title": "Total ytd_value"}, "[F553B-5E1]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "[6BACB-D20]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "[A5E3A-E43]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}}, "title": "% Change in Value"}, "format": {"mask": {"decimals": 1, "percent": true, "percentMultiplier": 100}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "B92C0-1868-E9", "wpanel": "series", "panel": "measures"}]}, {"name": "break by", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203, "members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#218a8c", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#06e5ff", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#7efb62", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#ff8a86", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Bridges &amp; Tunnels": {"color": "#269fa1", "title": "Bridges &amp; Tunnels", "sortData": "Bridges &amp; Tunnels", "isHandPickedColor": true}, "Rail/Transit: Subway &amp; Light Rail": {"color": "#00cee6", "title": "Rail/Transit: Subway &amp; Light Rail", "sortData": "Rail/Transit: Subway &amp; Light Rail", "isHandPickedColor": true}, "Water: Docks, Piers &amp; Wharfs": {"color": "#9b9bd7", "title": "Water: Docks, Piers &amp; Wharfs", "sortData": "Water: Docks, Piers &amp; Wharfs", "isHandPickedColor": true}}}, "disabled": false, "instanceid": "1CF9B-E08E-8B", "wpanel": "series", "panel": "columns"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": false, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true, "text": "Percent Change"}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}]}}, "instanceid": "159B1-CDF7-85", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "prevSortObjects": [], "realTimeRefreshing": false, "lastOpened": null}, {"title": "TTM Value", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60ab2", "desc": null, "source": "65982bc5de67d0004306a796", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}, "continuous": false}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "6BF96-2EB4-62", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM([4F915-99A])\n", "context": {"[4F915-99A]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "TTM Value of Construction Work"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "regressionType": "none", "instanceid": "F8D14-835B-40", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "days", "title": "Months in Date", "filter": {"from": "2020-01-01"}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Months in Date", "instanceid": "470C4-6373-46", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": 0, "max": 63}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "showNulls": {"Months in Date": false, "YTD Value of Construction Work": false, "month_current": false, "State (Filters Contract Awards)": false, "Mode (Filters Contract Awards & VPIP)": false, "Mode Detail": false}, "dateLevel": "months", "lastOpened": null}, {"title": "Monthly Value", "type": "pivot2", "subtype": "pivot", "oid": "68655384099a11833ea60ab3", "desc": null, "source": "65982bc5de67d0004306a797", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 237}, "disabled": false, "instanceid": "48E8A-CD15-DA", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM(([F4894-8DF], [87B25-190]))", "context": {"[87B25-190]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[2A2E5-324]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[F4894-8DF]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "datatype": "numeric", "title": "Total Value"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "SUM(([F4894-8DF], [87B25-190]))", "index": 2}, "instanceid": "941D5-613C-66", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[Dates.Date (Calendar)]_years", "index": 1}, "instanceid": "0C7A2-6078-7C", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2023-01-01T00:00:00", "2022-01-01T00:00:00", "2021-01-01T00:00:00", "2024-01-01T00:00:00", "2025-01-01T00:00:00"]}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years", "instanceid": "46B02-7389-E<PERSON>", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": false, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}, {"id": "years", "title": "years", "singular": "years", "plural": "years"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "script_old": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "lastOpened": null}, {"title": "TTM Value", "type": "pivot2", "subtype": "pivot", "oid": "68655384099a11833ea60ab4", "desc": null, "source": "65982bc5de67d0004306a798", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203}, "disabled": false, "instanceid": "1DEFF-6307-5C", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "[86DBD-4AD]", "context": {"[86DBD-4AD]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "datatype": "numeric", "title": "Trailing 12-Month Total"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "[86DBD-4AD]", "index": 2}, "instanceid": "FBB2C-0CE2-36", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MMM yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm"}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[Dates.Date (Calendar)]_months", "index": 1}, "instanceid": "07627-B836-1C", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date", "filter": {"last": {"count": 6, "offset": 2}, "custom": true, "rankingMessage": ""}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Months in Date", "instanceid": "235A1-B23E-FD", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": false, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}, {"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "script_old": null, "script": null, "lastOpened": null}, {"title": "% Change in TTM Value by Mode", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60ab5", "desc": null, "source": "5e541073b882c5081c3cac0b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": []}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([9D313-027], [360B4-414], [E6E0C-11F])-([9D313-027], [360B4-414], [3C8E0-7B6]))/([9D313-027], [360B4-414], [3C8E0-7B6])", "context": {"[360B4-414]": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[9D313-027]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}, "[3C8E0-7B6]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2022"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "[E6E0C-11F]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}}, "title": "% Change in TTM Totals"}, "format": {"mask": {"decimals": 1, "percent": true, "percentMultiplier": 100}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "633E3-DA7F-B2", "wpanel": "series", "panel": "measures"}]}, {"name": "break by", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203, "members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#218a8c", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#06e5ff", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#7efb62", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#ff8a86", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Bridges &amp; Tunnels": {"color": "#269fa1", "title": "Bridges &amp; Tunnels", "sortData": "Bridges &amp; Tunnels", "isHandPickedColor": true}, "Rail/Transit: Subway &amp; Light Rail": {"color": "#00cee6", "title": "Rail/Transit: Subway &amp; Light Rail", "sortData": "Rail/Transit: Subway &amp; Light Rail", "isHandPickedColor": true}, "Water: Docks, Piers &amp; Wharfs": {"color": "#9b9bd7", "title": "Water: Docks, Piers &amp; Wharfs", "sortData": "Water: Docks, Piers &amp; Wharfs", "isHandPickedColor": true}}}, "disabled": false, "instanceid": "0EFD7-F4EA-DF", "wpanel": "series", "panel": "columns"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": false, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true, "text": "Percent Change"}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}]}}, "instanceid": "159B1-CDF7-85", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "prevSortObjects": [], "realTimeRefreshing": false, "lastOpened": null}, {"title": "YTD Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68655384099a11833ea60ab6", "desc": null, "source": "65982bc5de67d0004306a79a", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Mode Detail"}, "format": {"members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#218a8c", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#b2b2f7", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#7efb62", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#ffc26a", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}}}, "instanceid": "8581F-FE5B-E0"}]}, {"name": "values", "items": [{"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "13B05-0ED8-81"}]}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "type", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["Air Transportation Total", "Land Transportation Total", "Mass Transit Total", "Total Transportation", "Water Transportation Total", "Highways & streets"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "65766-B4A5-F2"}, {"jaql": {"table": "vpip.csv", "column": "ytd", "dim": "[vpip.csv.ytd]", "datatype": "numeric", "title": "ytd", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "B22E7-098D-BC"}, {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2025"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "AF1E5-36EE-6C"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode_detail", "title": "Mode Detail", "singular": "Mode Detail", "plural": "Mode Detail"}, {"id": "type", "title": "type", "singular": "type", "plural": "type"}, {"id": "ytd", "title": "ytd", "singular": "ytd", "plural": "ytd"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "lastOpened": null}, {"title": "TTM Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68655384099a11833ea60ab7", "desc": null, "source": "65982bc5de67d0004306a79b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Mode Detail"}, "format": {"members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#218a8c", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#b2b2f7", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#7efb62", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#ffc26a", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}}}, "instanceid": "60B2C-29D3-01"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([94DE4-57F])", "context": {"[94DE4-57F]": {"table": "vpip.csv", "column": "ttm", "dim": "[vpip.csv.ttm]", "datatype": "numeric", "agg": "sum", "title": "Total ttm"}}, "title": "TTM Value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "785E5-BD15-2A"}]}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "type", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["Air Transportation Total", "Land Transportation Total", "Mass Transit Total", "Total Transportation", "Water Transportation Total", "Highways & streets"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "66992-79CB-AC"}, {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "76046-93C3-BE"}, {"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "025EB-B492-BA"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode_detail", "title": "Mode Detail", "singular": "Mode Detail", "plural": "Mode Detail"}, {"id": "type", "title": "type", "singular": "type", "plural": "type"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}, {"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68655384099a11833ea60ab8", "desc": null, "source": "65982bc5de67d0004306a79c", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"currentCard": {"script": "", "title": "", "titleStyle": [{"backgroundColor": "", "backgroundImage": "", "display": "none"}], "showCarousel": true, "body": [{"type": "Container", "style": {"background-color": "#246482", "margin": "0%", "min-height": "5em"}, "items": [{"type": "Image", "style": {"width": "100%"}, "spacing": 0, "size": "large", "horizontalAlignment": "center", "url": "https://www.artba.org/wp-content/uploads/2024/03/economist.jpg"}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 12, "medium": 22, "large": 32, "extraLarge": 50}, "fontWeights": {"default": 400, "light": 200, "bold": 800}, "containerStyles": {"default": {"backgroundColor": "#ffffff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#dcdcdc"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#93c0c0"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": "100%"}, "imageSet": {"imageSize": "large", "maxImageHeight": 200}, "actions": {"color": "", "backgroundColor": "", "maxActions": 5, "spacing": "default", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 5, "medium": 10, "large": 0, "extraLarge": 0, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 0}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 234.297}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "45154-1759-FC", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true, "triggersDomready": true}, "dashboardid": "68655384099a11833ea60aa4", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}, {"title": "Annual Value", "type": "pivot2", "subtype": "pivot", "oid": "68655384099a11833ea60ab9", "desc": null, "source": "65982bc5de67d0004306a79d", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Geography.state]", "[Mode.major_mode]", "[executive_summary.csv.mode]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203}, "disabled": false, "instanceid": "4CEAC-574C-11", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "SUM([7E4D2-7DF])", "context": {"[7E4D2-7DF]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Total Value", "datatype": "numeric"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "number": {"separated": true}}, "color": {"type": "color"}}, "field": {"id": "SUM([7E4D2-7DF])", "index": 2}, "instanceid": "B4175-8FF2-9F", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[Dates.Date (Calendar)]_years", "index": 1}, "instanceid": "6D999-D5B8-A5", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2020-01-01T00:00:00", "2021-01-01T00:00:00", "2022-01-01T00:00:00", "2023-01-01T00:00:00", "2024-01-01T00:00:00"]}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Years", "instanceid": "31ADD-3FC1-57", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": false, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}, {"id": "years", "title": "years", "singular": "years", "plural": "years"}]}, "rowsGrandTotal": true, "columnsGrandTotal": false, "scroll": false}, "instanceid": "A4585-23A4-BA", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "script_old": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": false, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "lastOpened": null}, {"title": "% Change in Annual Value by Mode, 2024 vs. 2023", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60aba", "desc": null, "source": "5e6124973073732b6c20f0f7", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": []}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "(([C56DE-C4C], [D4D0B-568])-([C56DE-C4C], [124D8-6F2]))/([C56DE-C4C], [124D8-6F2])", "context": {"[C56DE-C4C]": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "[124D8-6F2]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "[D4D0B-568]": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}}, "title": "% Change in Value"}, "format": {"mask": {"decimals": 1, "percent": true, "percentMultiplier": 100}}, "field": {"id": "SUM(([E6B0C-CF6], [77E12-1C6]))", "index": 2}, "instanceid": "E6336-0E52-D7", "wpanel": "series", "panel": "measures"}]}, {"name": "break by", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Type of Construction"}, "field": {"id": "[vpip.csv.type]", "index": 0}, "format": {"subtotal": false, "width": 203, "members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#218a8c", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#06e5ff", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#7efb62", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#ff8a86", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Bridges &amp; Tunnels": {"color": "#269fa1", "title": "Bridges &amp; Tunnels", "sortData": "Bridges &amp; Tunnels", "isHandPickedColor": true}, "Rail/Transit: Subway &amp; Light Rail": {"color": "#00cee6", "title": "Rail/Transit: Subway &amp; Light Rail", "sortData": "Rail/Transit: Subway &amp; Light Rail", "isHandPickedColor": true}, "Water: Docks, Piers &amp; Wharfs": {"color": "#9b9bd7", "title": "Water: Docks, Piers &amp; Wharfs", "sortData": "Water: Docks, Piers &amp; Wharfs", "isHandPickedColor": true}}}, "disabled": false, "instanceid": "E6C5A-8818-E3", "wpanel": "series", "panel": "columns"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": false, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true, "text": "Percent Change"}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_construction", "title": "Type of Construction", "singular": "Type of Construction", "plural": "Type of Construction"}]}}, "instanceid": "159B1-CDF7-85", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "customCategoryConfiguration": ["2019"], "sortBreakByOption": "Asc by Total"}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "prevSortObjects": [], "realTimeRefreshing": false, "lastOpened": null}, {"title": "2024 Annual Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "oid": "68655384099a11833ea60abb", "desc": null, "source": "65982bc5de67d0004306a79f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "Mode Detail"}, "format": {"members": {"Highway: Other Work": {"color": "#fc7570", "title": "Highway: Other Work", "sortData": "Highway: Other Work", "isHandPickedColor": true}, "Highway: Pavements": {"color": "#fbb755", "title": "Highway: Pavements", "sortData": "Highway: Pavements", "isHandPickedColor": true}, "Rail/Transit: Railroads": {"color": "#218a8c", "title": "Rail/Transit: Railroads", "sortData": "Rail/Transit: Railroads", "isHandPickedColor": true}, "Rail/Transit: Terminals": {"color": "#b2b2f7", "title": "Rail/Transit: Terminals", "sortData": "Rail/Transit: Terminals", "isHandPickedColor": true}, "Rail/Transit: Vehicle Service": {"color": "#7efb62", "title": "Rail/Transit: Vehicle Service", "sortData": "Rail/Transit: Vehicle Service", "isHandPickedColor": true}, "Water: Terminals": {"color": "#ffc26a", "title": "Water: Terminals", "sortData": "Water: Terminals", "isHandPickedColor": true}, "Bridges &amp; Tunnels": {"color": "#269fa1", "title": "Bridges &amp; Tunnels", "sortData": "Bridges &amp; Tunnels", "isHandPickedColor": true}, "Rail/Transit: Subway &amp; Light Rail": {"color": "#00cee6", "title": "Rail/Transit: Subway &amp; Light Rail", "sortData": "Rail/Transit: Subway &amp; Light Rail", "isHandPickedColor": true}, "Water: Docks, Piers &amp; Wharfs": {"color": "#9b9bd7", "title": "Water: Docks, Piers &amp; Wharfs", "sortData": "Water: Docks, Piers &amp; Wharfs", "isHandPickedColor": true}}}, "instanceid": "7A2B8-584D-A4"}]}, {"name": "values", "items": [{"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "A385B-727F-7F"}]}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "type", "dim": "[vpip.csv.type]", "datatype": "text", "merged": true, "title": "type", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["Air Transportation Total", "Land Transportation Total", "Mass Transit Total", "Total Transportation", "Water Transportation Total", "Highways & streets"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "D8345-6BD6-39"}, {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": true, "multiSelection": true, "members": ["2024"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "F803B-0E65-EB"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": false, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "mode_detail", "title": "Mode Detail", "singular": "Mode Detail", "plural": "Mode Detail"}, {"id": "type", "title": "type", "singular": "type", "plural": "type"}, {"id": "year", "title": "year", "singular": "year", "plural": "year"}]}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}}, "instanceid": "D09C8-4CD1-2A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "lastOpened": null}, {"title": "Annual Value", "type": "chart/column", "subtype": "column/classic", "oid": "68655384099a11833ea60abc", "desc": null, "source": "65982bc5de67d0004306a7a0", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"drillHistory": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, {"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "value"}, "parent": {"jaql": {"table": "vpip.csv", "column": "month", "dim": "[vpip.csv.month]", "datatype": "numeric", "merged": true, "title": "month"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year1"}, "parent": {"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.year]", "title": "year1", "column": "year", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["2018"]}}}}, "through": {"jaql": {"datatype": "numeric", "dim": "[vpip.csv.month]", "title": "month", "column": "month", "table": "vpip.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["6"]}}}}], "ignore": {"dimensions": ["[executive_summary.csv.mode]", "[Geography.state]"], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year"}, "instanceid": "64BED-4494-79", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Annual Value of Construction Work", "agg": "sum"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "originalJaql": {"table": "vpip.csv", "column": "value", "dim": "[vpip.csv.value]", "datatype": "numeric", "title": "Monthly Value of Construction Work", "agg": "sum"}, "quickFunction": false, "regressionType": "linear", "instanceid": "87256-3F04-2C", "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "year", "dim": "[vpip.csv.year]", "datatype": "numeric", "merged": true, "title": "year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2025"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "533D0-851D-76", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": false, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "year", "title": "year", "singular": "year", "plural": "year"}]}}, "instanceid": "961A9-CB7B-BC", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "68655384099a11833ea60aa4", "lastOpened": null}], "hierarchies": []}