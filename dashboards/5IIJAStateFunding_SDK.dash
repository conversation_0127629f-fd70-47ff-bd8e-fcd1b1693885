{"title": "5. IIJA State Funding_SDK", "desc": "", "source": "6144a3a85fc2ba4950ce4ee6", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "A023D-14CC-C5", "type": "columnar", "columns": [{"width": 100, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 1049, "minWidth": 128, "maxWidth": 2048, "height": "140px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b8e"}], "width": 100, "stretchable": false, "pxlWidth": 2108.19, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "40px", "defaultWidth": 128, "widgetid": "6865547a099a11833ea60b8f"}], "width": 100, "stretchable": false, "pxlWidth": 2108.19, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "164px", "defaultWidth": 128, "widgetid": "6865547a099a11833ea60b97"}], "width": 100, "stretchable": false, "pxlWidth": 2108.19, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 72, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "132px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b9a"}], "width": 20, "stretchable": false, "pxlWidth": 263.524, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "height": "132px", "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b94"}], "width": 20, "stretchable": false, "pxlWidth": 263.524, "index": 1}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b98", "height": "132px"}], "width": 20, "stretchable": false, "pxlWidth": 527.049, "index": 2}, {"elements": [{"minHeight": 68, "maxHeight": 1049, "minWidth": 128, "maxWidth": 2048, "height": "132px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b95"}], "width": 20, "stretchable": false, "pxlWidth": 527.049, "index": 3}, {"elements": [{"minHeight": 68, "maxHeight": 1049, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b96", "height": "132px"}], "width": 20, "stretchable": false, "pxlWidth": 527.049, "index": 4}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "472px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b91"}], "width": 50, "stretchable": false, "pxlWidth": 1054.1, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "472px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b93"}], "width": 50, "stretchable": false, "pxlWidth": 1054.1, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "490px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b90"}], "width": 50, "stretchable": false, "pxlWidth": 1054.1, "index": 0}, {"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "490px", "defaultWidth": 512, "widgetid": "6865547a099a11833ea60b92", "autoHeight": "491px"}], "width": 50, "stretchable": false, "pxlWidth": 1054.1, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "36px", "defaultWidth": 128, "widgetid": "6865547a099a11833ea60b99"}], "width": 100, "stretchable": false, "pxlWidth": 2108.19, "index": 0}]}], "pxlWidth": 2108.19, "index": 0}]}, "original": null, "previewLayout": [], "oid": "6865547a099a11833ea60b8d", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "filters": [{"jaql": {"datatype": "text", "dim": "[iija_dim_mode.csv.mode_desc]", "title": "mode_desc", "column": "mode_desc", "table": "iija_dim_mode.csv", "collapsed": true, "merged": true, "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "5C3D2-6F46-9B", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "text", "dim": "[dim_state.csv.stname]", "title": "State", "column": "stname", "table": "dim_state.csv", "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "3CEA2-F18D-B5", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "datetime", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "title": "Fiscal Year", "level": "years", "column": "YearStart", "table": "iija_dim_year.csv", "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "0D3F4-5183-D2", "isCascading": false, "disabled": false}], "editing": false, "settings": {"autoUpdateOnFiltersChange": true}, "parentFolder": "68654e16099a11833ea60a10", "filterToDatasourceMapping": {}, "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[iija_dim_mode.csv.mode_desc]", "title": "mode_desc", "column": "mode_desc", "table": "iija_dim_mode.csv", "collapsed": true, "merged": true, "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "5C3D2-6F46-9B", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "text", "dim": "[dim_state.csv.stname]", "title": "State", "column": "stname", "table": "dim_state.csv", "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "3CEA2-F18D-B5", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "datetime", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "title": "Fiscal Year", "level": "years", "column": "YearStart", "table": "iija_dim_year.csv", "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "0D3F4-5183-D2", "isCascading": false, "disabled": false}], "allowChangeSubscription": false, "isPublic": null, "lastOpened": null, "filterRelations": [], "widgets": [{"title": "", "type": "BloX", "subtype": "BloX", "oid": "6865547a099a11833ea60b8e", "desc": null, "source": "65b1928d15b06800405d4ce9", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": [{"jaql": {"table": "iija_fact.csv", "column": "state", "dim": "[iija_fact.csv.state]", "datatype": "text", "merged": true, "title": "state"}, "field": {"id": "[iija_fact.csv.state]", "index": 0}, "PanelName": "state"}]}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "Container", "spacing": "none", "items": [{"type": "Image", "url": "https://transportationinvestment.org/wp-content/uploads/2019/11/tiac-dashboard.png", "size": "auto", "spacing": "none", "style": {"width": "100%", "height": "100%", "margin": 0, "padding": 0}}]}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 500, "light": 100, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#ffcb05", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "white", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": ""}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}]}}, "instanceid": "E633C-185B-EB", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "filter", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "6865547a099a11833ea60b8f", "desc": null, "source": "65b1928d15b06800405d4cea", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"5\">State Formula Highway, Bridge, and Transit Funding In the Infrastructure Investment&nbsp;&amp; Jobs Act (IIJA)&nbsp;</font>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "F33AC-C8C2-3F", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "Breakdown of Formula Funds", "type": "chart/pie", "subtype": "pie/classic", "oid": "6865547a099a11833ea60b90", "desc": null, "source": "65b1928d15b06800405d4ceb", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "iija_dim_mode.csv", "column": "mode_desc", "dim": "[iija_dim_mode.csv.mode_desc]", "datatype": "text", "merged": true, "title": "Program"}, "format": {"members": {"Core Highway Fund": {"color": "#005880", "title": "Core Highway Fund", "sortData": "Core Highway Fund"}, "EV Charging Station Formula Funds": {"color": "#d39a47", "title": "EV Charging Station Formula Funds", "sortData": "EV Charging Station Formula Funds", "inResultset": true}, "Supplemental Bridge Formula Funds": {"color": "#5cb747", "title": "Supplemental Bridge Formula Funds", "sortData": "Supplemental Bridge Formula Funds", "inResultset": true}, "Transit Formula Funds": {"color": "#ab504c", "title": "Transit Formula Funds", "sortData": "Transit Formula Funds", "inResultset": true}, "Core Highway Program": {"color": "#005880", "title": "Core Highway Program", "sortData": "Core Highway Program", "inResultset": true}}}}]}, {"name": "values", "items": [{"jaql": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Federal Funding"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}}}]}, {"name": "filters", "items": []}]}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "program", "title": "Program", "singular": "Program", "plural": "Program"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "titleAlign": "center", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}}, "instanceid": "3ABE3-A673-5B", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "6865547a099a11833ea60b8d", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "lastOpened": null}, {"title": "Highway, Bridge, & Transit Formula Funds", "type": "map/area", "subtype": "areamap/usa", "oid": "6865547a099a11833ea60b91", "desc": null, "source": "65b1928d15b06800405d4cec", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "color", "items": [{"jaql": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Federal Investment"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"rangeMode": "auto", "type": "range", "steps": 8}}}]}, {"name": "filters", "items": []}]}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}]}, "title": {"titleMenuEnabled": true, "titleAlign": "center", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}}, "instanceid": "6FAC4-6688-F3", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "6865547a099a11833ea60b8d", "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "lastOpened": null}, {"title": "State Breakdown of FY 2021 Appropriations and IIJA Formula Funds, FY 2022 to FY 2026", "type": "pivot", "subtype": "pivot", "oid": "6865547a099a11833ea60b92", "desc": null, "source": "65b1928d15b06800405d4ced", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "iija_fact.csv", "column": "state", "dim": "[iija_fact.csv.state]", "datatype": "text", "merged": true, "title": "State"}, "field": {"id": "[iija_fact.csv.state]", "index": 0}}]}, {"name": "values", "items": [{"jaql": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Formula Funds"}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[iija_fact.csv.value]_sum", "index": 2}}]}, {"name": "columns", "items": [{"jaql": {"table": "iija_dim_year.csv", "column": "year", "dim": "[iija_dim_year.csv.year]", "datatype": "numeric", "title": "Fiscal Year"}, "field": {"id": "[iija_dim_year.csv.year]", "index": 1}}]}, {"name": "filters", "items": []}]}, "style": {"pageSize": 15, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleAlign": "center", "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold"}, "rowsGrandTotal": true}, "instanceid": "EB0FE-87FE-4A", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true}, "dashboardid": "6865547a099a11833ea60b8d", "custom": {"barcolumnchart": {"type": "pivot", "isTypeValid": false}}, "lastOpened": null}, {"title": "Annual Formula Funds, FY 2021 Approriations & IIJA", "type": "chart/column", "subtype": "column/stackedcolumn", "oid": "6865547a099a11833ea60b93", "desc": null, "source": "65b1928d15b06800405d4cee", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "iija_dim_year.csv", "column": "YearStart", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Fiscal Year"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "9DE47-A661-F4", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Federal Funds"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 2, "currency": {"symbol": "$", "position": "pre"}}, "color_bkp": {"colorIndex": 0, "type": "color"}}, "instanceid": "BC6D0-7F3C-2A", "panel": "measures"}]}, {"name": "break by", "items": [{"jaql": {"table": "iija_dim_mode.csv", "column": "mode_desc", "dim": "[iija_dim_mode.csv.mode_desc]", "datatype": "text", "merged": true, "title": "Type of Program"}, "format": {"members": {"EV Charging Station Formula Funds": {"color": "#d39a47", "title": "EV Charging Station Formula Funds", "sortData": "EV Charging Station Formula Funds", "isHandPickedColor": true}, "Supplemental Bridge Formula Funds": {"color": "#5cb747", "title": "Supplemental Bridge Formula Funds", "sortData": "Supplemental Bridge Formula Funds", "isHandPickedColor": true}, "Transit Formula Funds": {"color": "#ab504c", "title": "Transit Formula Funds", "sortData": "Transit Formula Funds", "isHandPickedColor": true}, "Core Highway Program": {"color": "#005880", "title": "Core Highway Program", "sortData": "Core Highway Program", "isHandPickedColor": true}}}, "instanceid": "66CB0-E9C0-26", "panel": "columns"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": true, "stacked": true, "stackedPercentage": false, "types": {"count": false, "relative": true, "totals": true, "percentage": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true, "text": "Federal Fiscal Year"}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}, {"id": "type_of_program", "title": "Type of Program", "singular": "Type of Program", "plural": "Type of Program"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}}, "instanceid": "9C74A-37AB-7D", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 0, "max": 5}, "selectorLocked": false}, "dashboardid": "6865547a099a11833ea60b8d", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": true, "addTotalOption": "No", "sortCategoriesOption": "None", "sortBreakByOption": "None", "customCategoryConfiguration": ["Fri Jan 01 2021 00:00:00 GMT-0500 (Eastern Standard Time)", "Sat Jan 01 2022 00:00:00 GMT-0500 (Eastern Standard Time)", "Sun Jan 01 2023 00:00:00 GMT-0500 (Eastern Standard Time)", "Mon Jan 01 2024 00:00:00 GMT-0500 (Eastern Standard Time)", "Wed Jan 01 2025 00:00:00 GMT-0500 (Eastern Standard Time)", "Thu Jan 01 2026 00:00:00 GMT-0500 (Eastern Standard Time)"]}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('processresult', function(ev, args){ \n\targs.result.plotOptions.series.dataLabels.style.fontSize = \"12px\";\n\targs.result.plotOptions.series.dataLabels.style.color  = \"white\";\n\targs.result.plotOptions.series.dataLabels.style.fontWeight = \"bold\";\n});", "realTimeRefreshing": false, "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "6865547a099a11833ea60b94", "desc": null, "source": "65b1928d15b06800405d4cef", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "((([8C612-AA3],[FBCAB-248])-([8C612-AA3],[0F73A-54E]))/([8C612-AA3],[0F73A-54E]))", "context": {"[0F73A-54E]": {"table": "iija_dim_year.csv", "column": "YearStart", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in YearStart", "filter": {"explicit": true, "multiSelection": true, "members": ["2021-01-01T00:00:00"]}, "collapsed": false, "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}}, "[FBCAB-248]": {"table": "iija_dim_year.csv", "column": "YearStart", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in YearStart", "filter": {"explicit": true, "multiSelection": true, "members": ["2022-01-01T00:00:00"]}, "collapsed": false, "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}}, "[8C612-AA3]": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "title": "Total value", "agg": "sum"}}, "title": "Increase FY2021-FY2022"}, "format": {"mask": {"decimals": 1, "percent": true}, "color": {"type": "color", "color": "#005880"}}}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "E82A3-D141-F1", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "6865547a099a11833ea60b8d", "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "6865547a099a11833ea60b95", "desc": null, "source": "65b1928d15b06800405d4cf0", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "TextBlock", "text": "", "style": {"text-align": "center", "font-weight": "bold", "font-size": "24px", "padding": 0, "margin": 0}}]}], "actions": [{"type": "Action.OpenUrl", "title": "ARTBA's Full IIJA Analysis", "url": "https://www.artba.org/wp-content/uploads/2021/08/ARTBA_Senate_Bill_Analysis_August2021.pdf"}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 800, "light": 500, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#fff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "white", "backgroundColor": "#b31622", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 8, "style": {"padding": "10", "font-weight": "bold"}}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 40}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "59EF1-F529-A2", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "6865547a099a11833ea60b8d", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "6865547a099a11833ea60b96", "desc": null, "source": "65b1928d15b06800405d4cf1", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "TextBlock", "text": "", "style": {"text-align": "center", "font-weight": "bold", "font-size": "24px", "padding": 0, "margin": 0}}]}], "actions": [{"type": "Action.OpenUrl", "title": "State Economic Impacts of IIJA", "url": "https://www.artba.org/economics/iija-impact/"}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 800, "light": 500, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#fff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "white", "backgroundColor": "#b31622", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 8, "style": {"padding": "10", "font-weight": "bold"}}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 40}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "59EF1-F529-A2", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "6865547a099a11833ea60b97", "desc": null, "source": "65b1928d15b06800405d4cf2", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<div style=\"text-align: left;\"><font size=\"3\"><font style=\"\">Signed into law Nov. 15, five-year Infrastructure </font>Investment<font style=\"\">&nbsp;&amp; Jobs Act (IIJA) provides the largest increase in federal highway, bridge, and transit funding in six decades.&nbsp; It offers a generational opportunity to repair and modernize every state's transportation system, while </font>simultaneously<font style=\"\">&nbsp;</font>delivering<font style=\"\">&nbsp;tangible economic benefits for years to come.&nbsp; &nbsp;</font></font></div><font size=\"3\"><br></font><div style=\"text-align: left;\"><font size=\"3\">The IIJA provides $347.8 billion for highways, and $91 billion for transit between FY 2022 and FY 2026.&nbsp; This dashboard highlights the formula funds that would be available to each state through the core highway program, the supplemental bridge program and the EV Charging program.&nbsp; It does not include discretionary programs that will be administered by the U.S. Department&nbsp;of Transportation, including the Nationally Significant&nbsp;Freight and Highway&nbsp;Projects Program, the Bridge Discretionary Grant Program, or the&nbsp;Reconnecting&nbsp;Communities&nbsp;Pilot Program.&nbsp;&nbsp;</font></div><div style=\"text-align: left;\"><font size=\"3\"><br></font></div><div style=\"text-align: left;\"><font size=\"3\">For ARTBA's full analysis of the bill, as well as the state-level economic impacts from the federal investment, click on the links below.&nbsp;&nbsp;</font></div>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "B5FEB-8AA6-1A", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "", "type": "indicator", "subtype": "indicator/numeric", "oid": "6865547a099a11833ea60b98", "desc": null, "source": "65b1928d15b06800405d4cf3", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "[7E0BC-E41]", "context": {"[7E0BC-E41]": {"table": "iija_fact.csv", "column": "value", "dim": "[iija_fact.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "IIJA Formula Funding"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#005880"}}}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "iija_dim_year.csv", "column": "YearStart", "dim": "[iija_dim_year.csv.YearStart (Calendar)]", "datatype": "datetime", "merged": true, "title": "Years in YearStart", "level": "years", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2021-01-01T00:00:00"]}}, "collapsed": false, "datasource": {"title": "IIJA", "fullname": "LocalHost/IIJA", "id": "aLOCALHOST_aIIJA", "address": "LocalHost", "database": "aIIJA"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}}]}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "years_in_yearstart", "title": "Years in YearStart", "singular": "Years in YearStart", "plural": "Years in YearStart"}]}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}}, "instanceid": "E82A3-D141-F1", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "6865547a099a11833ea60b99", "desc": null, "source": "65b1928d15b06800405d4cf4", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"2\"><div style=\"text-align: left;\">Source: Data provided by FHWA and the Senate Environment and Public Works Committee.</div></font>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "73946-FD53-B1", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "6865547a099a11833ea60b8d", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "6865547a099a11833ea60b9a", "desc": null, "source": "65b1928d15b06800405d4cf5", "datasource": {"address": "LocalHost", "title": "IIJA", "id": "aLOCALHOST_aIIJA", "database": "aIIJA", "fullname": "LocalHost/IIJA"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}]}}, "instanceid": "96E84-CB55-58", "displayMenu": false, "fullResults": [["Alabama"], ["Alaska"], ["Arizona"], ["Arkansas"], ["California"], ["Colorado"], ["Connecticut"], ["Delaware"], ["District of Columbia"], ["Florida"], ["Georgia"], ["Hawaii"], ["Idaho"], ["Illinois"], ["Indiana"], ["Iowa"], ["Kansas"], ["Kentucky"], ["Louisiana"], ["Maine"], ["Maryland"], ["Massachusetts"], ["Michigan"], ["Minnesota"], ["Mississippi"], ["Missouri"], ["Montana"], ["Nebraska"], ["Nevada"], ["New Hampshire"], ["New Jersey"], ["New Mexico"], ["New York"], ["North Carolina"], ["North Dakota"], ["Ohio"], ["Oklahoma"], ["Oregon"], ["Pennsylvania"], ["Puerto Rico"], ["Rhode Island"], ["South Carolina"], ["South Dakota"], ["Tennessee"], ["Texas"], ["Utah"], ["Vermont"], ["Virginia"], ["Washington"], ["West Virginia"], ["Wisconsin"], ["Wyoming"]], "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true}, "dashboardid": "6865547a099a11833ea60b8d", "listOpen": true, "zIndex": 100, "AllMembersSelected": true, "allFilteredMembersSelected": true, "multiSelect": true, "membersSelected": ["Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "District of Columbia", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey", "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Puerto Rico", "Rhode Island", "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"], "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "lastOpened": null}], "hierarchies": []}