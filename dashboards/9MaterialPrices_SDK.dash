{"title": "9. Material Prices_SDK", "desc": "", "source": "622a674aca7c6135304beb71", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "36E8B-8502-BC", "type": "columnar", "columns": [{"width": 100, "cells": [{"subcells": [{"elements": [{"minHeight": 102, "maxHeight": 1024, "minWidth": 128, "maxWidth": 2048, "height": "148px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c64"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "68px", "defaultWidth": 128, "widgetid": "686554e7099a11833ea60c54"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c65", "height": "156px"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 343.664, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c63", "height": "156px"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 343.664, "index": 1}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "height": "156px", "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c55"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 343.664, "index": 2}]}, {"subcells": [{"elements": [{"minHeight": 32, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "100px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c66"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "394px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c56", "autoHeight": "394px"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "height": "156px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c67"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5a", "height": "156px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5b", "height": "804px", "autoHeight": "1030px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c59", "height": "804px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5c", "height": "472px", "autoHeight": "472px"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "588px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c57"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c58", "height": "588px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5d", "height": "480px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c62", "height": "480px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5e", "height": "452px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c60", "height": "452px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c61", "height": "464px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c5f", "height": "464px"}], "width": 50, "stretchable": false, "pxlWidth": 515.5, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": 128, "defaultWidth": 128, "widgetid": "686554e7099a11833ea60c68"}], "width": 100, "stretchable": false, "pxlWidth": 1031, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 102, "maxHeight": 1024, "minWidth": 128, "maxWidth": 2048, "height": "36px", "defaultWidth": 512, "widgetid": "686554e7099a11833ea60c69"}], "index": 0, "stretchable": false, "width": 100, "pxlWidth": 1031}]}], "pxlWidth": 1031, "index": 0}]}, "original": null, "previewLayout": [], "oid": "686554e7099a11833ea60c53", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "07A37-E5DE-25", "isCascading": false}], "editing": false, "filterToDatasourceMapping": {}, "parentFolder": "68654e16099a11833ea60a10", "allowChangeSubscription": false, "isPublic": null, "settings": {"autoUpdateOnFiltersChange": true}, "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "07A37-E5DE-25", "isCascading": false}], "lastOpened": null, "defaultFilterRelations": null, "filterRelations": [], "widgets": [{"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554e7099a11833ea60c54", "desc": null, "source": "65ae9cd715b06800405d3892", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"6\">National Commodity and Material Prices for Transportation Construction</font><br>", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "05884-0FD5-C9", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "686554e7099a11833ea60c55", "desc": null, "source": "65ae9cd715b06800405d3893", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "([487CB-31D],[704EC-E45], [2A284-93F])", "context": {"[704EC-E45]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[6863D-1C6]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["2"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[2A284-93F]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[487CB-31D]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Highway & Street Construction Inputs"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "number": {"separated": true}}, "color": {"type": "color", "color": "#0c5268"}}}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([BDA42-F0B],[AB68D-294], [6C9E9-8BF])-([BDA42-F0B],[C21A8-7BC],[6C9E9-8BF]))/([BDA42-F0B],[C21A8-7BC],[6C9E9-8BF])", "context": {"[AB68D-294]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[C21A8-7BC]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["2"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[6C9E9-8BF]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[BDA42-F0B]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "% Change From Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "6505F-2585-54", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "686554e7099a11833ea60c53", "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "lastOpened": null}, {"title": "Construction, Highway and Street Inputs", "type": "pivot", "subtype": "pivot", "oid": "686554e7099a11833ea60c56", "desc": null, "source": "65ae9cd715b06800405d3894", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]", "[material_prices.csv.series_name]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Indices"}, "field": {"id": "[material_prices.csv.series_name]", "index": 0}, "instanceid": "F2A37-794A-95", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([9AD9A-243])", "context": {"[9AD9A-243]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}}, "title": "Index Value", "datatype": "numeric"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 1, "number": {"separated": true}}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "([9AD9A-243])", "index": 2}, "instanceid": "34878-B46E-18", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "By Month"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}, "width": 115}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[material_prices.csv.Date (Calendar)]_months", "index": 1}, "instanceid": "F57D8-8956-5E", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Highway Inputs", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs", "Highway and street inputs: Energy", "Highway and street inputs: Goods", "Highway and street inputs: Goods less food and energy", "Highway and street inputs: Services", "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services", "Highway and street inputs: Trade Services", "Highway and street inputs: Transportation & Warehousing Services", "Inputs to Construction", "Inputs to New Construction", "Inputs to New Non-Residential Construction", "Inputs to New Residential Construction"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "title": "Highway Inputs", "instanceid": "561B2-88A3-67", "panel": "scope"}, {"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "By Month", "filter": {"last": {"count": 12, "offset": 0}, "custom": true}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "instanceid": "F36C9-E1C2-B9", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "indices", "title": "Indices", "singular": "Indices", "plural": "Indices"}, {"id": "by_month", "title": "By Month", "singular": "By Month", "plural": "By Month"}, {"id": "highway_inputs", "title": "Highway Inputs", "singular": "Highway Inputs", "plural": "Highway Inputs"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "27991-03AB-<PERSON>", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554e7099a11833ea60c53", "custom": {"barcolumnchart": {"type": "pivot", "isTypeValid": false}}, "lastOpened": null}, {"title": "Highway & Street Construction Inputs", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c57", "desc": null, "source": "65ae9cd715b06800405d3895", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "0A4E7-0941-07"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([1D19A-7F4], [5CE29-D32])", "context": {"[1D19A-7F4]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}, "[5CE29-D32]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "73A4F-E9CD-19"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "dim_ppi_month_date.csv", "column": "MonthStart", "dim": "[dim_ppi_month_date.csv.MonthStart (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in MonthStart", "filter": {"explicit": false, "multiSelection": true, "all": true}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}, "collapsed": true}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "instanceid": "1854F-77ED-AC"}]}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}, {"id": "months_in_monthstart", "title": "Months in MonthStart", "singular": "Months in MonthStart", "plural": "Months in MonthStart"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 66, "max": 122}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "Major Components of the Highway & Street Construction Inputs", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c58", "desc": null, "source": "65ae9cd715b06800405d3896", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "EE8C1-88E5-F3"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([08B6D-FF8], [C681E-9FE])", "context": {"[08B6D-FF8]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}, "[39D82-BC2]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs: Energy", "Highway and street inputs: Goods", "Highway and street inputs: Transportation & Warehousing Services"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[C681E-9FE]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs: Energy"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "Highway and street inputs: Energy"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "24670-B860-3B"}, {"jaql": {"type": "measure", "formula": "([1CCED-C10], [C50C0-723])", "context": {"[1CCED-C10]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}, "[84C72-077]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[C50C0-723]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs: Goods"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "Highway and street inputs: Goods"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"color": "#9b9bd7", "type": "color"}}, "instanceid": "CFD69-AF33-22"}, {"jaql": {"type": "measure", "formula": "([858AA-F4D], [8D8E6-9E2])", "context": {"[858AA-F4D]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}, "[BA887-596]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[8D8E6-9E2]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Highway and street inputs: Services"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "Highway and street inputs: Services"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"color": "#6EDA55", "type": "color"}}, "instanceid": "9E840-DF85-58"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 69, "max": 122}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "Major Inputs and Commodities for Transportation Construction (base years may differ)", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c59", "desc": null, "source": "65ae9cd715b06800405d3897", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]", "[material_prices.csv.Date (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Month"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "values", "items": [{"jaql": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Index Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color_bkp": {"type": "color", "color": "#005880"}}}]}, {"name": "break by", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Input/Commodity"}, "format": {"members": {"Architectural & Engineering Services": {"color": "#00cee6", "title": "Architectural & Engineering Services", "sortData": "Architectural & Engineering Services", "inResultset": true}, "Asphalt": {"color": "#9b9bd7", "title": "<PERSON><PERSON><PERSON>", "sortData": "<PERSON><PERSON><PERSON>", "inResultset": true}, "Asphalt Paving Mixtures & Blocks": {"color": "#6EDA55", "title": "Asphalt Paving Mixtures & Blocks", "sortData": "Asphalt Paving Mixtures & Blocks", "inResultset": true}, "Carbon steel castings": {"color": "#fc7570", "title": "Carbon steel castings", "sortData": "Carbon steel castings", "inResultset": true}, "Carbon steel scrap": {"color": "#fbb755", "title": "Carbon steel scrap", "sortData": "Carbon steel scrap", "inResultset": true}, "Concrete block and brick": {"color": "#218A8C", "title": "Concrete block and brick", "sortData": "Concrete block and brick", "inResultset": true}, "Concrete Pavers": {"color": "#06e5ff", "title": "<PERSON><PERSON><PERSON>", "sortData": "<PERSON><PERSON><PERSON>", "inResultset": true}, "Concrete Pipe (including culverts)": {"color": "#b2b2f7", "title": "Concrete Pipe (including culverts)", "sortData": "Concrete Pipe (including culverts)", "inResultset": true}, "Construction Equipment Rental & Leasing": {"color": "#7efb62", "title": "Construction Equipment Rental & Leasing", "sortData": "Construction Equipment Rental & Leasing", "inResultset": true}, "Construction sand, gravel, and crushed stone": {"color": "#ff8a86", "title": "Construction sand, gravel, and crushed stone", "sortData": "Construction sand, gravel, and crushed stone", "inResultset": true}, "Consumer Price Index (CPI)": {"color": "#ffc26a", "title": "Consumer Price Index (CPI)", "sortData": "Consumer Price Index (CPI)", "inResultset": true}, "Diesel Fuel": {"color": "#269fa1", "title": "Diesel Fuel", "sortData": "Diesel Fuel", "inResultset": true}, "Fabricated steel plate": {"color": "#00cee6", "title": "Fabricated steel plate", "sortData": "Fabricated steel plate", "inResultset": true}, "Gasoline": {"color": "#9b9bd7", "title": "Gasoline", "sortData": "Gasoline", "inResultset": true}, "Hot rolled steel bars, plates, and structural shapes": {"color": "#6eda55", "title": "Hot rolled steel bars, plates, and structural shapes", "sortData": "Hot rolled steel bars, plates, and structural shapes", "inResultset": true}, "Hot rolled steel sheet and strip": {"color": "#fc7570", "title": "Hot rolled steel sheet and strip", "sortData": "Hot rolled steel sheet and strip", "inResultset": true}, "Hydraulic Cement": {"color": "#fbb755", "title": "Hydraulic Cement", "sortData": "Hydraulic Cement", "inResultset": true}, "Iron and steel": {"color": "#218a8c", "title": "Iron and steel", "sortData": "Iron and steel", "inResultset": true}, "Machinery and equipment: mixers, pavers, and related equipment": {"color": "#06e5ff", "title": "Machinery and equipment: mixers, pavers, and related equipment", "sortData": "Machinery and equipment: mixers, pavers, and related equipment", "inResultset": true}, "Ready-mixed concrete": {"color": "#b2b2f7", "title": "Ready-mixed concrete", "sortData": "Ready-mixed concrete", "inResultset": true}, "Highway and street inputs": {"color": "#7efb62", "title": "Highway and street inputs", "sortData": "Highway and street inputs", "inResultset": true}, "Highway and street inputs: Energy": {"color": "#ff8a86", "title": "Highway and street inputs: Energy", "sortData": "Highway and street inputs: Energy", "inResultset": true}, "Highway and street inputs: Goods": {"color": "#ffc26a", "title": "Highway and street inputs: Goods", "sortData": "Highway and street inputs: Goods", "inResultset": true}, "Highway and street inputs: Goods less food and energy": {"color": "#269fa1", "title": "Highway and street inputs: Goods less food and energy", "sortData": "Highway and street inputs: Goods less food and energy", "inResultset": true}, "Highway and street inputs: Services": {"color": "#00cee6", "title": "Highway and street inputs: Services", "sortData": "Highway and street inputs: Services", "inResultset": true}, "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services": {"color": "#9b9bd7", "title": "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services", "sortData": "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services", "inResultset": true}, "Highway and street inputs: Trade Services": {"color": "#6eda55", "title": "Highway and street inputs: Trade Services", "sortData": "Highway and street inputs: Trade Services", "inResultset": true}, "Highway and street inputs: Transportation & Warehousing Services": {"color": "#fc7570", "title": "Highway and street inputs: Transportation & Warehousing Services", "sortData": "Highway and street inputs: Transportation & Warehousing Services", "inResultset": true}, "Inputs to Construction": {"color": "#fbb755", "title": "Inputs to Construction", "sortData": "Inputs to Construction", "inResultset": true}, "Inputs to New Construction": {"color": "#218a8c", "title": "Inputs to New Construction", "sortData": "Inputs to New Construction", "inResultset": true}, "Inputs to New Non-Residential Construction": {"color": "#06e5ff", "title": "Inputs to New Non-Residential Construction", "sortData": "Inputs to New Non-Residential Construction", "inResultset": true}, "Inputs to New Residential Construction": {"color": "#b2b2f7", "title": "Inputs to New Residential Construction", "sortData": "Inputs to New Residential Construction", "inResultset": true}, "Stainless and other alloy steel scrap": {"color": "#7efb62", "title": "Stainless and other alloy steel scrap", "sortData": "Stainless and other alloy steel scrap", "inResultset": true}, "Prestressed Concrete Bridge Beams": {"color": "#ff8a86", "title": "Prestressed Concrete Bridge Beams", "sortData": "Prestressed Concrete Bridge Beams", "inResultset": true}, "Aluminum Panels, parts, and sections for prefabricated buildings": {"color": "#ffc26a", "title": "Aluminum Panels, parts, and sections for prefabricated buildings", "sortData": "Aluminum Panels, parts, and sections for prefabricated buildings", "inResultset": true}, "PVC Conduits, incl%46 plastic conduit & fittings": {"color": "#269fa1", "title": "PVC Conduits, incl. plastic conduit & fittings", "sortData": "PVC Conduits, incl. plastic conduit & fittings", "inResultset": true}, "Copper Scrap": {"color": "#00cee6", "title": "Copper Scrap", "sortData": "Copper Scrap", "inResultset": true}}}}]}, {"name": "filters", "items": []}]}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "month", "title": "Month", "singular": "Month", "plural": "Month"}, {"id": "input/commodity", "title": "Input/Commodity", "singular": "Input/Commodity", "plural": "Input/Commodity"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": false, "previousScrollerLocation": {"min": 124, "max": 148}, "selectorLocked": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "686554e7099a11833ea60c5a", "desc": null, "source": "65ae9cd715b06800405d3898", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]", "[material_prices.csv.Date (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "([7AB13-686],[6F971-362])", "context": {"[6F971-362]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[7AB13-686]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}}, "title": "Index Average"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#005880"}}}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([41705-423],[CF4AC-A3E])-([41705-423],[1D634-7CB]))/([41705-423],[1D634-7CB])", "context": {"[CF4AC-A3E]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[1D634-7CB]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["2"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[41705-423]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}}, "title": "% Change From Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "F311C-1709-64", "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "Producer Price Indices for Transportation Construction and Commodities", "type": "pivot2", "subtype": "pivot", "oid": "686554e7099a11833ea60c5b", "desc": null, "source": "65ae9cd715b06800405d3899", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]", "[material_prices.csv.Date (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "All Indices and Commodities", "sort": "asc", "sortDetails": {"field": 0, "dir": "asc", "sortingLastDimension": true, "initialized": true, "isLastApplied": true}}, "field": {"id": "[material_prices.csv.series_name]", "index": 0}, "instanceid": "640C8-995B-B9", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Index Value", "sort": null}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "[material_prices.csv.value]_sum", "index": 2}, "instanceid": "CC84A-6DC1-7D", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date", "sort": null}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[material_prices.csv.Date (Calendar)]_months", "index": 1}, "instanceid": "AD06B-A940-EF", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date", "filter": {"last": {"count": 11, "offset": 0}, "custom": true, "rankingMessage": ""}, "collapsed": true, "sort": null, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "title": "Months in Date", "instanceid": "D5186-65A6-C0", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 39, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "all_indices_and_commodities", "title": "All Indices and Commodities", "singular": "All Indices and Commodities", "plural": "All Indices and Commodities"}, {"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}, "scroll": false}, "instanceid": "27991-03AB-<PERSON>", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "Producer Price Indices for Major Commodities", "type": "pivot", "subtype": "pivot", "oid": "686554e7099a11833ea60c5c", "desc": null, "source": "65ae9cd715b06800405d389a", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]", "[material_prices.csv.series_name]"], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Commodity/Service"}, "field": {"id": "[material_prices.csv.series_name]", "index": 0}, "format": {"width": 561}}]}, {"name": "values", "items": [{"jaql": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Index Value"}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "transparent"}}, "field": {"id": "[material_prices.csv.value]_sum", "index": 2}}]}, {"name": "columns", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}, "width": 123}, "hierarchies": ["calendar", "calendar - weeks"], "field": {"id": "[material_prices.csv.Date (Calendar)]_months", "index": 1}}]}, {"name": "filters", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date", "filter": {"last": {"count": 12, "offset": 0}, "custom": true}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}}, {"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Commodity/Service", "filter": {"explicit": true, "multiSelection": true, "members": ["Architectural & Engineering Services", "<PERSON><PERSON><PERSON>", "Asphalt Paving Mixtures & Blocks", "Carbon steel castings", "Carbon steel scrap", "Concrete block and brick", "<PERSON><PERSON><PERSON>", "Concrete Pipe (including culverts)", "Construction Equipment Rental & Leasing", "Construction sand, gravel, and crushed stone", "Diesel Fuel", "Gasoline", "Hydraulic Cement", "Machinery and equipment: mixers, pavers, and related equipment", "Prestressed Concrete Bridge Beams", "Ready-mixed concrete"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "title": "Commodity/Service"}]}]}, "style": {"pageSize": 25, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": false}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "commodity/service", "title": "Commodity/Service", "singular": "Commodity/Service", "plural": "Commodity/Service"}, {"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "27991-03AB-<PERSON>", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "pivot", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "<PERSON><PERSON><PERSON>", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c5d", "desc": null, "source": "65ae9cd715b06800405d389b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "8B1B5-8560-D4"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([05F6A-59C], [0501A-6C4])", "context": {"[05F6A-59C]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "avg", "title": "Average value"}, "[0501A-6C4]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[00308-482]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#005880"}}, "instanceid": "779D3-4372-77"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in date", "singular": "Months in date", "plural": "Months in date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 4, "max": 153}}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "Construction sand and gravel (aggregates)", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c5e", "desc": null, "source": "65ae9cd715b06800405d389c", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"]}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([11E6F-9F7], [6E95F-304])", "context": {"[11E6F-9F7]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}, "[4C123-E8C]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[6E95F-304]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Construction sand, gravel, and crushed stone"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}]}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": true}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 0, "max": 153}}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "Machinery and equipment: mixers, pavers, and related equipment", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c5f", "desc": null, "source": "65ae9cd715b06800405d389d", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "A45C9-56D3-47"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([1A097-2F6], [B5B97-62F])", "context": {"[1A097-2F6]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}, "[3CCFB-89F]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[B5B97-62F]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Machinery and equipment: mixers, pavers, and related equipment"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "6DED9-6348-BC"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 58, "max": 151}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "Diesel Fuel", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c60", "desc": null, "source": "65ae9cd715b06800405d389e", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "B25A7-2535-6A"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([FC32C-1C1], [1F0BC-CEF])", "context": {"[FC32C-1C1]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}, "[4E898-87B]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[1F0BC-CEF]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Diesel Fuel"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "69D69-744C-3F"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 107, "max": 152}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "Ready-Mix Concrete", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c61", "desc": null, "source": "65ae9cd715b06800405d389f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "C3185-2883-D6"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([C74B6-4C5], [99241-D0B])", "context": {"[C74B6-4C5]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}, "[66AEE-16B]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[99241-D0B]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Ready-mixed concrete"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "0DD24-FE90-94"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 67, "max": 148}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "Concrete Block and Brick", "type": "chart/line", "subtype": "line/basic", "oid": "686554e7099a11833ea60c62", "desc": null, "source": "65ae9cd715b06800405d38a0", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "material_prices.csv", "column": "Date", "dim": "[material_prices.csv.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in Date"}, "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "dateAndTime": "MM/dd/y HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "instanceid": "4AF6E-6C89-A6"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([F439E-4D3], [69081-B47])", "context": {"[F439E-4D3]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "title": "Average value", "agg": "avg"}, "[8463F-191]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["<PERSON><PERSON><PERSON>"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[69081-B47]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Concrete block and brick"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}, "title": "PPI Value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color", "color": "#0c5268"}}, "instanceid": "52A35-9886-C6"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_date", "title": "Months in Date", "singular": "Months in Date", "plural": "Months in Date"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "72B2E-C00A-98", "displayMenu": false, "custom": {"barcolumnchart": {"type": "chart/line", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": 75, "max": 151}}, "dashboardid": "686554e7099a11833ea60c53", "prevSortObjects": [], "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "686554e7099a11833ea60c63", "desc": null, "source": "65ae9cd715b06800405d38a1", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "([C272A-5AC], [D3FC3-687], [515C9-07A])", "context": {"[515C9-07A]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[5DECA-C59]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current"}, "[D3FC3-687]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Inputs to Construction"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[C272A-5AC]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "General Construction Inputs"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "number": {"separated": true}}, "color": {"type": "color", "color": "#0c5268"}}}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([4E32B-93C],[B2886-4DA],[1C237-B44])-([4E32B-93C],[3A3D7-B8F],[1C237-B44]))/([4E32B-93C],[3A3D7-B8F],[1C237-B44])", "context": {"[B2886-4DA]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[3A3D7-B8F]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["2"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[1C237-B44]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Inputs to Construction"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[4E32B-93C]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "% Change From Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": []}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "6505F-2585-54", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "686554e7099a11833ea60c64", "desc": null, "source": "65ae9cd715b06800405d38a2", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "Container", "spacing": "none", "items": [{"type": "Image", "url": "https://transportationinvestment.org/wp-content/uploads/2019/11/tiac-dashboard.png", "size": "auto", "spacing": "none", "style": {"width": "100%", "height": "140%", "margin": 0, "padding": 0}}]}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 500, "light": 100, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#ffcb05", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "large", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "white", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": ""}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "96035-7EC1-C9", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "686554e7099a11833ea60c65", "desc": null, "source": "65ae9cd715b06800405d38a3", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_dates.csv.WeekStart (Calendar)]", "[dim_company.csv.company_name]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "([FC335-FF9],[D800B-447], [17965-C80])", "context": {"[D800B-447]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[17965-C80]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Consumer Price Index (CPI)", "Consumer Price Index - Not Seasonally Adj. (CPI)"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[82F26-6FE]": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Consumer Price Index (CPI)"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[FC335-FF9]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Consumer Price Index"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "number": {"separated": true}}, "color": {"type": "color", "color": "#0c5268"}}}]}, {"name": "secondary", "items": [{"jaql": {"type": "measure", "formula": "(([F6109-10C],[EEB0D-C50])-([F6109-10C],[A33FC-CBB]))/([F6109-10C],[A33FC-CBB])", "context": {"[EEB0D-C50]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[A33FC-CBB]": {"table": "material_prices.csv", "column": "month_current", "dim": "[material_prices.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["2"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}, "[F6109-10C]": {"table": "material_prices.csv", "column": "value", "dim": "[material_prices.csv.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "% Change From Last Year:"}, "format": {"mask": {"decimals": 1, "percent": true}}}]}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "series_name", "filter": {"explicit": true, "multiSelection": true, "members": ["Consumer Price Index (CPI)", "Consumer Price Index - Not Seasonally Adj. (CPI)"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "oid": "7a3f3a90-f17a-4df0-96ca-182012149f16"}}}]}]}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": false, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "series_name", "title": "series_name", "singular": "series_name", "plural": "series_name"}]}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontColorEnabled": true, "titleFontColor": "#005880", "titleAlign": "center"}}, "instanceid": "6505F-2585-54", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "oid": "686554e7099a11833ea60c66", "desc": null, "source": "65ae9cd715b06800405d38a4", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"activeTab": "0", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "showTitle": false, "showSeparators": true, "useSelectedBkg": false, "useUnselectedBkg": false}, "instanceid": "BC860-BDDA-DB", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "autoUpdateOnEveryChange": true}, "dashboardid": "686554e7099a11833ea60c53", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.tabs = [\n  {\n    title: \"Construction, Highway and street inputs\", \n    displayWidgetIds : [\"686554e7099a11833ea60c56\",\"686554e7099a11833ea60c57\",\"686554e7099a11833ea60c58\"], \n    hideWidgetIds : [\"686554e7099a11833ea60c67\", \"686554e7099a11833ea60c53\",\"686554e7099a11833ea60c5c\",\"686554e7099a11833ea60c5d\", \"686554e7099a11833ea60c62\", \"686554e7099a11833ea60c5e\", \"686554e7099a11833ea60c60\", \"686554e7099a11833ea60c61\", \"686554e7099a11833ea60c5f\",\"61b25f6325769833c0028ad4\", \"686554e7099a11833ea60c5a\", \"686554e7099a11833ea60c5b\", \"686554e7099a11833ea60c59\"] \n  },\n  { \n    title: \"Major commodities\", \n    displayWidgetIds : [\"686554e7099a11833ea60c5c\",\"686554e7099a11833ea60c5d\", \"686554e7099a11833ea60c62\", \"686554e7099a11833ea60c5e\", \"686554e7099a11833ea60c60\", \"686554e7099a11833ea60c61\", \"686554e7099a11833ea60c5f\"], \n    hideWidgetIds : [\"686554e7099a11833ea60c67\",\"686554e7099a11833ea60c53\",\"686554e7099a11833ea60c56\",\"686554e7099a11833ea60c57\",\"686554e7099a11833ea60c58\",\"61b25f6325769833c0028ad4\", \"686554e7099a11833ea60c5a\", \"686554e7099a11833ea60c5b\", \"686554e7099a11833ea60c59\"],\n  },\n { \n    title: \"Select your input\", \n    displayWidgetIds : [\"61b25f6325769833c0028ad4\", \"686554e7099a11833ea60c5a\", \"686554e7099a11833ea60c5b\", \"686554e7099a11833ea60c59\", \"686554e7099a11833ea60c67\"], \n    hideWidgetIds : [\"686554e7099a11833ea60c53\",\"686554e7099a11833ea60c56\",\"686554e7099a11833ea60c57\",\"686554e7099a11833ea60c58\", \"686554e7099a11833ea60c5c\",\"686554e7099a11833ea60c5d\", \"686554e7099a11833ea60c62\", \"686554e7099a11833ea60c5e\", \"686554e7099a11833ea60c60\", \"686554e7099a11833ea60c61\", \"686554e7099a11833ea60c5f\" ]\n  }\n];", "custom": {"barcolumnchart": {"type": "WidgetsTabber", "isTypeValid": false}}, "tabs": [{"title": "Construction, Highway and street inputs", "displayWidgetIds": ["686554e7099a11833ea60c56", "686554e7099a11833ea60c57", "686554e7099a11833ea60c58"], "hideWidgetIds": ["686554e7099a11833ea60c53", "686554e7099a11833ea60c5c", "686554e7099a11833ea60c5d", "686554e7099a11833ea60c62", "686554e7099a11833ea60c5e", "686554e7099a11833ea60c60", "686554e7099a11833ea60c61", "686554e7099a11833ea60c5f", "61b25f6325769833c0028ad4", "686554e7099a11833ea60c5a", "686554e7099a11833ea60c5b", "686554e7099a11833ea60c59"]}, {"title": "Major commodities", "displayWidgetIds": ["686554e7099a11833ea60c5c", "686554e7099a11833ea60c5d", "686554e7099a11833ea60c62", "686554e7099a11833ea60c5e", "686554e7099a11833ea60c60", "686554e7099a11833ea60c61", "686554e7099a11833ea60c5f"], "hideWidgetIds": ["686554e7099a11833ea60c53", "686554e7099a11833ea60c56", "686554e7099a11833ea60c57", "686554e7099a11833ea60c58", "61b25f6325769833c0028ad4", "686554e7099a11833ea60c5a", "686554e7099a11833ea60c5b", "686554e7099a11833ea60c59"]}, {"title": "Select your input", "displayWidgetIds": ["61b25f6325769833c0028ad4", "686554e7099a11833ea60c5a", "686554e7099a11833ea60c5b", "686554e7099a11833ea60c59"], "hideWidgetIds": ["686554e7099a11833ea60c53", "686554e7099a11833ea60c56", "686554e7099a11833ea60c57", "686554e7099a11833ea60c58", "686554e7099a11833ea60c5c", "686554e7099a11833ea60c5d", "686554e7099a11833ea60c62", "686554e7099a11833ea60c5e", "686554e7099a11833ea60c60", "686554e7099a11833ea60c61", "686554e7099a11833ea60c5f"]}], "lastOpened": null}, {"title": "'", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554e7099a11833ea60c67", "desc": null, "source": "65ae9cd715b06800405d38a5", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[dim_company.csv.company_name]", "[material_prices.csv.Date (Calendar)]"], "all": true, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "material_prices.csv", "column": "series_name", "dim": "[material_prices.csv.series_name]", "datatype": "text", "merged": true, "title": "Select a Commodity"}, "instanceid": "E483E-04DC-DD"}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "select_a_commodity", "title": "Select a Commodity", "singular": "Select a Commodity", "plural": "Select a Commodity"}]}}, "instanceid": "F6ED3-64DA-DA", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true}, "dashboardid": "686554e7099a11833ea60c53", "fullResults": [[{"data": "Aluminum Panels, parts, and sections for prefabricated buildings", "text": "Aluminum Panels, parts, and sections for prefabricated buildings"}], [{"data": "Aluminum Production & Processing", "text": "Aluminum Production & Processing"}], [{"data": "Architectural & Engineering Services", "text": "Architectural & Engineering Services"}], [{"data": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>"}], [{"data": "Asphalt Paving Mixtures & Blocks", "text": "Asphalt Paving Mixtures & Blocks"}], [{"data": "Carbon steel castings", "text": "Carbon steel castings"}], [{"data": "Carbon steel scrap", "text": "Carbon steel scrap"}], [{"data": "Concrete block and brick", "text": "Concrete block and brick"}], [{"data": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>"}], [{"data": "Concrete Pipe (including culverts)", "text": "Concrete Pipe (including culverts)"}], [{"data": "Construction Equipment Rental & Leasing", "text": "Construction Equipment Rental & Leasing"}], [{"data": "Construction sand, gravel, and crushed stone", "text": "Construction sand, gravel, and crushed stone"}], [{"data": "Consumer Price Index - Not Seasonally Adj. (CPI)", "text": "Consumer Price Index - Not Seasonally Adj. (CPI)"}], [{"data": "Copper Scrap", "text": "Copper Scrap"}], [{"data": "Diesel Fuel", "text": "Diesel Fuel"}], [{"data": "Fabricated steel plate", "text": "Fabricated steel plate"}], [{"data": "Gasoline", "text": "Gasoline"}], [{"data": "Highway and street inputs", "text": "Highway and street inputs"}], [{"data": "Highway and street inputs: Energy", "text": "Highway and street inputs: Energy"}], [{"data": "Highway and street inputs: Goods", "text": "Highway and street inputs: Goods"}], [{"data": "Highway and street inputs: Goods less food and energy", "text": "Highway and street inputs: Goods less food and energy"}], [{"data": "Highway and street inputs: Services", "text": "Highway and street inputs: Services"}], [{"data": "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services", "text": "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services"}], [{"data": "Highway and street inputs: Trade Services", "text": "Highway and street inputs: Trade Services"}], [{"data": "Highway and street inputs: Transportation & Warehousing Services", "text": "Highway and street inputs: Transportation & Warehousing Services"}], [{"data": "Hot rolled steel bars, plates, and structural shapes", "text": "Hot rolled steel bars, plates, and structural shapes"}], [{"data": "Hot rolled steel sheet and strip", "text": "Hot rolled steel sheet and strip"}], [{"data": "Hydraulic Cement", "text": "Hydraulic Cement"}], [{"data": "Inputs to Construction", "text": "Inputs to Construction"}], [{"data": "Inputs to New Construction", "text": "Inputs to New Construction"}], [{"data": "Inputs to New Non-Residential Construction", "text": "Inputs to New Non-Residential Construction"}], [{"data": "Inputs to New Residential Construction", "text": "Inputs to New Residential Construction"}], [{"data": "Iron and steel", "text": "Iron and steel"}], [{"data": "Machinery and equipment: mixers, pavers, and related equipment", "text": "Machinery and equipment: mixers, pavers, and related equipment"}], [{"data": "Prestressed Concrete Products, Including Bridge Beams", "text": "Prestressed Concrete Products, Including Bridge Beams"}], [{"data": "PVC Conduits, incl. plastic conduit & fittings", "text": "PVC Conduits, incl. plastic conduit & fittings"}], [{"data": "PVC, Conduits and Fittings", "text": "PVC, Conduits and Fittings"}], [{"data": "Ready-mixed concrete", "text": "Ready-mixed concrete"}], [{"data": "Stainless and other alloy steel scrap", "text": "Stainless and other alloy steel scrap"}]], "listOpen": false, "membersSelected": ["Aluminum Panels, parts, and sections for prefabricated buildings", "Architectural & Engineering Services", "<PERSON><PERSON><PERSON>", "Asphalt Paving Mixtures & Blocks", "Carbon steel castings", "Carbon steel scrap", "Concrete block and brick", "<PERSON><PERSON><PERSON>", "Concrete Pipe (including culverts)", "Construction Equipment Rental & Leasing", "Construction sand, gravel, and crushed stone", "Consumer Price Index (CPI)", "Copper Scrap", "Diesel Fuel", "Fabricated steel plate", "Gasoline", "Highway and street inputs", "Highway and street inputs: Energy", "Highway and street inputs: Goods", "Highway and street inputs: Goods less food and energy", "Highway and street inputs: Services", "Highway and street inputs: Services Less Trade, Transportation & Warehousing Services", "Highway and street inputs: Trade Services", "Highway and street inputs: Transportation & Warehousing Services", "Hot rolled steel bars, plates, and structural shapes", "Hot rolled steel sheet and strip", "Hydraulic Cement", "Inputs to Construction", "Inputs to New Construction", "Inputs to New Non-Residential Construction", "Inputs to New Residential Construction", "Iron and steel", "Machinery and equipment: mixers, pavers, and related equipment", "Prestressed Concrete Bridge Beams", "PVC Conduits, incl. plastic conduit & fittings", "Ready-mixed concrete", "Stainless and other alloy steel scrap"], "excludeMembers": [], "AllMembersSelected": true, "allFilteredMembersSelected": true, "zIndex": 100, "multiSelect": true, "AllMemberSelected": false, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "custom": {"barcolumnchart": {"type": "advancedFilterWidget", "isTypeValid": false}}, "prevSortObjects": [], "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554e7099a11833ea60c68", "desc": null, "source": "65ae9cd715b06800405d38a6", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<font size=\"3\"><b>About the Data:</b><br></font><div>The national data below includes a series of indices from the U.S. Bureau of Labor Statistics to measure relative price changes in the economy.&nbsp; The consumer price index (CPI) is a measure of the average changes in consumer prices over time and is widely cited as a measure of general inflation.&nbsp; Specific goods, services, and commodities are measured in the Producer Price Index (PPI) series, a family of indexes measuring the average change over time in the prices received by producers for domestically produced goods, services, and construction.&nbsp; &nbsp;The PPI for Highway &amp; Street Construction includes weighted prices for the different components used to deliver highway and bridge projects.&nbsp; It does not include labor, capital investment, or imports.&nbsp; The main index is also broken out into energy, service, and goods, highlighted below.&nbsp; The PPI index for different inputs for transportation construction are included separately in the \"Major Commodities\" and \"Select Your Input\" options below.&nbsp;&nbsp;</div><div><br></div><div>To price change for a given material between two periods of time can be calculated using the percent change.&nbsp; As an example, if the index is 100 in the base year and increase to 110, prices for that commodity have risen 10 percent over that time period.&nbsp; For questions, please contact ARTBA's Chief E<PERSON>omist <PERSON> (ablack@<a rel=\"noopener noreferrer\" href=\"https://dashboard.artba.org/app/artba.org)\" target=\"_blank\">artba.org)</a>&nbsp;or Senior Economist Josh Hurwitz (<EMAIL>).</div>", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "left"}}, "instanceid": "6E216-795A-2A", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554e7099a11833ea60c69", "desc": null, "source": "65ae9cd715b06800405d38a7", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "", "vAlign": "valign-middle", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "05884-0FD5-C9", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554e7099a11833ea60c53", "lastOpened": null}], "hierarchies": []}