{"title": "4. Federal-Aid Obligations_SDK", "desc": "", "source": "5d7fb4902469173fb4e2b209", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "0951E-C8BC-C9", "type": "columnar", "columns": [{"width": 30.57093881856541, "cells": [{"subcells": [{"elements": [{"minHeight": 68, "maxHeight": 937, "minWidth": 128, "maxWidth": 2048, "height": 234.297, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b61"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "40px", "defaultWidth": 128, "widgetid": "68655464099a11833ea60b5b"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "32px", "defaultWidth": 128, "widgetid": "68655464099a11833ea60b59"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "476px", "defaultWidth": 128, "widgetid": "68655464099a11833ea60b5e"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b66", "height": "472px"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b67", "height": "512px"}], "width": 100, "stretchable": false, "pxlWidth": 444.5, "index": 0}]}], "pxlWidth": 444.5, "index": 0}, {"width": 69.42906118143459, "pxlWidth": 1009.49, "index": 1, "cells": [{"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 1028, "height": "180px", "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b58"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 336.492, "index": 0}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b5a", "height": "180px"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 336.492, "index": 1}, {"elements": [{"minHeight": 64, "maxHeight": 1028, "minWidth": 48, "maxWidth": 1028, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b5c", "height": "180px"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 336.492, "index": 2}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "468px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b5f"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 32, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "84px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b65"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 256, "maxHeight": 2048, "minWidth": 256, "maxWidth": 2048, "height": "356px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b60", "autoHeight": "360px"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "472px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b62"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "528px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b68"}], "width": 50, "stretchable": false, "pxlWidth": 504.742, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "68655464099a11833ea60b63", "height": "528px"}], "width": 50, "stretchable": false, "pxlWidth": 504.742, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 256, "maxHeight": 2048, "minWidth": 256, "maxWidth": 2048, "height": "1476px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b5d", "autoHeight": "1476px"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 256, "maxHeight": 2048, "minWidth": 256, "maxWidth": 2048, "height": "475px", "defaultWidth": 512, "widgetid": "68655464099a11833ea60b64", "autoHeight": "475px"}], "width": 100, "stretchable": false, "pxlWidth": 1009.49, "index": 0}]}]}]}, "original": null, "previewLayout": [], "oid": "68655464099a11833ea60b57", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "45EC6-28AE-D6", "isCascading": false}], "editing": false, "filterToDatasourceMapping": {}, "parentFolder": "68654e16099a11833ea60a10", "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "instanceid": "45EC6-28AE-D6", "isCascading": false}, {"jaql": {"table": "fhwa_fact.csv", "column": "type_major", "dim": "[fhwa_fact.csv.type_major]", "datatype": "text", "merged": true, "title": "Type of FHWA Funds", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "EEB97-31DC-F0", "isCascading": false, "disabled": false}], "allowChangeSubscription": false, "isPublic": null, "lastOpened": null, "settings": {"autoUpdateOnFiltersChange": true}, "defaultFilterRelations": null, "filterRelations": [], "widgets": [{"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655464099a11833ea60b58", "desc": null, "source": "659d7e6fde67d0004306bd85", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"table": "obligations_box.csv", "column": "monthly", "dim": "[obligations_box.csv.monthly]", "datatype": "numeric", "agg": "sum", "title": "Monthly Obligations"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#0e6481", "isHandPickedColor": true}}, "instanceid": "9B929-240A-86"}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "awards", "column": "month_current", "dim": "[awards.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T11:06:28"}}, "disabled": true, "instanceid": "95836-F823-9C"}]}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}]}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false, "dirty": true}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "drillTarget": {"oid": "5d02a5e9a761c42adc78836c", "caption": "_drill_monthly"}}, "dashboardid": "68655464099a11833ea60b57", "drilledDashboardDisplay": {}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655464099a11833ea60b59", "desc": null, "source": "659d7e6fde67d0004306bd86", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<p class=\"MsoNormal\"><b style=\"\"><font size=\"3\">April 2025 Data</font></b></p>", "vAlign": "valign-middle", "bgColor": "#ffffff", "textAlign": "center"}}, "instanceid": "0859A-5D6C-5D", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655464099a11833ea60b57", "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/numeric", "oid": "68655464099a11833ea60b5a", "desc": null, "source": "659d7e6fde67d0004306bd87", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Dates.Date (Calendar)]"], "all": false, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"table": "obligations_box.csv", "column": "ytd", "dim": "[obligations_box.csv.ytd]", "datatype": "numeric", "agg": "sum", "title": "Fiscal YTD Obligations"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#0e6481", "isHandPickedColor": true}}, "instanceid": "5ABB2-5949-C0"}]}, {"name": "secondary", "items": []}, {"name": "min", "items": []}, {"name": "max", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "vpip.csv", "column": "month_current", "dim": "[vpip.csv.month_current]", "datatype": "numeric", "title": "month_current", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["0"]}}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "disabled": true, "instanceid": "8F86C-5B0B-1E"}, {"jaql": {"table": "Dates", "column": "Year", "dim": "[Dates.Year]", "datatype": "numeric", "merged": true, "title": "Year", "filter": {"explicit": true, "multiSelection": true, "members": ["2019"]}, "collapsed": false, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics", "lastBuildTime": "2019-08-06T09:47:15"}}, "disabled": true, "instanceid": "5FF90-0652-1D"}]}], "usedFormulasMapping": {}}, "style": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": true, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "month_current", "title": "month_current", "singular": "month_current", "plural": "month_current"}, {"id": "year", "title": "Year", "singular": "Year", "plural": "Year"}]}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655464099a11833ea60b57", "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655464099a11833ea60b5b", "desc": null, "source": "659d7e6fde67d0004306bd88", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<div style=\"text-align: center;\"><b style=\"\"><font size=\"3\">Obligation of Federal-Aid Highway &amp;&nbsp;</font></b></div><div style=\"text-align: center;\"><b style=\"\"><font size=\"3\">Highway Safety Construction Funds</font></b><br></div>", "vAlign": "valign-middle", "bgColor": "#ffffff", "textAlign": "center"}}, "instanceid": "DE60D-D481-FC", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655464099a11833ea60b57", "lastOpened": null}, {"title": "'", "type": "indicator", "subtype": "indicator/gauge", "oid": "68655464099a11833ea60b5c", "desc": null, "source": "659d7e6fde67d0004306bd89", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": true, "ids": []}, "panels": [{"name": "value", "items": [{"jaql": {"type": "measure", "formula": "sum(22/47)", "context": {}, "title": "FY25 Percent Formula Funds Committed"}, "format": {"mask": {"decimals": 1, "percent": true}, "color": {"type": "color", "color": "#0e6481", "isHandPickedColor": true}}, "instanceid": "B1F01-040D-8A"}]}, {"name": "secondary", "items": []}, {"name": "min", "items": [{"jaql": {"formula": "0", "title": "Invalid Formula"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": false}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}}, "instanceid": "1E612-4926-7F"}]}, {"name": "max", "items": [{"jaql": {"type": "measure", "formula": "1\n", "context": {}, "title": "1\n"}, "format": {"mask": {"decimals": "auto", "percent": true}}, "instanceid": "F06E8-60FF-1B"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "indicator/numeric": {"subtype": "simple", "skin": "vertical", "components": {"title": {"inactive": false, "enabled": true}, "icon": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "indicator/gauge": {"subtype": "round", "skin": "1", "components": {"ticks": {"inactive": false, "enabled": true}, "labels": {"inactive": false, "enabled": true}, "title": {"inactive": false, "enabled": true}, "secondaryTitle": {"inactive": true, "enabled": true}}}, "title": {"titleMenuEnabled": true, "fontColorEnabled": true, "titleFontColor": "#005880", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "3E92C-AD3A-8D", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "displayMenu": false, "custom": {"barcolumnchart": {"type": "indicator", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "disallowSelector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655464099a11833ea60b57", "script": "dashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') \n.css('background-color','#ffffff')\n\n$('.dashboard-layout-subcell-vertical-divider') \n.css('background-color','#ffffff')\n\n})", "realTimeRefreshing": false, "lastOpened": null}, {"title": "State Obligations of Federal-Aid Highway and Highway Safety Construction Funds", "type": "tablewidget", "subtype": "tablewidget", "oid": "68655464099a11833ea60b5d", "desc": null, "source": "659d7e6fde67d0004306bd8a", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "columns", "items": [{"jaql": {"table": "obligations_state.csv", "column": "stname", "dim": "[obligations_state.csv.stname]", "datatype": "text", "merged": true, "title": "State", "sort": "asc"}, "field": {"id": "[obligations_state.csv.stname]", "index": 0}, "instanceid": "FD6E7-9C8C-35"}, {"jaql": {"table": "obligations_state.csv", "column": "column1", "dim": "[obligations_state.csv.column1]", "datatype": "numeric", "title": "Amount Apportioned or Allocated So Far During FY 2025"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "width": 164}, "field": {"id": "[obligations_state.csv.column1]", "index": 1}, "instanceid": "315D0-1895-66"}, {"jaql": {"table": "obligations_state.csv", "column": "column2", "dim": "[obligations_state.csv.column2]", "datatype": "numeric", "title": "Amount Carried Over from Prior Years"}, "field": {"id": "[obligations_state.csv.column2]", "index": 2}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "width": 125}, "instanceid": "B47A5-CAEA-DA"}, {"jaql": {"table": "obligations_state.csv", "column": "column3", "dim": "[obligations_state.csv.column3]", "datatype": "numeric", "title": "Total Amount Available for Obligation in FY 2025"}, "field": {"id": "[obligations_state.csv.column3]", "index": 3}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": false, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "width": 149}, "instanceid": "23137-B434-B7"}, {"jaql": {"table": "obligations_state.csv", "column": "column4", "dim": "[obligations_state.csv.column4]", "datatype": "numeric", "title": "Year-to-Date Obligations (Formula and Exempt Programs)"}, "field": {"id": "[obligations_state.csv.column4]", "index": 4}, "instanceid": "70F8C-C426-30", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}, {"jaql": {"table": "obligations_state.csv", "column": "column5", "dim": "[obligations_state.csv.column5]", "datatype": "numeric", "title": "Year-to-Date Funds Flexed to Transit and Other Programs"}, "field": {"id": "[obligations_state.csv.column5]", "index": 5}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "width": 152}, "instanceid": "E9B37-9DFF-A9"}, {"jaql": {"table": "obligations_state.csv", "column": "column6", "dim": "[obligations_state.csv.column6]", "datatype": "numeric", "title": "Amount Remaining Subject to Annual Obligation"}, "field": {"id": "[obligations_state.csv.column6]", "index": 6}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}, "instanceid": "67F85-ADB4-0E"}, {"jaql": {"table": "obligations_state.csv", "column": "column7", "dim": "[obligations_state.csv.column7]", "datatype": "numeric", "title": "Amount Re<PERSON>ining from Special Programs"}, "field": {"id": "[obligations_state.csv.column7]", "index": 7}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}, "instanceid": "C6813-2460-5A"}, {"jaql": {"table": "obligations_state.csv", "column": "column8", "dim": "[obligations_state.csv.column8]", "datatype": "numeric", "title": "Total Amount Remaining"}, "field": {"id": "[obligations_state.csv.column8]", "index": 8}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}, "instanceid": "24FC9-4B81-31"}, {"jaql": {"table": "obligations_state.csv", "column": "order", "dim": "[obligations_state.csv.order]", "datatype": "numeric", "title": "order", "sort": "asc", "sortDetails": {"field": 9, "dir": "asc", "measurePath": null, "sortingLastDimension": true, "initialized": true}}, "field": {"id": "[obligations_state.csv.order]", "index": 9}, "disabled": true, "instanceid": "2DF00-3C8B-77"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"borders/all": true, "borders/grid": false, "borders/rows": false, "borders/columns": false, "width/content": false, "width/window": true, "colors/headers": true, "colors/rows": true, "colors/columns": false, "wordwrap/headers": true, "wordwrap/rows": false, "scroll": false, "pageSize": 55, "tableState": {}, "automaticHeight": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "amount_apportioned_or_allocated_so_far_during_fy_2025", "title": "Amount Apportioned or Allocated So Far During FY 2025", "singular": "Amount Apportioned or Allocated So Far During FY 2025", "plural": "Amount Apportioned or Allocated So Far During FY 2025"}, {"id": "amount_carried_over_from_prior_years", "title": "Amount Carried Over from Prior Years", "singular": "Amount Carried Over from Prior Years", "plural": "Amount Carried Over from Prior Years"}, {"id": "total_amount_available_for_obligation_in_fy_2025", "title": "Total Amount Available for Obligation in FY 2025", "singular": "Total Amount Available for Obligation in FY 2025", "plural": "Total Amount Available for Obligation in FY 2025"}, {"id": "year-to-date_obligations_(formula_and_exempt_programs)", "title": "Year-to-Date Obligations (Formula and Exempt Programs)", "singular": "Year-to-Date Obligations (Formula and Exempt Programs)", "plural": "Year-to-Date Obligations (Formula and Exempt Programs)"}, {"id": "year-to-date_funds_flexed_to_transit_and_other_programs", "title": "Year-to-Date Funds Flexed to Transit and Other Programs", "singular": "Year-to-Date Funds Flexed to Transit and Other Programs", "plural": "Year-to-Date Funds Flexed to Transit and Other Programs"}, {"id": "amount_remaining_subject_to_annual_obligation", "title": "Amount Remaining Subject to Annual Obligation", "singular": "Amount Remaining Subject to Annual Obligation", "plural": "Amount Remaining Subject to Annual Obligation"}, {"id": "amount_remaining_from_special_programs", "title": "Amount Re<PERSON>ining from Special Programs", "singular": "Amount Re<PERSON>ining from Special Programs", "plural": "Amount Re<PERSON>ining from Special Programs"}, {"id": "total_amount_remaining", "title": "Total Amount Remaining", "singular": "Total Amount Remaining", "plural": "Total Amount Remaining"}, {"id": "order", "title": "order", "singular": "order", "plural": "order"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "3514B-0D24-75", "displayMenu": false, "currentPage": 0, "options": {"dashboardFiltersMode": "select", "disallowSelector": true, "triggersDomready": true, "selector": false, "dataTypes": {"dimensions": true, "measures": false, "filter": false}, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "tablewidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('domready', function (a, b) {\n            _.each ($('td', element), function (td) {\n                        var $td = $(td);\n                        if (!isNaN(parseFloat($td.text()))) {\n                                    $td.css({ 'text-align': 'right' });\n                        }\n            });\n});\n\nwidget.on(\"ready\", function(w, args) {\nvar $ps1 = $(\".p-grand-total-head,.p-dim-head\", element);\n $ps1.css('font-size' , '14px');\n $ps1.css('font-weight' , 'bold');\n $ps1.css('text-align' , 'center'); \n $ps1.css('vertical-align' , 'middle'); \n});", "prevSortObjects": [], "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "68655464099a11833ea60b5e", "desc": null, "source": "659d7e6fde67d0004306bd8b", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\nnormal\"><font size=\"3\" face=\"Open Sans, serif\"><b style=\"\">FY 2025 Obligations Total $22 Billion YTD</b></font></p><p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\nnormal\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\"><b><br></b></span></font></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\">States obligated a total of $4.2 billion in federal-aid highway construction funds in April 2025, bringing total commitments to $22.1 billion in FY 2025, according to the latest data from the U.S. Department of Transportation\n(DOT).&nbsp; &nbsp;</span></font></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 10pt;\"><br></span></font></p><p class=\"MsoNormal\" style=\"margin-bottom: 0.0001pt; text-align: left; line-height: normal;\"><font face=\"Open Sans, serif\"><span style=\"font-size: 13.3333px;\">Although Congress has approved a FY 2025 spending bill, for this reporting period states continued to operate with access to a portion of their FY 2025 federal aid highway funds.&nbsp;&nbsp;</span></font></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\"><br></span></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">As expected, states obligated 100% of their FY 2024 formula funds, by the end of the federal fiscal year on September 30, 2024.&nbsp; &nbsp;They also obligated funds from exempt programs, including supplemental appropriations bills, emergency funding, and COVID relief.&nbsp; &nbsp; &nbsp;</span></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><br></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">This\nreport covers FHWA data on the obligation of federal-aid highway and highway\nsafety construction funds.&nbsp;</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">&nbsp;&nbsp;</span><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">An\nobligation is when the federal government commits to pay or reimburse the\nstates for the Federal share of the eligible project.&nbsp; In most cases funds are obligated for a\nproject before the State DOT puts the work out to bid, and thus can be a\nleading indicator of market activity.&nbsp;</span></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\"><br></span></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0.0001pt; line-height: normal;\"><span style=\"font-family: &quot;Open Sans&quot;, serif; font-size: 10pt;\">Please note that project details can be lagged by one month from national and state overview data and totals may differ slightly from the overview table since the data is updated at different times.</span></p><div style=\"text-align: left;\">\n\n<p class=\"MsoNormal\" style=\"margin-bottom:0in;margin-bottom:.0001pt;line-height:\nnormal\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, serif;\">&nbsp;</span></p></div><div style=\"text-align: left;\"><span style=\"font-size: 10pt; line-height: 107%; font-family: &quot;Open Sans&quot;, serif;\"><i>June 17, 2025</i></span></div><div style=\"text-align: left;\"><span style=\"font-size: 10pt; line-height: 107%; font-family: &quot;Open Sans&quot;, serif;\"><i><br></i></span></div>", "vAlign": "valign-top", "bgColor": "#FFFFFF", "textAlign": "center"}}, "instanceid": "ED390-C848-2B", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "68655464099a11833ea60b57", "lastOpened": null}, {"title": "Fiscal Year 2025 YTD Obligations", "type": "map/area", "subtype": "areamap/usa", "oid": "68655464099a11833ea60b5f", "desc": null, "source": "659d7e6fde67d0004306bd8c", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "obligations_state.csv", "column": "stname", "dim": "[obligations_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "CA043-43F7-33"}]}, {"name": "color", "items": [{"jaql": {"type": "measure", "formula": "([52080-5D1])", "context": {"[52080-5D1]": {"table": "obligations_state.csv", "column": "column4", "dim": "[obligations_state.csv.column4]", "datatype": "numeric", "title": "Total column4", "agg": "sum"}}, "title": "Obligations"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"rangeMode": "auto", "type": "range", "steps": 9}}, "instanceid": "B1F24-CAFB-62"}]}, {"name": "filters", "items": [{"jaql": {"table": "obligations_state.csv", "column": "stname", "dim": "[obligations_state.csv.stname]", "datatype": "text", "merged": true, "title": "stname", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["US territories & Federal Lands", "Total"]}}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": true}, "instanceid": "510C0-E61A-AF"}]}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "stname", "title": "stname", "singular": "stname", "plural": "stname"}]}, "title": {"titleMenuEnabled": true, "titleAlign": "center", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontSizeEnabled": true, "fontColorEnabled": true, "titleFontColor": "white", "titleFontSize": "16"}, "legend": {"enabled": false, "position": "bottomright"}}, "instanceid": "28792-371A-00", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "prevSortObjects": [], "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "Fiscal Year Obligation of Federal-Aid Highway & Highway Safety Construction Funds", "type": "tablewidget", "subtype": "tablewidget", "oid": "68655464099a11833ea60b60", "desc": null, "source": "659d7e6fde67d0004306bd8d", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "columns", "items": [{"jaql": {"table": "obligations_table.csv", "column": "category", "dim": "[obligations_table.csv.category]", "datatype": "text", "merged": true, "title": "Type of Funds/Obligation"}, "instanceid": "1D14E-A5BD-23"}, {"jaql": {"table": "obligations_table.csv", "column": "column1", "dim": "[obligations_table.csv.column1]", "datatype": "numeric", "title": "Amount Apportioned or Allocated So Far During FY 2025"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}, "instanceid": "1D47F-177B-D7"}, {"jaql": {"table": "obligations_table.csv", "column": "column2", "dim": "[obligations_table.csv.column2]", "datatype": "numeric", "title": "Amout Carried Over From Previous Years"}, "instanceid": "C076C-D870-0B", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}, {"jaql": {"table": "obligations_table.csv", "column": "column3", "dim": "[obligations_table.csv.column3]", "datatype": "numeric", "title": "Total Amount Available for Obligation in FY 2025, To Date"}, "instanceid": "4AAD5-6DA3-C9", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}, {"jaql": {"table": "obligations_table.csv", "column": "column4", "dim": "[obligations_table.csv.column4]", "datatype": "numeric", "title": "Year-to-Date Obligations"}, "instanceid": "F891C-927A-2F", "format": {"mask": {"abbreviations": {"t": false, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}, {"jaql": {"table": "obligations_table.csv", "column": "column5", "dim": "[obligations_table.csv.column5]", "datatype": "numeric", "title": "Remaining Amount Available for Obligation, To Date"}, "instanceid": "1272B-EC80-28", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}, "abbreviateAll": false}}}, {"jaql": {"table": "obligations_table.csv", "column": "column6", "dim": "[obligations_table.csv.column6]", "datatype": "numeric", "title": "Percent of Funds Remaining"}, "format": {"mask": {"decimals": 1, "percent": true}}, "instanceid": "2F279-D811-D3"}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"borders/all": true, "borders/grid": false, "borders/rows": false, "borders/columns": false, "width/content": true, "width/window": false, "colors/headers": true, "colors/rows": true, "colors/columns": false, "wordwrap/headers": true, "wordwrap/rows": true, "scroll": false, "pageSize": 25, "tableState": {"time": 1745512439885, "start": 0, "length": 25, "order": [[0, "asc"]], "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}, "columns": [{"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}], "colResize": {"columns": ["238.297px", "357.391px", "270.875px", "219.547px", "172.891px", "188.969px", "195.766px"], "tableSize": 1643.73}, "childRows": [], "iScrollerTopRow": 0, "iScroller": 0, "headers": [{"title": "Type of Funds/Obligation", "dim": "[obligations_table.csv.category]", "datatype": "text"}, {"title": "Amount Apportioned or Allocated So Far During FY 2025", "dim": "[obligations_table.csv.column1]", "datatype": "numeric"}, {"title": "Amout Carried Over From Previous Years", "dim": "[obligations_table.csv.column2]", "datatype": "numeric"}, {"title": "Total Amount Available for Obligation in FY 2025, To Date", "dim": "[obligations_table.csv.column3]", "datatype": "numeric"}, {"title": "Year-to-Date Obligations", "dim": "[obligations_table.csv.column4]", "datatype": "numeric"}, {"title": "Remaining Amount Available for Obligation, To Date", "dim": "[obligations_table.csv.column5]", "datatype": "numeric"}, {"title": "Percent of Funds Remaining", "dim": "[obligations_table.csv.column6]", "datatype": "numeric"}]}, "automaticHeight": false, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_funds/obligation", "title": "Type of Funds/Obligation", "singular": "Type of Funds/Obligation", "plural": "Type of Funds/Obligation"}, {"id": "amount_apportioned_or_allocated_so_far_during_fy_2025", "title": "Amount Apportioned or Allocated So Far During FY 2025", "singular": "Amount Apportioned or Allocated So Far During FY 2025", "plural": "Amount Apportioned or Allocated So Far During FY 2025"}, {"id": "amout_carried_over_from_previous_years", "title": "Amout Carried Over From Previous Years", "singular": "Amout Carried Over From Previous Years", "plural": "Amout Carried Over From Previous Years"}, {"id": "total_amount_available_for_obligation_in_fy_2025,_to_date", "title": "Total Amount Available for Obligation in FY 2025, To Date", "singular": "Total Amount Available for Obligation in FY 2025, To Date", "plural": "Total Amount Available for Obligation in FY 2025, To Date"}, {"id": "year-to-date_obligations", "title": "Year-to-Date Obligations", "singular": "Year-to-Date Obligations", "plural": "Year-to-Date Obligations"}, {"id": "remaining_amount_available_for_obligation,_to_date", "title": "Remaining Amount Available for Obligation, To Date", "singular": "Remaining Amount Available for Obligation, To Date", "plural": "Remaining Amount Available for Obligation, To Date"}, {"id": "percent_of_funds_remaining", "title": "Percent of Funds Remaining", "singular": "Percent of Funds Remaining", "plural": "Percent of Funds Remaining"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "65949-943D-CE", "displayMenu": false, "currentPage": 0, "options": {"dashboardFiltersMode": "select", "disallowSelector": true, "triggersDomready": true, "selector": false, "dataTypes": {"dimensions": true, "measures": false, "filter": false}, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "tablewidget", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.on('domready', function (a, b) {\n            _.each ($('td', element), function (td) {\n                        var $td = $(td);\n                        if (!isNaN(parseFloat($td.text()))) {\n                                    $td.css({ 'text-align': 'right' });\n                        }\n            });\n});\n\nwidget.on(\"ready\", function(w, args) {\nvar $ps1 = $(\".p-grand-total-head,.p-dim-head\", element);\n $ps1.css('font-size' , '14px');\n $ps1.css('font-weight' , 'bold');\n $ps1.css('text-align' , 'center'); \n $ps1.css('vertical-align' , 'middle'); \n});", "prevSortObjects": [], "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "realTimeRefreshing": false, "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "68655464099a11833ea60b61", "desc": null, "source": "659d7e6fde67d0004306bd8e", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"currentCard": {"script": "", "title": "", "titleStyle": [{"backgroundColor": "", "backgroundImage": "", "display": "none"}], "showCarousel": true, "body": [{"type": "Container", "style": {"background-color": "#246482", "margin": "0%", "min-height": "5em"}, "items": [{"type": "Image", "style": {"width": "100%"}, "spacing": 0, "size": "large", "horizontalAlignment": "center", "url": "https://www.artba.org/wp-content/uploads/2024/03/economist.jpg"}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 12, "medium": 22, "large": 32, "extraLarge": 50}, "fontWeights": {"default": 400, "light": 200, "bold": 800}, "containerStyles": {"default": {"backgroundColor": "#ffffff", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#dcdcdc"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#93c0c0"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": "100%"}, "imageSet": {"imageSize": "large", "maxImageHeight": 200}, "actions": {"color": "", "backgroundColor": "", "maxActions": 5, "spacing": "default", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 5, "medium": 10, "large": 0, "extraLarge": 0, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 0}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 234.297}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "24D7B-1203-53", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true, "triggersDomready": true}, "dashboardid": "68655464099a11833ea60b57", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}, {"title": "Federal-Aid Commitments by Major Type, FY 2022 to Date", "type": "chart/pie", "subtype": "pie/classic", "oid": "68655464099a11833ea60b62", "desc": null, "source": "659d7e6fde67d0004306bd8f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "fhwa_fact.csv", "column": "type_major", "dim": "[fhwa_fact.csv.type_major]", "datatype": "text", "merged": true, "title": "Type of FHWA Funds"}, "instanceid": "5328D-501B-3C", "format": {"members": {"Formula Funds": {"title": "Formula Funds", "sortData": "Formula Funds", "color": "#0e6481", "isHandPickedColor": true}, "U%46S%46 DOT Discretionary &amp; Other Projects": {"title": "U.S. DOT Discretionary &amp; Other Projects", "sortData": "U.S. DOT Discretionary &amp; Other Projects", "color": "#8282b5", "isHandPickedColor": true}, "Emergency Relief": {"title": "Emergency Relief", "sortData": "Emergency Relief", "color": "#6eda55", "isHandPickedColor": true}}}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([49CAC-BA4],[C0A08-A53])", "context": {"[C0A08-A53]": {"table": "Dates", "column": "Date", "dim": "[Dates.Date (Calendar)]", "datatype": "datetime", "merged": true, "level": "years", "title": "Years in Date", "filter": {"level": "years", "explicit": true, "multiSelection": true, "members": ["2024-01-01T00:00:00", "2023-01-01T00:00:00", "2022-01-01T00:00:00"]}, "collapsed": true, "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "[49CAC-BA4]": {"table": "fhwa_fact.csv", "column": "ob", "dim": "[fhwa_fact.csv.ob]", "datatype": "numeric", "title": "Total ob", "agg": "sum"}}, "title": "Type of Funds:"}, "instanceid": "7AA1E-CBB1-63", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}}]}, {"name": "filters", "items": [{"jaql": {"table": "fhwa_projects", "column": "submission_year", "dim": "[fhwa_projects.submission_year]", "datatype": "numeric", "filter": {"explicit": true, "multiSelection": true, "members": ["2022", "2023", "2024", "2025"]}, "collapsed": true, "title": "submission_year", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "instanceid": "158B8-89D1-7A"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "bySlicesCount", "minimalIndependentSlicePercentage": "1", "independentSlicesCount": 7}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": true}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_fhwa_funds", "title": "Type of FHWA Funds", "singular": "Type of FHWA Funds", "plural": "Type of FHWA Funds"}, {"id": "submission_year", "title": "submission_year", "singular": "submission_year", "plural": "submission_year"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "9E14A-627E-57", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "selectorLocked": false}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "lastOpened": null}, {"title": "Federal-Aid Commitments by Program, FY 2022 to Date", "type": "chart/pie", "subtype": "pie/classic", "oid": "68655464099a11833ea60b63", "desc": null, "source": "659d7e6fde67d0004306bd90", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "fhwa_fact.csv", "column": "program_activity_name", "dim": "[fhwa_fact.csv.program_activity_name]", "datatype": "text", "merged": true, "title": "FHWA Program"}, "instanceid": "47D44-AD4D-7A", "format": {"members": {"SURFACE TRANSPORTATION BLOCK GRANT-ELIGIBLE (23 USC 133(B)(1)(A)) FORMULA PROGRAM FUNDS": {"title": "SURFACE TRANSPORTATION BLOCK GRANT-ELIGIBLE (23 USC 133(B)(1)(A)) FORMULA PROGRAM FUNDS", "sortData": "SURFACE TRANSPORTATION BLOCK GRANT-ELIGIBLE (23 USC 133(B)(1)(A)) FORMULA PROGRAM FUNDS", "color": "#0e6481", "isHandPickedColor": true}}}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([8702F-557])", "context": {"[8702F-557]": {"table": "fhwa_fact.csv", "column": "ob", "dim": "[fhwa_fact.csv.ob]", "datatype": "numeric", "title": "Total ob", "agg": "sum"}}, "title": "Value of Obligations"}, "instanceid": "7AA1E-CBB1-63", "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "abbreviateAll": false, "isdefault": true}, "color_bkp": {"colorIndex": 0, "type": "color"}}}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "bySlicesCount", "minimalIndependentSlicePercentage": "1", "independentSlicesCount": "5"}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": true}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "fhwa_program", "title": "FHWA Program", "singular": "FHWA Program", "plural": "FHWA Program"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880"}}, "instanceid": "9E14A-627E-57", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "lastOpened": null}, {"title": "Details for Major Projects Receiving IIJA Funds, FY 2022 to Date", "type": "tablewidget", "subtype": "tablewidget", "oid": "68655464099a11833ea60b64", "desc": null, "source": "659d7e6fde67d0004306bd91", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "columns", "items": [{"jaql": {"table": "fhwa_projects", "column": "state", "dim": "[fhwa_projects.state]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "C890A-7C96-F0"}, {"jaql": {"table": "fhwa_projects", "column": "desc", "dim": "[fhwa_projects.desc]", "datatype": "text", "merged": true, "title": "Project Description"}, "instanceid": "E3ADD-0547-2A"}, {"jaql": {"table": "fhwa_projects", "column": "total_obligation", "dim": "[fhwa_projects.total_obligation]", "datatype": "numeric", "title": "Total Federal Funds Committed", "sort": "desc"}, "instanceid": "0F647-EAEE-8E", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "total_spend", "dim": "[fhwa_projects.total_spend]", "datatype": "numeric", "title": "Total Amount Reimbursed to State DOT"}, "instanceid": "E80C2-325A-E0", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "formula_ob", "dim": "[fhwa_projects.formula_ob]", "datatype": "numeric", "title": "Formula Funds"}, "instanceid": "08082-6397-58", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "bridge_ob", "dim": "[fhwa_projects.bridge_ob]", "datatype": "numeric", "title": "Bridge Formula Funds"}, "instanceid": "71AAD-957F-18", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "disc_ob", "dim": "[fhwa_projects.disc_ob]", "datatype": "numeric", "title": "Discretionary Funds"}, "instanceid": "C095D-8E6C-CD", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "covid_ob", "dim": "[fhwa_projects.covid_ob]", "datatype": "numeric", "title": "COVID Funds"}, "instanceid": "1E24D-C2B3-7D", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "approp_ob", "dim": "[fhwa_projects.approp_ob]", "datatype": "numeric", "title": "Supplemental Approps"}, "instanceid": "9524F-9425-83", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}, {"jaql": {"table": "fhwa_projects", "column": "emerg_ob", "dim": "[fhwa_projects.emerg_ob]", "datatype": "numeric", "title": "Emergency Relief"}, "instanceid": "4A948-9D9B-F4", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$"}}}}]}, {"name": "filters", "items": [{"jaql": {"table": "fhwa_projects", "column": "total_obligation", "dim": "[fhwa_projects.total_obligation]", "datatype": "numeric", "title": "Total Federal Funds Committed", "sort": "desc", "filter": {"fromNotEqual": "1"}, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}, "collapsed": false}, "instanceid": "6BC24-B3A4-20"}]}], "usedFormulasMapping": {}}, "style": {"borders/all": true, "borders/grid": false, "borders/rows": false, "borders/columns": false, "width/content": true, "width/window": false, "colors/headers": true, "colors/rows": true, "colors/columns": false, "wordwrap/headers": true, "wordwrap/rows": true, "scroll": false, "pageSize": 10, "tableState": {"time": 1726172686579, "start": 0, "length": 10, "order": [[0, "asc"]], "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}, "columns": [{"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}, {"visible": true, "search": {"search": "", "smart": true, "regex": false, "caseInsensitive": true}}], "colResize": {"columns": ["106px", "324px", "100.344px", "102.297px", "89.4219px", "79.8125px", "121.594px", "80.9219px", "113.797px", "111.594px"], "tableSize": 1229.78}, "childRows": [], "iScrollerTopRow": 0, "iScroller": 0, "headers": [{"title": "State", "dim": "[fhwa_projects.state]", "datatype": "text"}, {"title": "Project Description", "dim": "[fhwa_projects.desc]", "datatype": "text"}, {"title": "Total Federal Funds Committed", "dim": "[fhwa_projects.total_obligation]", "datatype": "numeric"}, {"title": "Total Amount Reimbursed to State DOT", "dim": "[fhwa_projects.total_spend]", "datatype": "numeric"}, {"title": "Formula Funds", "dim": "[fhwa_projects.formula_ob]", "datatype": "numeric"}, {"title": "Bridge Formula Funds", "dim": "[fhwa_projects.bridge_ob]", "datatype": "numeric"}, {"title": "Discretionary Funds", "dim": "[fhwa_projects.disc_ob]", "datatype": "numeric"}, {"title": "COVID Funds", "dim": "[fhwa_projects.covid_ob]", "datatype": "numeric"}, {"title": "Supplemental Approps", "dim": "[fhwa_projects.approp_ob]", "datatype": "numeric"}, {"title": "Emergency Relief", "dim": "[fhwa_projects.emerg_ob]", "datatype": "numeric"}]}, "automaticHeight": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "project_description", "title": "Project Description", "singular": "Project Description", "plural": "Project Description"}, {"id": "total_federal_funds_committed", "title": "Total Federal Funds Committed", "singular": "Total Federal Funds Committed", "plural": "Total Federal Funds Committed"}, {"id": "total_amount_reimbursed_to_state_dot", "title": "Total Amount Reimbursed to State DOT", "singular": "Total Amount Reimbursed to State DOT", "plural": "Total Amount Reimbursed to State DOT"}, {"id": "formula_funds", "title": "Formula Funds", "singular": "Formula Funds", "plural": "Formula Funds"}, {"id": "bridge_formula_funds", "title": "Bridge Formula Funds", "singular": "Bridge Formula Funds", "plural": "Bridge Formula Funds"}, {"id": "discretionary_funds", "title": "Discretionary Funds", "singular": "Discretionary Funds", "plural": "Discretionary Funds"}, {"id": "covid_funds", "title": "COVID Funds", "singular": "COVID Funds", "plural": "COVID Funds"}, {"id": "supplemental_approps", "title": "Supplemental Approps", "singular": "Supplemental Approps", "plural": "Supplemental Approps"}, {"id": "emergency_relief", "title": "Emergency Relief", "singular": "Emergency Relief", "plural": "Emergency Relief"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "4A703-26CD-29", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "disallowSelector": true, "triggersDomready": true, "selector": false, "dataTypes": {"dimensions": true, "measures": false, "filter": false}, "autoUpdateOnEveryChange": true, "supportsHierarchies": false}, "dashboardid": "68655464099a11833ea60b57", "currentPage": 0, "custom": {"barcolumnchart": {"type": "tablewidget", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "oid": "68655464099a11833ea60b65", "desc": null, "source": "659d7e6fde67d0004306bd92", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [], "usedFormulasMapping": {}}, "style": {"activeTab": "0", "showTitle": false, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "showSeparators": true, "useSelectedBkg": false, "useUnselectedBkg": false}, "instanceid": "783CA-54ED-72", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "autoUpdateOnEveryChange": true}, "dashboardid": "68655464099a11833ea60b57", "script": "/*\n*********************************************************************************\n*                                                                               *\n*                               !WARNING!                                       *\n*                                                                               *\n*       This version of <PERSON><PERSON><PERSON> uses Design Panel to configure the widget.       *\n*               Configuration via widget script is deprecated                   *\n*                                                                               *\n*********************************************************************************\n*/\n/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\nwidget.tabs = [\n  {\n    title: \"Overview\", \n    displayWidgetIds : [\"68655464099a11833ea60b67\", \"662a5c00ea9830003f8938d6\", \"68655464099a11833ea60b60\",\"68655464099a11833ea60b5f\",\"68655464099a11833ea60b5d\",\"6478f75487b81200335f41fb\"], \n    hideWidgetIds : [\"68655464099a11833ea60b68\", \"657744a5c11a4a0033e59d4d\", \"68655464099a11833ea60b63\", \"68655464099a11833ea60b62\", \"68655464099a11833ea60b64\", \"657774c1c11a4a0033e59db1\",\"68655464099a11833ea60b66\"],\n  },\n  { \n    title: \"Project Details\", \n    displayWidgetIds : [\"68655464099a11833ea60b68\", \"657744a5c11a4a0033e59d4d\",\"68655464099a11833ea60b5f\",\"68655464099a11833ea60b63\", \"68655464099a11833ea60b62\", \"68655464099a11833ea60b64\", \"657774c1c11a4a0033e59db1\",\"68655464099a11833ea60b66\"], \n    hideWidgetIds : [\"68655464099a11833ea60b67\", \"662a5c00ea9830003f8938d6\",\"68655464099a11833ea60b60\",\"68655464099a11833ea60b5d\",\"6478f75487b81200335f41fb\"],\n  }\n];\n\n", "custom": {"barcolumnchart": {"type": "WidgetsTabber", "isTypeValid": false}}, "tabs": [{"title": "Overview", "displayWidgetIds": ["662a5c00ea9830003f8938d6", "68655464099a11833ea60b60", "68655464099a11833ea60b5f", "68655464099a11833ea60b5d", "6478f75487b81200335f41fb"], "hideWidgetIds": ["657744a5c11a4a0033e59d4d", "68655464099a11833ea60b63", "68655464099a11833ea60b62", "68655464099a11833ea60b64", "657774c1c11a4a0033e59db1", "68655464099a11833ea60b66"]}, {"title": "Project Details", "displayWidgetIds": ["657744a5c11a4a0033e59d4d", "68655464099a11833ea60b5f", "68655464099a11833ea60b63", "68655464099a11833ea60b62", "68655464099a11833ea60b64", "657774c1c11a4a0033e59db1", "68655464099a11833ea60b66"], "hideWidgetIds": ["662a5c00ea9830003f8938d6", "68655464099a11833ea60b60", "68655464099a11833ea60b5d", "6478f75487b81200335f41fb"]}], "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "lastOpened": null}, {"title": "Total Value of Federal-Aid Highway Obligations (Lagged)", "type": "chart/column", "subtype": "column/classic", "oid": "68655464099a11833ea60b66", "desc": null, "source": "659d7e6fde67d0004306bd93", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "fhwa_projects", "column": "submission_year", "dim": "[fhwa_projects.submission_year]", "datatype": "numeric", "title": "submission_year"}, "instanceid": "DA1FA-ADBC-D5", "format": {}, "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([4CDF2-6E7])", "context": {"[4CDF2-6E7]": {"table": "fhwa_projects", "column": "total_obligation", "dim": "[fhwa_projects.total_obligation]", "datatype": "numeric", "agg": "sum", "title": "Total total_obligation"}}, "title": "Total Value of Federal Aid Commitments"}, "instanceid": "7AA1E-CBB1-63", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}, "color": {"type": "color", "color": "#0e6481", "isHandPickedColor": true}}, "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "fhwa_projects", "column": "submission_year", "dim": "[fhwa_projects.submission_year]", "datatype": "numeric", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2020", "2021"]}}, "collapsed": true, "title": "submission_year", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}}, "instanceid": "CD49B-F8B1-96", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "types": {"count": false, "percentage": false, "relative": false, "totals": false}, "stacked": false, "stackedPercentage": false}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "hideMinMax": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "hideMinMax": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "submission_year", "title": "submission_year", "singular": "submission_year", "plural": "submission_year"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center"}}, "instanceid": "E0D5F-D554-8C", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "Yes"}}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": null, "max": null}}, "dashboardid": "68655464099a11833ea60b57", "lastOpened": null}, {"title": "Fiscal Year 2025 Obligations", "type": "chart/area", "subtype": "area/basic", "oid": "68655464099a11833ea60b67", "desc": null, "source": "659d7e6fde67d0004306bd94", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "x-axis", "items": [{"jaql": {"table": "graph_obytd.csv", "column": "month_year", "dim": "[graph_obytd.csv.month_year (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in month_year"}, "instanceid": "668FE-FBD4-27", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "hierarchies": ["calendar", "calendar - weeks"], "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "graph_obytd.csv", "column": "total_month", "dim": "[graph_obytd.csv.total_month]", "datatype": "numeric", "agg": "sum", "title": "Monthly Obligation"}, "instanceid": "44C4E-095A-BF", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 0, "type": "color"}}, "panel": "measures"}, {"jaql": {"table": "graph_obytd.csv", "column": "total_ytd", "dim": "[graph_obytd.csv.total_ytd]", "datatype": "numeric", "agg": "sum", "title": "Total YTD Obligations"}, "instanceid": "C2083-56B6-0B", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"colorIndex": 1, "type": "color"}}, "panel": "measures"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "graph_obytd.csv", "column": "month_year", "dim": "[graph_obytd.csv.month_year (Calendar)]", "datatype": "datetime", "merged": true, "level": "months", "title": "Months in month_year", "filter": {"level": "months", "explicit": false, "multiSelection": true, "all": true}, "collapsed": true, "datasource": {"title": "ARTBA Economics", "fullname": "LocalHost/ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "address": "LocalHost", "database": "aARTBAIAAaEconomics"}}, "instanceid": "28917-B4A2-8C", "format": {"mask": {"years": "yyyy", "quarters": "yyyy Q", "months": "MM/yyyy", "weeks": "ww yyyy", "days": "shortDate", "minutes": "HH:mm", "seconds": "MM/dd/yyyy HH:mm:ss", "dateAndTime": "MM/dd/yyyy HH:mm", "isdefault": true}}, "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"lineWidth": {"width": "bold"}, "legend": {"enabled": true, "position": "bottom"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "months_in_month_year", "title": "Months in month_year", "singular": "Months in month_year", "plural": "Months in month_year"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "titleAlign": "center", "titleFontColor": "white"}}, "instanceid": "1D3CB-9794-D9", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/area", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": null, "max": null}}, "dashboardid": "68655464099a11833ea60b57", "lastOpened": null}, {"title": "Breakdown of Federal Aid Highway Projects by Type of Spending, FY 2022- FY 2024", "type": "chart/pie", "subtype": "pie/classic", "oid": "68655464099a11833ea60b68", "desc": null, "source": "6634eb68ea9830003f89612f", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "ids": [], "all": false}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "spending_breakdown.csv", "column": "type", "dim": "[spending_breakdown.csv.type]", "datatype": "text", "merged": true, "title": "Type of Spending"}, "instanceid": "DA8D4-9DCD-27", "format": {"members": {}}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "[C8326-C05]", "context": {"[C8326-C05]": {"table": "spending_breakdown.csv", "column": "totalcost", "dim": "[spending_breakdown.csv.totalcost]", "datatype": "numeric", "agg": "sum", "title": "Total totalcost"}}, "title": "Type of Work"}, "instanceid": "95B9D-E86F-E3", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}, "abbreviateAll": false}}}]}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": false, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "bySlicesCount", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": true}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_of_spending", "title": "Type of Spending", "singular": "Type of Spending", "plural": "Type of Spending"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true}}, "instanceid": "A6CD7-D4EF-AD", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "68655464099a11833ea60b57", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\n\nwidget.on(\"beforedatapointtooltip\", function(w, args) {\n    args.template = args.template.replace(/\\$\\d+(\\.\\d{1,2})?/g, \"\");\n});", "lastOpened": null}], "hierarchies": []}