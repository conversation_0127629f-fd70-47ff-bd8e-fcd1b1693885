{"title": "7. State DOT Budgets_SDK", "desc": "", "source": "62bb59139961dc2ea095232a", "type": "dashboard", "style": {"palette": {"name": "Vivid", "colors": ["#00cee6", "#9b9bd7", "#6EDA55", "#fc7570", "#fbb755", "#218A8C"]}}, "layout": {"instanceid": "925FE-36CF-93", "type": "columnar", "columns": [{"width": 100, "pxlWidth": 1264, "index": 0, "cells": [{"subcells": [{"elements": [{"minHeight": 102, "maxHeight": 1024, "minWidth": 128, "maxWidth": 2048, "height": "108px", "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c26"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 64, "maxHeight": 2048, "minWidth": 64, "maxWidth": 2048, "height": "360px", "defaultWidth": 128, "widgetid": "686554d1099a11833ea60c21"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "height": 72, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2c"}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 421.328, "index": 0}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2d", "height": 72}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 421.328, "index": 1}, {"elements": [{"minHeight": 72, "maxHeight": 200, "minWidth": 128, "maxWidth": 200, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2e", "height": 72}], "width": 33.333333333333336, "stretchable": false, "pxlWidth": 421.328, "index": 2}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "488px", "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c22"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 0}, {"elements": [{"minHeight": 68, "maxHeight": 937, "minWidth": 128, "maxWidth": 2048, "height": 412.5, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c25"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "488px", "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c23"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": "488px", "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2f"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c30", "height": "488px"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 0}, {"elements": [{"minHeight": 96, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c29", "height": "488px"}], "width": 50, "stretchable": false, "pxlWidth": 632, "index": 1}]}, {"subcells": [{"elements": [{"minHeight": 32, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "height": 32, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c27"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c28", "height": "1162px", "autoHeight": "1162px"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2a", "height": "1162px", "autoHeight": "1162px"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 128, "maxHeight": 2048, "minWidth": 128, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c2b", "height": "1162px", "autoHeight": "1162px"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}, {"subcells": [{"elements": [{"minHeight": 256, "maxHeight": 2048, "minWidth": 256, "maxWidth": 2048, "defaultWidth": 512, "widgetid": "686554d1099a11833ea60c24", "height": "1162px", "autoHeight": "1162px"}], "width": 100, "stretchable": false, "pxlWidth": 1264, "index": 0}]}]}]}, "original": null, "previewLayout": [], "oid": "686554d1099a11833ea60c20", "dataExploration": false, "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "filters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "isCascading": false, "disabled": false, "instanceid": "67037-4B84-E7"}, {"jaql": {"datatype": "text", "dim": "[dim_state.csv.stname]", "title": "State", "column": "stname", "table": "dim_state.csv", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "isDashboardFilter": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "D577A-AD6E-B0", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "text", "dim": "[budget.type_2]", "title": "type_2", "column": "type_2", "table": "budget", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "EE6BA-3762-AD", "isCascading": false, "disabled": false}, {"jaql": {"datatype": "text", "dim": "[Sheet1.type]", "title": "type", "column": "type", "table": "Sheet1", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "isDashboardFilter": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}}, "instanceid": "64C1B-4634-7A", "isCascading": false, "disabled": false}], "editing": false, "filterToDatasourceMapping": {}, "allowChangeSubscription": false, "isPublic": null, "defaultFilters": [{"jaql": {"datatype": "text", "dim": "[executive_summary.csv.mode]", "title": "Mode (Executive Summary)", "column": "mode", "table": "executive_summary.csv", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "merged": true, "filter": {"explicit": false, "multiSelection": true, "all": true}, "isDashboardFilter": true}, "isCascading": false, "disabled": false, "instanceid": "67037-4B84-E7"}, {"jaql": {"datatype": "text", "dim": "[dim_state.csv.stname]", "title": "State", "column": "stname", "table": "dim_state.csv", "filter": {"explicit": true, "multiSelection": true, "members": ["South Dakota"]}, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true, "isDashboardFilter": true}, "instanceid": "D577A-AD6E-B0", "isCascading": false}], "parentFolder": "68654e16099a11833ea60a10", "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "settings": {"autoUpdateOnFiltersChange": true}, "lastOpened": null, "defaultFilterRelations": null, "filterRelations": [], "widgets": [{"title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "oid": "686554d1099a11833ea60c21", "desc": null, "source": "659830fede67d0004306a7de", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"content": {"html": "<p class=\"MsoNormal\" style=\"text-align: center; margin-bottom: 0.0001pt; line-height: normal;\"><font size=\"5\"><b><span style=\"font-family: Calibri, sans-serif; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-caps: small-caps;\">FY 2023 State Transportation Budgets &amp;\nWork Plans</span></b><br></font></p><p class=\"MsoNormal\" style=\"text-align: center; margin-bottom: 0.0001pt; line-height: normal;\"><br></p><p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">Each year state and local governments make decisions about what\nthey expect to spend on transportation programs through the budget\nprocess.&nbsp; Planned expenditures are based on revenue estimates and what the\ngovernment expects to collect in the coming year from taxes, fees, bond issues\nand the federal government.&nbsp; </span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">&nbsp;</span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">In some states, all transportation budget authority must be\napproved through an annual or biennial legislative process, which culminates in\nappropriations acts. In other states, legislative budgets only tell part of the\nstory since certain recurring funding sources – such as federal formula funding\n- can flow directly to transportation agencies without ongoing legislative\napproval. Some state Departments of Transportation (DOTs) also release separate\ncapital budgets or work plans outlining expected annual or multi-year spending\non transportation infrastructure projects. </span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">&nbsp;</span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">Included in this dashboard are state DOT\nlegislative budgets and selected DOT work plans for all 50 states. The\ncollection of work plan data was prioritized for DOTs that provide annual\nupdates of these programs and/or for states where the legislative budget\nprovides only a limited view of overall funding. It is often useful to\ncross-reference both data sources to obtain a more comprehensive understanding\nof the budget. </span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">&nbsp;</span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">The goal of this data is to provide\ninformation on&nbsp;<u><span style=\"border:none windowtext 1.0pt;mso-border-alt:\nnone windowtext 0in;padding:0in\">expected transportation construction spending\nfor ongoing work</span></u>.&nbsp; The federal revenues in the budget are\nreimbursements to the state for work as it is completed.&nbsp; This will\ninclude early expenditures for new projects recently obligated and awarded by\nthe state using funds from the federal Infrastructure Investment and Jobs Act\n(IIJA).&nbsp; </span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left; margin-bottom: 0in; line-height: normal;\"><span style=\"font-size: 10pt; font-family: &quot;Open Sans&quot;, sans-serif;\">&nbsp;</span></p>\n\n<p class=\"MsoNormal\" style=\"text-align: left;\"><span style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\">The map and toggles below allow the user to\nselect an individual state and budget source to see specific details over time.\nA write-up is provided for each state to provide important summary information\nand specify which budget source is being used as the “default plan” to\ninitially populate the charts.</span></p><p class=\"MsoNormal\" style=\"text-align: left;\"><span style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\"><br></span></p><p class=\"MsoNormal\" style=\"text-align: left;\"><span style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\"></span><span style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\">Before accessing the data, </span><b style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\"><u>we ask all users\nto read through the following disclaimers (<a href=\"https://artba.sisense.com/app/main/dashboards/63fd1789d50e0a003581daac\">click here</a>).</u></b><span style=\"font-family: &quot;Open Sans&quot;, sans-serif; font-size: 10pt; text-align: center;\">&nbsp;</span>&nbsp; &nbsp; &nbsp;<br></p><div style=\"text-align: left;\"><br></div>", "vAlign": "valign-top", "bgColor": "#ffffff", "textAlign": "center"}}, "instanceid": "8CD76-B0A1-A8", "options": {"triggersDomready": true, "hideFromWidgetList": true, "disableExportToCSV": true, "disableExportToImage": true, "toolbarButton": {"css": "add-rich-text", "tooltip": "RICHTEXT_MAIN.TOOLBAR_BUTTON"}, "selector": false, "disallowSelector": true, "disallowWidgetTitle": true, "supportsHierarchies": false, "dashboardFiltersMode": "filter"}, "dashboardid": "686554d1099a11833ea60c20", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": false, "displayToolbarRow": false, "displayHeaderRow": false, "volatile": false, "hideDrilledDashboards": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 3, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true}, "lastOpened": null}, {"title": "", "type": "map/area", "subtype": "areamap/usa", "oid": "686554d1099a11833ea60c22", "desc": null, "source": "659830fede67d0004306a7df", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[Mode.major_mode]", "[vpip.csv.year]"], "all": false, "ids": ["67037-4B84-E7"]}, "panels": [{"name": "geo", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "instanceid": "0B659-1EA9-D1"}]}, {"name": "color", "items": [{"jaql": {"table": "budget", "column": "value", "dim": "[budget.value]", "datatype": "numeric", "agg": "sum", "title": "FY 2023 Spending"}, "instanceid": "3D78D-5386-B7", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "range", "steps": 9, "rangeMode": "auto"}}}]}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "fy", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": false, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "DFE4F-1A2B-EB"}, {"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State", "filter": {"explicit": false, "multiSelection": true, "all": true}, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}, "collapsed": true}, "instanceid": "D28B2-F6DA-C1"}, {"jaql": {"table": "budget", "column": "dashboard_choice", "dim": "[budget.dashboard_choice]", "datatype": "numeric", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["N\\A"]}}, "collapsed": true, "title": "dashboard_choice", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "7E24D-8FBC-B8"}]}], "usedFormulasMapping": {}}, "style": {"narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "fy", "title": "fy", "singular": "fy", "plural": "fy"}, {"id": "dashboard_choice", "title": "dashboard_choice", "singular": "dashboard_choice", "plural": "dashboard_choice"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "titleAlign": "center", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481"}}, "instanceid": "7FEFC-C645-46", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": true, "disallowSelector": false, "triggersDomready": true, "autoUpdateOnEveryChange": true, "supportsHierarchies": false, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "custom": {"barcolumnchart": {"type": "map/area", "isTypeValid": false}}, "prevSortObjects": [], "lastOpened": null}, {"title": "State Transportation Program/Budget Expenditures (Default Plan Selected)", "type": "chart/column", "subtype": "column/classic", "oid": "686554d1099a11833ea60c23", "desc": null, "source": "659830fede67d0004306a7e0", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": ["F4716-EE58-2D", "67037-4B84-E7"]}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "fy"}, "instanceid": "140B7-A397-A1"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "[0A0A9-B12]", "context": {"[0A0A9-B12]": {"table": "budget", "column": "value", "dim": "[budget.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}}, "title": "Total Program Expenditures"}, "format": {"color": {"type": "color", "color": "#d39a47", "isHandPickedColor": true}, "mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}}, "instanceid": "A83D1-CE1A-EF"}, {"jaql": {"type": "measure", "formula": "[0EB1B-0D7]", "context": {"[0EB1B-0D7]": {"table": "budget", "column": "hwy_const", "dim": "[budget.hwy_const]", "datatype": "numeric", "agg": "sum", "title": "Total hwy_const"}}, "title": "Highway & Bridge Capital Spending"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#0e6481", "isHandPickedColor": true}}, "singleSeriesType": "line", "instanceid": "7C51F-794F-E6"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024", "2025", "2026", "2027"]}}, "collapsed": true, "title": "fy", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "ACDE0-0967-5C"}, {"jaql": {"table": "budget", "column": "dashboard_choice", "dim": "[budget.dashboard_choice]", "datatype": "numeric", "filter": {"explicit": true, "multiSelection": true, "members": ["1"]}, "collapsed": true, "title": "dashboard_choice", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "1F3A5-E190-2B"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "fy", "title": "fy", "singular": "fy", "plural": "fy"}, {"id": "dashboard_choice", "title": "dashboard_choice", "singular": "dashboard_choice", "plural": "dashboard_choice"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "titleAlign": "center", "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481"}}, "instanceid": "15397-AE80-33", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('processresult', function(ev, args){ \n\targs.result.plotOptions.series.dataLabels.style.fontWeight = \"bold\";\n});", "prevSortObjects": [], "lastOpened": null}, {"title": "Transportation Budget or Work Program Details", "type": "pivot2", "subtype": "pivot", "oid": "686554d1099a11833ea60c24", "desc": null, "source": "659830fede67d0004306a7e1", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "field": {"id": "[dim_state.csv.stname]", "index": 0}, "format": {"subtotal": false}, "instanceid": "D2B87-B4DC-75", "panel": "rows"}, {"jaql": {"table": "budget", "column": "plan", "dim": "[budget.plan]", "datatype": "text", "merged": true, "title": "Type of Plan"}, "field": {"id": "[budget.plan]", "index": 1}, "format": {"subtotal": true}, "instanceid": "ABA5D-D0AD-FD", "panel": "rows"}, {"jaql": {"table": "budget", "column": "category", "dim": "[budget.category]", "datatype": "text", "merged": true, "title": "Program"}, "field": {"id": "[budget.category]", "index": 2}, "instanceid": "B79B2-802B-5C", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "budget", "column": "value", "dim": "[budget.value]", "datatype": "numeric", "agg": "sum", "title": "Transportation Budget or Work Program Details", "sort": "desc", "sortDetails": {"field": 4, "dir": "desc", "sortingLastDimension": "sum", "measurePath": {"3": "2023"}, "isLastApplied": true, "initialized": true}}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "[budget.value]_sum", "index": 4}, "instanceid": "3D0DB-64D9-A6", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "Fiscal Year"}, "field": {"id": "[budget.fy]", "index": 3}, "instanceid": "20F63-57F0-A4", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "Fiscal Year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024", "2025", "2026", "2027"]}}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "30FB8-E1BA-C5", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 40, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "type_of_plan", "title": "Type of Plan", "singular": "Type of Plan", "plural": "Type of Plan"}, {"id": "program", "title": "Program", "singular": "Program", "plural": "Program"}, {"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}, "columnsGrandTotal": false, "rowsGrandTotal": false, "scroll": false}, "instanceid": "B8D8A-C65F-59", "displayMenu": false, "currentPage": 0, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\n// remove export to csv in the widget editing window \nwidget.on('beforewidgetmenu', function(e,args){ \nargs.items.shift();\n\n}) \n// remove export to csv in the dashboard view mode \nwidget.on('beforewidgetindashboardmenu', function(e,args){ \nargs.items.shift() \n})", "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "686554d1099a11833ea60c25", "desc": null, "source": "659830fede67d0004306a7e2", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": [{"jaql": {"table": "budget", "column": "state", "dim": "[budget.state]", "datatype": "text", "merged": true, "title": "state"}, "PanelName": "state", "instanceid": "CC109-DE7B-F8"}, {"jaql": {"table": "state_analysis", "column": "description", "dim": "[state_analysis.description]", "datatype": "text", "merged": true, "title": "description"}, "PanelName": "description", "instanceid": "3E13F-4F82-0D"}, {"jaql": {"table": "state_analysis", "column": "type", "dim": "[state_analysis.type]", "datatype": "text", "merged": true, "title": "type"}, "instanceid": "F8831-9850-F1", "PanelName": "type"}]}, {"name": "Values", "items": []}, {"name": "filters", "items": []}], "usedFormulasMapping": {}}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "display": "none", "carouselAnimation": {"delay": 0}, "body": [{"spacing": "none", "type": "Container", "items": [{"spacing": "small", "type": "TextBlock", "text": "{panel:state}", "weight": "default", "size": "default", "color": "grey", "horizontalAlignment": "center"}, {"spacing": "small", "type": "TextBlock", "text": "Default Plan: {panel:type}", "weight": "default", "size": "small", "color": "grey", "horizontalAlignment": "center"}, {"spacing": "small", "type": "TextBlock", "class": "condition_data", "text": "{panel:description}", "wrap": true, "horizontalAlignment": "left", "size": "small", "weight": "default", "color": "grey"}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 16, "small": 13, "medium": 22, "large": 32, "extraLarge": 50}, "fontWeights": {"default": 500, "light": 100, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "none", "foregroundColors": {"default": {"normal": "#ffffff"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5b6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "medium", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "white", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 50}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 30}, "supportsInteractivity": true, "imageBaseUrl": "", "height": 412.5}, "currentCardName": "Conditional Card", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "description", "title": "description", "singular": "description", "plural": "description"}, {"id": "state", "title": "state", "singular": "state", "plural": "state"}, {"id": "type", "title": "type", "singular": "type", "plural": "type"}]}}, "instanceid": "C5CFE-37D5-E8", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true, "triggersDomready": true}, "dashboardid": "686554d1099a11833ea60c20", "displayMenu": false, "custom": {"barcolumnchart": {"type": "BloX", "isTypeValid": false}}, "lastOpened": null}, {"title": "", "type": "BloX", "subtype": "BloX", "oid": "686554d1099a11833ea60c26", "desc": null, "source": "659830fede67d0004306a7e3", "datasource": {"address": "LocalHost", "title": "ARTBA Economics", "id": "aLOCALHOST_aARTBAIAAaECONOMICS", "database": "aARTBAIAAaEconomics", "fullname": "LocalHost/ARTBA Economics"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "Items", "items": []}, {"name": "Values", "items": []}, {"name": "filters", "items": []}]}, "style": {"currentCard": {"style": "", "script": "", "title": "", "showCarousel": true, "body": [{"type": "Container", "items": [{"type": "Container", "spacing": "none", "items": [{"type": "Image", "url": "https://transportationinvestment.org/wp-content/uploads/2019/11/tiac-dashboard.png", "size": "auto", "spacing": "none", "style": {"width": "100%", "height": "140%", "margin": 0, "padding": 0}}]}]}]}, "currentConfig": {"fontFamily": "Open Sans", "fontSizes": {"default": 14, "small": 16, "medium": 20, "large": 50, "extraLarge": 32}, "fontWeights": {"default": 500, "light": 100, "bold": 1000}, "containerStyles": {"default": {"backgroundColor": "#ffcb05", "foregroundColors": {"default": {"normal": "#000000"}, "white": {"normal": "#ffffff"}, "grey": {"normal": "#5C6372"}, "orange": {"normal": "#f2B900"}, "yellow": {"normal": "#ffcb05"}, "black": {"normal": "#000000"}, "lightGreen": {"normal": "#3ADCCA"}, "green": {"normal": "#54a254"}, "red": {"normal": "#dd1111"}, "accent": {"normal": "#2E89FC"}, "good": {"normal": "#54a254"}, "warning": {"normal": "#e69500"}, "attention": {"normal": "#cc3300"}}}}, "imageSizes": {"default": 40, "small": 40, "medium": 80, "large": 160}, "imageSet": {"imageSize": "large", "maxImageHeight": 100}, "actions": {"color": "", "backgroundColor": "white", "maxActions": 5, "spacing": "extraLarge", "buttonSpacing": 20, "actionsOrientation": "horizontal", "actionAlignment": "center", "showCard": {"actionMode": "inline", "inlineTopMargin": 16, "style": "default"}}, "spacing": {"default": 5, "small": 20, "medium": 60, "large": 20, "extraLarge": 40, "padding": 0}, "separator": {"lineThickness": 1, "lineColor": "#eeeeee"}, "factSet": {"title": {"size": "default", "color": "default", "weight": "bold", "warp": true}, "value": {"size": "default", "color": "default", "weight": "default", "warp": true}, "spacing": 20}, "supportsInteractivity": true, "imageBaseUrl": ""}, "currentCardName": "default", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}}, "instanceid": "96035-7EC1-C9", "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 4, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "drilledDashboardDisplay": {}, "options": {"dashboardFiltersMode": "select", "selector": true, "title": false, "drillTarget": "dummy", "autoUpdateOnEveryChange": true}, "dashboardid": "686554d1099a11833ea60c20", "lastOpened": null}, {"title": "Details of State Budgets, Workplans and Revenues", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "oid": "686554d1099a11833ea60c27", "desc": null, "source": "659830fede67d0004306a7e4", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": []}, "style": {"activeTab": "0", "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": []}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true, "titleAlign": "center"}, "showTitle": false, "showSeparators": true, "useSelectedBkg": false, "useUnselectedBkg": false}, "instanceid": "CC5DA-35C0-C0", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "autoUpdateOnEveryChange": true}, "dashboardid": "686554d1099a11833ea60c20", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwidget.tabs = [\n  {\n    title: \"State Budget/Workplan Spending\", \n    displayWidgetIds : [\"686554d1099a11833ea60c24\"], \n    hideWidgetIds : [\"686554d1099a11833ea60c28\",\"686554d1099a11833ea60c2a\", \"686554d1099a11833ea60c2b\"],\n  },\n  { \n    title: \"Highway & Bridge Capital Spending\", \n    displayWidgetIds : [\"686554d1099a11833ea60c28\"], \n    hideWidgetIds : [\"686554d1099a11833ea60c24\", \"686554d1099a11833ea60c2a\", \"686554d1099a11833ea60c2b\"]\n},\n\t  { \n    title: \"State Budget/Workplan Revenues\", \n    displayWidgetIds : [\"686554d1099a11833ea60c2a\"], \n    hideWidgetIds : [\"686554d1099a11833ea60c24\", \"686554d1099a11833ea60c28\", \"686554d1099a11833ea60c2b\"]\n},\n\t  { \n    title: \"Federal Revenues\", \n    displayWidgetIds : [\"686554d1099a11833ea60c2b\"], \n    hideWidgetIds : [\"686554d1099a11833ea60c24\", \"686554d1099a11833ea60c28\", \"686554d1099a11833ea60c2a\"]\n}\n];\n", "custom": {"barcolumnchart": {"type": "WidgetsTabber", "isTypeValid": false}}, "tabs": [{"title": "State Budget/Workplan Spending", "displayWidgetIds": ["686554d1099a11833ea60c24"], "hideWidgetIds": ["686554d1099a11833ea60c28", "686554d1099a11833ea60c2a", "686554d1099a11833ea60c2b"]}, {"title": "Highway & Bridge Capital Spending", "displayWidgetIds": ["686554d1099a11833ea60c28"], "hideWidgetIds": ["686554d1099a11833ea60c24", "686554d1099a11833ea60c2a", "686554d1099a11833ea60c2b"]}, {"title": "State Budget/Workplan Revenues", "displayWidgetIds": ["686554d1099a11833ea60c2a"], "hideWidgetIds": ["686554d1099a11833ea60c24", "686554d1099a11833ea60c28", "686554d1099a11833ea60c2b"]}, {"title": "Federal Revenues", "displayWidgetIds": ["686554d1099a11833ea60c2b"], "hideWidgetIds": ["686554d1099a11833ea60c24", "686554d1099a11833ea60c28", "686554d1099a11833ea60c2a"]}], "lastOpened": null}, {"title": "Select Items - Highway & Bridge Capital Spending", "type": "pivot2", "subtype": "pivot", "oid": "686554d1099a11833ea60c28", "desc": null, "source": "659830fede67d0004306a7e5", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "field": {"id": "[dim_state.csv.stname]", "index": 0}, "format": {"subtotal": false}, "instanceid": "10C5A-AB41-8F", "panel": "rows"}, {"jaql": {"table": "budget", "column": "plan", "dim": "[budget.plan]", "datatype": "text", "merged": true, "title": "Type of Plan"}, "field": {"id": "[budget.plan]", "index": 1}, "format": {"subtotal": true}, "instanceid": "24B38-65AE-88", "panel": "rows"}, {"jaql": {"table": "budget", "column": "category", "dim": "[budget.category]", "datatype": "text", "merged": true, "title": "Program"}, "field": {"id": "[budget.category]", "index": 2}, "instanceid": "B3B82-50D5-3D", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "sum([C86CA-0C2])", "context": {"[C86CA-0C2]": {"table": "budget", "column": "hwy_const", "dim": "[budget.hwy_const]", "datatype": "numeric", "agg": "sum", "title": "Total hwy_const"}}, "title": "Highway & Bridge Capital Spending", "datatype": "numeric"}, "format": {"mask": {"type": "number", "t": true, "b": true, "separated": true, "decimals": "auto", "isdefault": true}, "color": {"type": "color"}}, "field": {"id": "sum([C86CA-0C2])", "index": 4}, "instanceid": "01417-42AB-CF", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "Fiscal Year"}, "field": {"id": "[budget.fy]", "index": 3}, "instanceid": "8AA7F-82DF-E1", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "Fiscal Year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024", "2025", "2026", "2027"]}}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "0AD83-26A8-20", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 40, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "type_of_plan", "title": "Type of Plan", "singular": "Type of Plan", "plural": "Type of Plan"}, {"id": "program", "title": "Program", "singular": "Program", "plural": "Program"}, {"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}, "columnsGrandTotal": false, "rowsGrandTotal": false, "scroll": false}, "instanceid": "B8D8A-C65F-59", "displayMenu": false, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\n// remove export to csv in the widget editing window \nwidget.on('beforewidgetmenu', function(e,args){ \nargs.items.shift();\n\n}) \n// remove export to csv in the dashboard view mode \nwidget.on('beforewidgetindashboardmenu', function(e,args){ \nargs.items.shift() \n})", "lastOpened": null}, {"title": "State Transportation Program Revenues (Default Plan Selected)", "type": "chart/column", "subtype": "column/classic", "oid": "686554d1099a11833ea60c29", "desc": null, "source": "659830fede67d0004306a7e6", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": ["[executive_summary.csv.mode]"], "all": false, "ids": ["F4716-EE58-2D"]}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year"}, "instanceid": "AC958-4DCA-04"}]}, {"name": "values", "items": [{"jaql": {"table": "Sheet1", "column": "value", "dim": "[Sheet1.value]", "datatype": "numeric", "agg": "sum", "title": "Total (or selected) Revenues"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#5ca793", "isHandPickedColor": true}}, "instanceid": "AA874-35DD-8F"}, {"jaql": {"table": "Sheet1", "column": "federal", "dim": "[Sheet1.federal]", "datatype": "numeric", "agg": "sum", "title": "Federal Reimbursements"}, "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color", "color": "#005880", "isHandPickedColor": true}}, "singleSeriesType": "line", "instanceid": "D2EF9-7969-8B"}]}, {"name": "break by", "items": []}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "fy", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2025", "2026", "2027", "N\\A"]}}, "collapsed": false, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "E0A63-3C3C-6C"}, {"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024", "2025"]}}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "1EEF6-01F1-62"}, {"jaql": {"table": "Sheet1", "column": "dashboard_choice", "dim": "[Sheet1.dashboard_choice]", "datatype": "numeric", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["N\\A"]}}, "collapsed": true, "title": "dashboard_choice", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "0B26F-76DE-C2"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "seriesLabels": {"enabled": true, "rotation": 0, "labels": {"enabled": false, "stacked": false, "stackedPercentage": false, "types": {"count": false, "relative": false, "percentage": false, "totals": false}}}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": false, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": true, "hideMinMax": false}, "navigator": {"enabled": true}, "dataLimits": {"seriesCapacity": 50, "categoriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}, {"id": "fy", "title": "fy", "singular": "fy", "plural": "fy"}, {"id": "dashboard_choice", "title": "dashboard_choice", "singular": "dashboard_choice", "plural": "dashboard_choice"}]}, "title": {"titleMenuEnabled": true, "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "titleAlign": "center", "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481"}}, "instanceid": "15397-AE80-33", "displayMenu": true, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/column", "isTypeValid": true, "customMenuEnabled": false, "addTotalOption": "No"}}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true, "previousScrollerLocation": {"min": null, "max": null}, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "prevSortObjects": [], "lastOpened": null}, {"title": "Revenue Details", "type": "pivot2", "subtype": "pivot", "oid": "686554d1099a11833ea60c2a", "desc": null, "source": "659830fede67d0004306a7e7", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "field": {"id": "[dim_state.csv.stname]", "index": 0}, "format": {"subtotal": false}, "instanceid": "978F7-3BD6-17", "panel": "rows"}, {"jaql": {"table": "Sheet1", "column": "plan", "dim": "[Sheet1.plan]", "datatype": "text", "merged": true, "title": "Type of Plan"}, "field": {"id": "[Sheet1.plan]", "index": 1}, "format": {"subtotal": true}, "instanceid": "DFF81-D972-45", "panel": "rows"}, {"jaql": {"table": "Sheet1", "column": "source", "dim": "[Sheet1.source]", "datatype": "text", "merged": true, "title": "Revenue Source"}, "field": {"id": "[Sheet1.source]", "index": 2}, "instanceid": "06590-8483-6C", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "Sheet1", "column": "value", "dim": "[Sheet1.value]", "datatype": "numeric", "agg": "sum", "title": "Program Revenues"}, "format": {"mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": "auto", "currency": {"symbol": "$", "position": "pre"}}, "color": {"type": "color"}}, "field": {"id": "[Sheet1.value]_sum", "index": 4}, "instanceid": "101A6-3F1D-BE", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year"}, "field": {"id": "[Sheet1.fy]", "index": 3}, "instanceid": "C756D-F245-92", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024"]}}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "C1F47-3856-A1", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 40, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "type_of_plan", "title": "Type of Plan", "singular": "Type of Plan", "plural": "Type of Plan"}, {"id": "revenue_source", "title": "Revenue Source", "singular": "Revenue Source", "plural": "Revenue Source"}, {"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}, "columnsGrandTotal": false, "rowsGrandTotal": false, "scroll": false}, "instanceid": "B8D8A-C65F-59", "displayMenu": false, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\n// remove export to csv in the widget editing window \nwidget.on('beforewidgetmenu', function(e,args){ \nargs.items.shift();\n\n}) \n// remove export to csv in the dashboard view mode \nwidget.on('beforewidgetindashboardmenu', function(e,args){ \nargs.items.shift() \n})", "lastOpened": null}, {"title": "Federal Reimbursements", "type": "pivot2", "subtype": "pivot", "oid": "686554d1099a11833ea60c2b", "desc": null, "source": "659830fede67d0004306a7e8", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "rows", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "State"}, "field": {"id": "[dim_state.csv.stname]", "index": 0}, "format": {"subtotal": false}, "instanceid": "ED753-4792-CD", "panel": "rows"}, {"jaql": {"table": "Sheet1", "column": "plan", "dim": "[Sheet1.plan]", "datatype": "text", "merged": true, "title": "Type of Plan"}, "field": {"id": "[Sheet1.plan]", "index": 1}, "instanceid": "B128E-BA31-5A", "panel": "rows"}, {"jaql": {"table": "Sheet1", "column": "source", "dim": "[Sheet1.source]", "datatype": "text", "merged": true, "title": "Program Revenues"}, "field": {"id": "[Sheet1.source]", "index": 2}, "instanceid": "6A528-3D10-9D", "panel": "rows"}]}, {"name": "values", "items": [{"jaql": {"table": "Sheet1", "column": "federal", "dim": "[Sheet1.federal]", "datatype": "numeric", "agg": "sum", "title": "Total federal", "subtotalAgg": "sum"}, "format": {"color": {"type": "color"}, "mask": {"abbreviations": {"t": false, "b": false, "m": false, "k": false}, "decimals": 0, "currency": {"symbol": "$", "position": "pre"}}}, "field": {"id": "[Sheet1.federal]_sum", "index": 4}, "instanceid": "DEE76-F2BA-39", "panel": "measures"}]}, {"name": "columns", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year"}, "field": {"id": "[Sheet1.fy]", "index": 3}, "instanceid": "D9498-9C12-3E", "panel": "columns"}]}, {"name": "filters", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "Fiscal Year", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["2024"]}}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "A1AC0-262B-B7", "panel": "scope"}]}], "usedFormulasMapping": {}}, "style": {"pageSize": 40, "automaticHeight": true, "colors": {"rows": true, "columns": false, "headers": false, "members": false, "totals": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "state", "title": "State", "singular": "State", "plural": "State"}, {"id": "type_of_plan", "title": "Type of Plan", "singular": "Type of Plan", "plural": "Type of Plan"}, {"id": "program_revenues", "title": "Program Revenues", "singular": "Program Revenues", "plural": "Program Revenues"}, {"id": "fiscal_year", "title": "Fiscal Year", "singular": "Fiscal Year", "plural": "Fiscal Year"}]}, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#0e6481", "fontFamilyEnabled": true, "titleFontFamily": "Open Sans", "fontSizeEnabled": true, "titleFontSize": "16", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "titleAlign": "center"}, "columnsGrandTotal": false, "rowsGrandTotal": false, "scroll": false}, "instanceid": "B8D8A-C65F-59", "displayMenu": false, "custom": {"barcolumnchart": {"type": "pivot2", "isTypeValid": false}}, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 2, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "filter", "selector": false, "triggersDomready": true, "drillToAnywhere": true, "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://sisense.dev/guides/js/extensions\n*/\n\n// remove export to csv in the widget editing window \nwidget.on('beforewidgetmenu', function(e,args){ \nargs.items.shift();\n\n}) \n// remove export to csv in the dashboard view mode \nwidget.on('beforewidgetindashboardmenu', function(e,args){ \nargs.items.shift() \n})", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554d1099a11833ea60c2c", "desc": null, "source": "659830fede67d0004306a7e9", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "dim_state.csv", "column": "stname", "dim": "[dim_state.csv.stname]", "datatype": "text", "merged": true, "title": "Select a State"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "select_a_state", "title": "Select a State", "singular": "Select a State", "plural": "Select a State"}]}}, "instanceid": "1C57D-1C15-2E", "displayMenu": false, "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554d1099a11833ea60c2d", "desc": null, "source": "659830fede67d0004306a7ea", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "budget", "column": "plan", "dim": "[budget.plan]", "datatype": "text", "merged": true, "title": "Select a Spending Plan"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "select_a_spending_plan", "title": "Select a Spending Plan", "singular": "Select a Spending Plan", "plural": "Select a Spending Plan"}]}}, "instanceid": "1C57D-1C15-2E", "displayMenu": false, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true, "selectorLocked": false}, "dashboardid": "686554d1099a11833ea60c20", "lastOpened": null}, {"title": "", "type": "filterWidget", "subtype": "filterWidget", "oid": "686554d1099a11833ea60c2e", "desc": null, "source": "659830fede67d0004306a7eb", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "items", "items": [{"jaql": {"table": "Sheet1", "column": "type", "dim": "[Sheet1.type]", "datatype": "text", "merged": true, "title": "Select a Revenue Source"}}]}, {"name": "sort", "items": []}, {"name": "filters", "items": []}]}, "style": {"isDependantFilter": true, "ignoreDashboardFilter": true, "includeNoSelection": true, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "select_a_revenue_source", "title": "Select a Revenue Source", "singular": "Select a Revenue Source", "plural": "Select a Revenue Source"}]}}, "instanceid": "1C57D-1C15-2E", "displayMenu": false, "script": "/*\nWelcome to your Widget's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\nwidget.on('ready', function(se, ev){\n$('.filter-widget-wrapper', element).css('background-color','#005880');\n$('span', element).css('color','white');\n$('.filter-widget-wrapper:after', element).css('border-color','white transparent');\n})", "custom": {"barcolumnchart": {"type": "filterWidget", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": false, "noSelectionText": "", "autoUpdateOnEveryChange": true}, "dashboardid": "686554d1099a11833ea60c20", "lastOpened": null}, {"title": "FY 2023 Spending by Type for (based on Selected Budget/Workplan)", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554d1099a11833ea60c2f", "desc": null, "source": "659830fede67d0004306a7ec", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "budget", "column": "type_2", "dim": "[budget.type_2]", "datatype": "text", "merged": true, "title": "type_2"}, "format": {"members": {"Capital Spending": {"color": "#005880", "title": "Capital Spending", "sortData": "Capital Spending"}, "Other Spending": {"color": "#d39a47", "title": "Other Spending", "sortData": "Other Spending", "inResultset": true}, "Highway Capital Spending": {"color": "#0e6481", "title": "Highway Capital Spending", "sortData": "Highway Capital Spending", "inResultset": true}}}, "instanceid": "D0290-C189-67"}]}, {"name": "values", "items": [{"jaql": {"table": "budget", "column": "value", "dim": "[budget.value]", "datatype": "numeric", "agg": "sum", "title": "Total value"}, "format": {"mask": {"type": "number", "abbreviations": {"t": true, "b": true, "m": true, "k": true}, "separated": true, "decimals": "auto", "isdefault": true}}, "instanceid": "D8CB9-08FA-1A"}]}, {"name": "filters", "items": [{"jaql": {"table": "budget", "column": "fy", "dim": "[budget.fy]", "datatype": "numeric", "title": "fy", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": false, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "14D76-63C7-06"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": 3, "independentSlicesCount": 7}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type_2", "title": "type_2", "singular": "type_2", "plural": "type_2"}, {"id": "fy", "title": "fy", "singular": "fy", "plural": "fy"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true, "titleAlign": "center"}}, "instanceid": "3387B-8939-55", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "686554d1099a11833ea60c20", "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "prevSortObjects": [], "lastOpened": null}, {"title": "FY 2023 Revenues by Type, (Federal Funds Include GARVEE Bonds)", "type": "chart/pie", "subtype": "pie/classic", "oid": "686554d1099a11833ea60c30", "desc": null, "source": "659830fede67d0004306a7ed", "datasource": {"address": "LocalHost", "title": "State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "database": "aStateXwAaBudgets", "fullname": "LocalHost/State_Budgets"}, "selection": null, "metadata": {"ignore": {"dimensions": [], "all": false, "ids": []}, "panels": [{"name": "categories", "items": [{"jaql": {"table": "Sheet1", "column": "type", "dim": "[Sheet1.type]", "datatype": "text", "merged": true, "title": "type"}, "instanceid": "DB658-C20E-61", "format": {"members": {"Bonds": {"color": "#00cee6", "title": "<PERSON><PERSON>", "sortData": "<PERSON><PERSON>", "inResultset": true}, "Federal Funds": {"color": "#0e6481", "title": "Federal Funds", "sortData": "Federal Funds", "inResultset": true}, "General Funds": {"color": "#6EDA55", "title": "General Funds", "sortData": "General Funds", "inResultset": true}, "Local Contributions": {"color": "#fc7570", "title": "Local Contributions", "sortData": "Local Contributions", "inResultset": true}, "State Funds": {"color": "#d39a47", "title": "State Funds", "sortData": "State Funds", "inResultset": true}, "": {"color": "#218A8C", "title": "", "sortData": ""}}}}]}, {"name": "values", "items": [{"jaql": {"type": "measure", "formula": "([BABAF-6E2],[F3DF2-270])", "context": {"[BABAF-6E2]": {"table": "Sheet1", "column": "value", "dim": "[Sheet1.value]", "datatype": "numeric", "title": "Total value", "agg": "sum"}, "[F3DF2-270]": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "fy", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}}, "title": "Revenues"}, "instanceid": "54E83-46FF-<PERSON><PERSON>", "format": {"mask": {"abbreviations": {"t": true, "b": true, "m": true, "k": true}, "decimals": 1, "currency": {"symbol": "$", "position": "pre"}}}}]}, {"name": "filters", "items": [{"jaql": {"table": "Sheet1", "column": "fy", "dim": "[Sheet1.fy]", "datatype": "numeric", "title": "fy", "filter": {"explicit": true, "multiSelection": true, "members": ["2023"]}, "collapsed": false, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "1F035-8195-9F"}, {"jaql": {"table": "Sheet1", "column": "type", "dim": "[Sheet1.type]", "datatype": "text", "merged": true, "title": "type", "filter": {"explicit": false, "multiSelection": true, "all": true}, "collapsed": true, "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "7A0C8-F446-F5", "title": "type"}, {"jaql": {"table": "Sheet1", "column": "dashboard_choice", "dim": "[Sheet1.dashboard_choice]", "datatype": "numeric", "filter": {"explicit": false, "multiSelection": true, "exclude": {"members": ["N\\A"]}}, "collapsed": true, "title": "dashboard_choice", "datasource": {"title": "State_Budgets", "fullname": "LocalHost/State_Budgets", "id": "aLOCALHOST_aSTATEXwAaBUDGETS", "address": "LocalHost", "database": "aStateXwAaBudgets"}}, "instanceid": "B25E9-F2D9-34"}]}], "usedFormulasMapping": {}}, "style": {"legend": {"enabled": true, "position": "bottom"}, "labels": {"enabled": true, "categories": true, "value": false, "percent": true, "decimals": false, "fontFamily": "Open Sans", "color": "red"}, "convolution": {"enabled": true, "selectedConvolutionType": "byPercentage", "minimalIndependentSlicePercentage": "1", "independentSlicesCount": 7}, "dataLimits": {"seriesCapacity": 100000}, "lineWidth": {"width": "bold"}, "seriesLabels": {"enabled": false, "rotation": 0}, "markers": {"enabled": false, "fill": "filled", "size": "small"}, "xAxis": {"enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "x2Title": {"enabled": false}, "gridLines": true, "isIntervalEnabled": false}, "yAxis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": true, "logarithmic": false, "isIntervalEnabled": false}, "y2Axis": {"inactive": true, "enabled": true, "ticks": true, "labels": {"enabled": true, "rotation": 0}, "title": {"enabled": false}, "gridLines": false, "logarithmic": false, "isIntervalEnabled": false}, "navigator": {"enabled": true}, "narration": {"enabled": false, "display": "above", "format": "bullets", "verbosity": "medium", "up_sentiment": "good", "aggregation": "sum", "labels": [{"id": "type", "title": "type", "singular": "type", "plural": "type"}, {"id": "fy", "title": "fy", "singular": "fy", "plural": "fy"}, {"id": "dashboard_choice", "title": "dashboard_choice", "singular": "dashboard_choice", "plural": "dashboard_choice"}]}, "automaticHeight": false, "title": {"titleMenuEnabled": true, "fontTitleBackgroundEnabled": true, "titleBackground": "#005880", "fontSizeEnabled": true, "titleFontSize": "18", "fontColorEnabled": true, "titleFontColor": "white", "fontWeightEnabled": true, "titleFontWeight": "bold", "fontFamilyEnabled": true, "titleAlign": "center"}}, "instanceid": "3387B-8939-55", "displayMenu": false, "drillToDashboardConfig": {"drilledDashboardPrefix": "_drill", "drilledDashboardsFolderPrefix": "", "displayFilterPane": true, "displayDashboardsPane": true, "displayToolbarRow": true, "displayHeaderRow": true, "volatile": false, "hideDrilledDashboards": true, "hideSharedDashboardsForNonOwner": true, "drillToDashboardMenuCaption": "Jump to dashboard", "drillToDashboardRightMenuCaption": "Jump to ", "drillToDashboardNavigateType": 1, "drillToDashboardNavigateTypePivot": 2, "drillToDashboardNavigateTypeCharts": 1, "drillToDashboardNavigateTypeOthers": 3, "excludeFilterDims": [], "includeFilterDims": [], "drilledDashboardDisplayType": 2, "dashboardIds": [], "modalWindowResize": false, "showFolderNameOnMenuSelection": false, "resetDashFiltersAfterJTD": false, "sameCubeRestriction": true, "showJTDIcon": true, "sendPieChartMeasureFiltersOnClick": true, "forceZeroInsteadNull": false, "mergeTargetDashboardFilters": false, "drillToDashboardByName": false}, "custom": {"barcolumnchart": {"type": "chart/pie", "isTypeValid": false}}, "options": {"dashboardFiltersMode": "select", "selector": true, "triggersDomready": true, "autoUpdateOnEveryChange": true, "drillToAnywhere": true}, "dashboardid": "686554d1099a11833ea60c20", "prevSortObjects": [], "lastOpened": null}], "hierarchies": []}