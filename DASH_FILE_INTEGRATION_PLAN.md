# .dash File Integration Plan

## Overview
The .dash files are Sisense dashboard export files containing complete dashboard configurations including:
- Dashboard metadata (title, description, datasource)
- Layout structure (columns, cells, positioning)
- Widget definitions (types, data queries, styling)
- Data connections and JAQL queries

## File Structure Analysis

### Key Components:
1. **Dashboard Metadata**
   - `title`: Dashboard name
   - `source`: Dashboard ID
   - `type`: "dashboard"
   - `datasource`: Connection details

2. **Layout System**
   - Uses columnar layout with cells and subcells
   - Each widget has positioning info (width, height, min/max dimensions)
   - `widgetid` links to widget definitions

3. **Widget Types Found**
   - `indicator/numeric`: KPI widgets
   - `BloX`: Custom HTML/card widgets
   - `chart/column`: Column charts
   - `chart/pie`: Pie charts
   - `pivot2`: Pivot tables
   - `chart/scatter`: Scatter plots

4. **Data Queries**
   - Uses JAQL (JSON Analytic Query Language)
   - Contains formulas, aggregations, filters
   - References data model dimensions and measures

## Implementation Strategy

### Phase 1: Create Parser and Type System
```typescript
// src/types/dash.types.ts
export interface DashFile {
  title: string;
  desc: string;
  source: string;
  type: 'dashboard';
  datasource: DashDataSource;
  layout: DashLayout;
  widgets: DashWidget[];
  style?: DashStyle;
}

export interface DashWidget {
  oid: string;
  title: string;
  type: string;
  subtype?: string;
  metadata: {
    panels: DashPanel[];
  };
  style?: any;
}

export interface DashPanel {
  name: string;
  items: DashPanelItem[];
}

export interface DashPanelItem {
  jaql: JAQLQuery;
  format?: any;
}
```

### Phase 2: Create Parser Service
```typescript
// src/services/dashParser.service.ts
export class DashParserService {
  static async parseDashFile(filePath: string): Promise<DashFile> {
    const content = await readFile(filePath);
    return JSON.parse(content);
  }
  
  static convertToSisenseConfig(dash: DashFile): DashboardConfig {
    // Convert .dash format to our internal format
  }
}
```

### Phase 3: Widget Mapping System
Map Sisense widget types to our components:
- `indicator/numeric` → `KPIWidget`
- `chart/column`, `chart/bar` → `ChartWidget` (type: "column/bar")
- `chart/pie` → `ChartWidget` (type: "pie")
- `chart/scatter` → `ChartWidget` (type: "scatter")
- `pivot2` → `TableWidget` or new `PivotWidget`
- `BloX` → New `CustomHTMLWidget`

### Phase 4: Data Model Integration
1. Extract datasource information
2. Map JAQL queries to Sisense SDK data options
3. Handle formulas and calculated measures
4. Convert filters to SDK format

### Phase 5: Create Dashboard Importer
```typescript
// src/pages/DashboardImporter/DashboardImporter.tsx
export const DashboardImporter: React.FC = () => {
  const importDashboard = async (dashFile: File) => {
    const dash = await DashParserService.parseDashFile(dashFile);
    const config = DashParserService.convertToSisenseConfig(dash);
    // Create dashboard from config
  };
}
```

## File Organization

```
src/
├── dashboards/
│   ├── imported/          # Store imported dashboard configs
│   │   ├── summary-dashboard.config.ts
│   │   ├── contract-awards.config.ts
│   │   └── ...
│   └── templates/         # Original .dash files for reference
├── services/
│   ├── dashParser.service.ts
│   ├── jaqlConverter.service.ts
│   └── widgetMapper.service.ts
├── types/
│   ├── dash.types.ts
│   └── jaql.types.ts
└── utils/
    └── dashboardConverter.ts
```

## Implementation Steps

### Step 1: Basic Parser (Week 1)
- [ ] Create type definitions for .dash structure
- [ ] Build JSON parser for .dash files
- [ ] Create basic dashboard config converter

### Step 2: Widget Mapping (Week 1-2)
- [ ] Map indicator widgets to KPIWidget
- [ ] Map chart widgets to ChartWidget
- [ ] Create BloX widget component for custom HTML
- [ ] Handle pivot table widgets

### Step 3: Data Integration (Week 2-3)
- [ ] Parse JAQL queries
- [ ] Convert to Sisense SDK data options
- [ ] Handle calculated measures and formulas
- [ ] Map filters and date ranges

### Step 4: Layout System (Week 3)
- [ ] Convert columnar layout to grid system
- [ ] Handle responsive design
- [ ] Preserve widget positioning

### Step 5: Import UI (Week 4)
- [ ] Create import dashboard page
- [ ] File upload interface
- [ ] Preview before import
- [ ] Save imported dashboards

## Benefits of This Approach

1. **Reuse Existing Dashboards**: Import all 8 existing dashboards
2. **Maintain Consistency**: Keep the same metrics and calculations
3. **Faster Development**: No need to recreate from scratch
4. **Easy Migration**: Smooth transition from old to new system

## Next Actions

1. Start with parsing the smallest file (6StateLegislativeInitiatives.dash)
2. Create type definitions based on actual file structure
3. Build incremental parser focusing on one widget type at a time
4. Test with each dashboard file to ensure compatibility