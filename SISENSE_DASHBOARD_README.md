# Sisense Dashboard Project

A scalable, modular dashboard application built with React, TypeScript, and Sisense Compose SDK.

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ 
- Sisense instance (Linux version L2022.10 or later)
- npm or yarn

### Installation

```bash
npm install
```

### Development

```bash
npm run dev
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Shared components (Header, Footer, etc.)
│   ├── layout/         # Layout components (PageLayout, DashboardLayout)
│   └── widgets/        # Widget templates (ChartWidget, KPIWidget, TableWidget)
├── pages/              # Page components
│   ├── Landing/        # Landing page with dashboard navigation
│   └── dashboards/     # Individual dashboard pages
├── config/             # Configuration files
│   ├── sisense.config.ts    # Sisense connection settings
│   └── theme.config.ts      # UI theme configuration
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── styles/             # Global styles
```

## 🎨 Key Features

### 1. Modular Widget System
- **ChartWidget**: Wrapper for Sisense ChartWidget with consistent styling
- **KPIWidget**: Display key metrics with trends
- **TableWidget**: Data tables with pagination
- **WidgetContainer**: Consistent container for all widgets

### 2. Dashboard Configuration
Each dashboard has a configuration file that defines:
- Widget layout and positioning
- Data sources and options
- Widget types and properties

Example:
```typescript
export const dashboardConfig = {
  widgets: [
    {
      id: 'revenue-chart',
      type: 'chart',
      position: { x: 0, y: 0, w: 6, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [DM.Commerce.Date],
          value: [measureFactory.sum(DM.Commerce.Revenue)]
        }
      }
    }
  ]
}
```

### 3. Consistent Theme
- Centralized color palette
- Spacing system
- Shadow definitions
- Responsive breakpoints

## 🔧 Configuration

### Sisense Connection
Update `src/config/sisense.config.ts`:
```typescript
export const sisenseConfig = {
  url: 'https://your-sisense-instance.com',
  defaultDataSource: 'Your Data Model Name'
}
```

### Data Model
Define your data model in the config file:
```typescript
export const DM = {
  YourModel: {
    Field1: '[Model.Field1]',
    Field2: '[Model.Field2]'
  }
}
```

## 📊 Creating a New Dashboard

1. Create a new folder in `src/pages/dashboards/YourDashboard/`

2. Create a configuration file:
```typescript
// YourDashboard.config.ts
export const yourDashboardConfig = {
  id: 'your-dashboard',
  title: 'Your Dashboard',
  widgets: [
    // Define your widgets here
  ]
}
```

3. Create the dashboard component:
```typescript
// YourDashboard.tsx
import { createWidget } from '../../../utils/widgetFactory';

export const YourDashboard = () => {
  return (
    <DashboardLayout>
      {widgets.map(createWidget)}
    </DashboardLayout>
  );
}
```

4. Add route in App.tsx

## 🔐 Authentication (To Be Implemented)

Authentication setup is pending. When ready, update:
- `sisense.config.ts` with auth tokens
- Add auth context/hooks
- Implement protected routes

## 🎯 Best Practices

1. **Component Composition**: Keep components small and focused
2. **Type Safety**: Use TypeScript interfaces for all props
3. **Configuration-Driven**: Define dashboards in config files
4. **Consistent Styling**: Use theme variables for all styling
5. **Error Handling**: Wrap widgets in error boundaries

## 📝 Next Steps

1. Implement authentication
2. Add more widget types (filters, pivots)
3. Implement dashboard export functionality
4. Add real-time data refresh
5. Create more dashboard templates