# Sisense WidgetById Implementation

This document outlines how we've implemented the official Sisense `WidgetById` component according to the [official Sisense SDK documentation](https://sisense.dev/guides/sdk/modules/sdk-ui/fusion-assets/function.WidgetById.html).

## Overview

The `WidgetById` component is a thin wrapper on the ChartWidget component, used to render widgets created in a Sisense Fusion instance. We've properly integrated this component into our React application following the official API specifications.

## Implementation Details

### 1. SisenseContextProvider Setup

The main application is wrapped with `SisenseContextProvider` in `src/main.tsx`:

```tsx
<SisenseContextProvider
  url={import.meta.env.VITE_INSTANCE_URL}
  ssoEnabled={true}
  enableSilentPreAuth={true}
>
  <App />
</SisenseContextProvider>
```

### 2. SisenseWidget Component

The `SisenseWidget` component (`src/components/widgets/SisenseWidget/SisenseWidget.tsx`) properly implements the `WidgetById` component:

```tsx
import { WidgetById } from '@sisense/sdk-ui';

const widgetByIdProps: SisenseWidgetByIdProps = {
  widgetOid: widgetId,
  dashboardOid: dashboardId,
  includeDashboardFilters,
  styleOptions: widgetStyleOptions,
};

return (
  <WidgetContainer title={title} position={position}>
    <WidgetById {...widgetByIdProps} />
  </WidgetContainer>
);
```

### 3. TypeScript Integration

We've added proper TypeScript types that extend the official `WidgetByIdProps` interface:

```tsx
export interface SisenseWidgetByIdProps extends Partial<WidgetByIdProps> {
  widgetOid: string;
  dashboardOid: string;
  includeDashboardFilters?: boolean;
  styleOptions?: {
    height?: number;
    width?: number | string;
    [key: string]: any;
  };
}
```

### 4. Widget Factory

The widget factory (`src/utils/widgetFactory.tsx`) creates widgets using the official `WidgetById` component:

- In production (when `VITE_INSTANCE_URL` is set): Uses actual Sisense widgets with `WidgetById`
- In development (when `VITE_INSTANCE_URL` is not set): Uses placeholder widgets for development

### 5. Dashboard Configuration

Each dashboard (e.g., `ContractAwardsDashboard`) is configured with:
- `dashboardId`: The OID of the Sisense dashboard
- `widgetId`: The OID of individual widgets within the dashboard
- Proper positioning and styling options

## Key Features

1. **Official API Compliance**: Uses the exact `WidgetById` component from `@sisense/sdk-ui`
2. **Proper Props**: Uses `widgetOid` and `dashboardOid` properties as specified in the documentation
3. **Style Options**: Supports dynamic height and width styling
4. **Dashboard Filters**: Includes dashboard filters when `includeDashboardFilters` is true
5. **Error Handling**: Wrapped in error boundaries for robust error handling
6. **Development Mode**: Graceful fallback to placeholder widgets during development

## Configuration Requirements

To use the Sisense widgets, ensure the following environment variables are set:

```bash
VITE_INSTANCE_URL=https://your-sisense-instance.com
```

## Dashboard Widget IDs

Each widget configuration includes the proper Sisense widget OID:

```typescript
{
  id: '5defb5024faf0a20b09a2146', // Widget OID from Sisense
  type: 'chart',
  title: 'Widget Title',
  position: { x: 0, y: 0, w: 4, h: 3 },
  // ... other config
}
```

## Benefits of This Implementation

1. **Direct Integration**: Uses the official Sisense SDK without custom wrappers
2. **Performance**: Leverages Sisense's optimized widget rendering
3. **Feature Complete**: Access to all Sisense widget features and functionality
4. **Maintainable**: Follows official patterns and best practices
5. **Type Safe**: Full TypeScript support with proper interfaces

This implementation ensures that we're using the official Sisense `WidgetById` component exactly as documented, providing the best performance and feature compatibility with Sisense Fusion instances. 