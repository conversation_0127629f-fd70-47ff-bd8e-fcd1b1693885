# Universal Dashboard Filters - Integration Guide

## 🚀 Quick Start

The Universal Dashboard Filters system provides consistent, professional filters across all your dashboards with minimal setup.

### 1. Basic Integration (2 minutes)

```tsx
import React, { useState } from 'react';
import { UniversalDashboardFilters } from '../components/filters/UniversalDashboardFilters';
import type { FilterValue } from '../components/filters/UniversalDashboardFilters';
import { setupDashboardFilters } from '../utils/dashboardFilters';

export const YourDashboard: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.contractAwards(); // Choose your type

  return (
    <div>
      <h1>Your Dashboard</h1>
      
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Your widgets here */}
    </div>
  );
};
```

### 2. Available Dashboard Types

Choose the appropriate setup function:

```tsx
// Contract Awards (State + Transportation Mode)
const filterSetup = setupDashboardFilters.contractAwards();

// Federal Aid (State + Year + Program)
const filterSetup = setupDashboardFilters.federalAid();

// Material Prices (Region + Material + Time Period)
const filterSetup = setupDashboardFilters.materialPrices();

// State DOT Budgets (State + Category + Amount)
const filterSetup = setupDashboardFilters.stateDOTBudgets();

// Summary (Year + Region)
const filterSetup = setupDashboardFilters.summary();

// Value Put in Place (State + Mode + Year)
const filterSetup = setupDashboardFilters.valuePutInPlace();
```

## 📋 Dashboard Integration Checklist

For each dashboard you want to add filters to:

- [ ] Import `UniversalDashboardFilters` and `FilterValue`
- [ ] Import `setupDashboardFilters` utility
- [ ] Add filter state: `useState<FilterValue[]>([])`
- [ ] Choose appropriate filter setup
- [ ] Add `<UniversalDashboardFilters>` component
- [ ] Pass filters to your widgets

## 🎨 Customization Options

### Layout Variants

```tsx
// Horizontal (default) - filters in a row
<UniversalDashboardFilters variant="horizontal" />

// Vertical - filters stacked
<UniversalDashboardFilters variant="vertical" />
```

### Custom Styling

```tsx
<UniversalDashboardFilters
  style={{
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: '16px',
    padding: '24px'
  }}
  className="my-custom-filters"
/>
```

### Custom Filter Configuration

```tsx
import { createQuickFilters } from '../utils/dashboardFilters';

const customConfig = createQuickFilters([
  {
    id: 'project-type',
    title: 'Project Type',
    type: 'select',
    options: ['All Types', 'New Construction', 'Rehabilitation']
  },
  {
    id: 'search',
    title: 'Search',
    type: 'text',
    placeholder: 'Search projects...'
  }
]);

<UniversalDashboardFilters
  customConfig={customConfig}
  onFiltersChange={setFilters}
/>
```

## 🔧 Integration Examples

### Federal Aid Dashboard

```tsx
// src/pages/dashboards/FederalAidDashboard/FederalAidDashboard.tsx
import { UniversalDashboardFilters } from '../../../components/filters/UniversalDashboardFilters';
import type { FilterValue } from '../../../components/filters/UniversalDashboardFilters';
import { setupDashboardFilters } from '../../../utils/dashboardFilters';

export const FederalAidDashboard: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.federalAid();

  return (
    <PageLayout title="Federal Aid Dashboard">
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Your existing widgets */}
      {widgets.map(widget => (
        <FilteredSisenseWidget
          key={widget.id}
          {...widget}
          filters={dashboardFilters} // Pass filters here
        />
      ))}
    </PageLayout>
  );
};
```

### Material Prices Dashboard

```tsx
// src/pages/dashboards/MaterialPricesDashboard/MaterialPricesDashboard.tsx
export const MaterialPricesDashboard: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.materialPrices();

  return (
    <PageLayout title="Material Prices Dashboard">
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
      />
      {/* Dashboard content */}
    </PageLayout>
  );
};
```

### Summary Dashboard

```tsx
// src/pages/dashboards/SummaryDashboard/SummaryDashboard.tsx
export const SummaryDashboard: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.summary();

  return (
    <PageLayout title="Summary Dashboard">
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
      />
      {/* Dashboard content */}
    </PageLayout>
  );
};
```

## 🎯 Benefits

### ✅ Consistency
- Same look and feel across all dashboards
- Consistent filter behavior and interactions
- Unified styling with your liquid glass theme

### ✅ Easy Integration
- 2-minute setup for most dashboards
- Pre-configured filter sets for common use cases
- Minimal code changes required

### ✅ Flexibility
- Custom filter configurations when needed
- Multiple layout options
- Easy to extend with new filter types

### ✅ Maintainability
- Centralized filter logic
- Easy to update all dashboards at once
- Consistent data handling

## 🔍 Debugging

Enable filter debugging:

```tsx
import { debugFilters } from '../utils/dashboardFilters';

const handleFiltersChange = (filters: FilterValue[]) => {
  debugFilters(filters, 'My Dashboard'); // Logs filter state
  setDashboardFilters(filters);
};
```

## 📝 Next Steps

1. **Start with Contract Awards**: Already integrated ✅
2. **Add to Federal Aid Dashboard**: Copy the pattern from Contract Awards
3. **Add to Material Prices Dashboard**: Use `setupDashboardFilters.materialPrices()`
4. **Add to remaining dashboards**: Follow the same pattern
5. **Customize as needed**: Add dashboard-specific filters if required

## 🆘 Need Help?

Common issues and solutions:

**Q: Filters not showing up?**
A: Check that you imported `UniversalDashboardFilters` correctly and passed a valid `dashboardType` or `customConfig`.

**Q: Filters not applying to widgets?**
A: Make sure you're passing the `dashboardFilters` state to your `FilteredSisenseWidget` components.

**Q: Want different filters?**
A: Use `createQuickFilters()` or create a custom configuration in `filters.config.ts`.

**Q: Styling issues?**
A: The filters inherit your theme automatically. Use the `style` prop for custom styling.

---

That's it! You now have a powerful, consistent filter system across all your dashboards. 🎉
