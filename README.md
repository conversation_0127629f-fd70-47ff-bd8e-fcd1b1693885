# ARTBA Economics Dashboard

A Sisense-powered dashboard application for transportation construction industry analytics.

## Overview

This application displays 7 transportation industry dashboards using Sisense's WidgetById API to load existing widgets directly from your Sisense instance.

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure Sisense connection:**
   - Copy `.env.example` to `.env`
   - Add your Sisense instance URL and API token:
     ```
     VITE_INSTANCE_URL=https://your-sisense-instance.com
     VITE_API_TOKEN=your-api-token-here
     ```

3. **Run the development server:**
   ```bash
   npm run dev
   ```

## Architecture

The application uses Sisense's `WidgetById` component to load widgets directly from your Sisense dashboards. Each widget is identified by:
- **Dashboard ID**: The unique identifier of the dashboard in Sisense
- **Widget ID**: The unique identifier of the specific widget

These IDs are stored in configuration files for each dashboard (e.g., `summaryDashboard.config.ts`).

## Dashboards

1. **Summary Dashboard** (`5defaedc4faf0a20b09a20e6`)
   - Executive overview of key metrics
   
2. **Contract Awards** (`68654950099a11833ea60935`)
   - State and local contract awards tracking
   
3. **Value Put in Place** (`5defbf594faf0a20b09a2176`)
   - Construction value analysis
   
4. **Federal-Aid Obligations** (`5d7fb4112469173fb4e2b1c9`)
   - Federal highway aid tracking
   
5. **Material Prices** (`622a674aca7c6135304beb5b`)
   - Construction material price indices
   
6. **State Legislative Initiatives** (`61e84f9771137213003b976f`)
   - Transportation funding initiatives
   
7. **State DOT Budgets** (`667ac76d6cb17e003386f82a`)
   - State transportation budgets analysis

## Development Mode

When running without a Sisense connection (no environment variables set), the app displays placeholder widgets showing the widget IDs. This helps with development and debugging.

## Data Source Requirements

The widgets are loaded directly from your Sisense dashboards using their original data source configurations. For the widgets to load properly:

1. The data model referenced in the widgets must exist in your Sisense instance
2. The data source must be properly connected and accessible
3. The user token must have permissions to access the dashboards and data

If you see errors like "dimension was not found", it means the widget is trying to reference data that doesn't exist in your current Sisense setup.

## Troubleshooting

If widgets don't load:
1. Verify your Sisense credentials in the `.env` file
2. Ensure your Sisense instance is accessible
3. Check that the dashboard and widget IDs in the config files match your Sisense instance
4. Verify that the data model/cube referenced by the widgets exists in your Sisense instance
5. Check the browser console for specific error messages about missing dimensions or data sources
6. Ensure your API token has the necessary permissions

Common errors:
- **"Cannot read properties of undefined (reading 'CRS')"**: The widget is trying to access a coordinate reference system that's not available
- **"dimension was not found"**: The widget references a dimension that doesn't exist in your data model
- **"401 Unauthorized"**: Check your API token in the `.env` file

The application includes error boundaries that will display helpful messages when widgets fail to load.
