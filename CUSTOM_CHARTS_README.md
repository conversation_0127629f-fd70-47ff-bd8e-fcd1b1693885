# Custom Charts with Sisense Data Integration

This implementation demonstrates how to extract data from Sisense widgets using `useExecuteQueryByWidgetId` and render it with Recharts for better visualization control and styling.

## 🎯 Benefits

- **Better Design Control**: Custom charts that perfectly match your liquid glass theme
- **Enhanced Interactivity**: More responsive hover effects and animations
- **Improved Performance**: Lighter weight than full Sisense widgets
- **Brand Consistency**: Charts styled to match your application design
- **Custom Features**: Add features not available in standard Sisense widgets

## 📁 File Structure

```
src/
├── components/charts/
│   ├── CustomChart.tsx          # Base chart component with data fetching
│   ├── CustomLineChart.tsx      # Line and multi-line charts
│   ├── CustomBarChart.tsx       # Bar and stacked bar charts
│   ├── CustomPieChart.tsx       # Pie, donut, and pie with center content
│   └── index.ts                 # Export all chart components
├── utils/
│   └── sisenseDataTransform.ts  # Data transformation utilities
└── pages/dashboards/SummaryDashboard/
    └── SummaryDashboardWithCustomCharts.tsx  # Example implementation
```

## 🚀 Quick Start

### 1. Basic Line Chart

```tsx
import { CustomLineChart } from '../../../components/charts';

<CustomLineChart
  widgetId="your-widget-id"
  dashboardId="your-dashboard-id"
  title="TTM Transportation Construction Work"
  position={{ x: 0, y: 0, w: 6, h: 4 }}
  formatType="currency"
  lineColor="#ff6b35"
  strokeWidth={3}
  showGrid={true}
/>
```

### 2. Custom Bar Chart

```tsx
import { CustomBarChart } from '../../../components/charts';

<CustomBarChart
  widgetId="your-widget-id"
  dashboardId="your-dashboard-id"
  title="Market Outlook by Year"
  position={{ x: 0, y: 0, w: 6, h: 4 }}
  formatType="currency"
  barColor="#ff6b35"
  gradient={true}
  layout="vertical"
/>
```

### 3. Donut Chart with Center Content

```tsx
import { CustomDonutChart } from '../../../components/charts';

<CustomDonutChart
  widgetId="your-widget-id"
  dashboardId="your-dashboard-id"
  title="Work by Transportation Mode"
  position={{ x: 6, y: 0, w: 6, h: 4 }}
  formatType="percentage"
  colors={['#ff6b35', '#ffa947', '#ff4757', '#6EDA55', '#9b9bd7']}
/>
```

## 🔧 Available Chart Types

### Line Charts
- `CustomLineChart` - Single line chart
- `CustomMultiLineChart` - Multiple lines on same chart

### Bar Charts
- `CustomBarChart` - Single series bar chart
- `CustomStackedBarChart` - Multiple stacked series

### Pie Charts
- `CustomPieChart` - Standard pie chart
- `CustomDonutChart` - Donut chart (pie with center hole)
- `CustomPieChartWithCenter` - Donut with custom center content

## 📊 Data Transformation

The `sisenseDataTransform.ts` utility handles converting Sisense data format to Recharts format:

```typescript
// Sisense format
{
  rows: [
    [{ data: "Q1", text: "Q1" }, { data: 1000000, text: "1000000" }],
    [{ data: "Q2", text: "Q2" }, { data: 1500000, text: "1500000" }]
  ],
  columns: [
    { name: "Quarter", type: "text" },
    { name: "Revenue", type: "numeric" }
  ]
}

// Transformed to Recharts format
[
  { category: "Q1", value: 1000000 },
  { category: "Q2", value: 1500000 }
]
```

## 🎨 Styling Features

### Liquid Glass Theme Integration
- Glassmorphism effects on chart containers
- Consistent color palette from theme
- Proper typography and spacing
- Responsive design

### Custom Formatting
- Currency formatting: `$1.2B`, `$500M`, `$50K`
- Number formatting: `1.2B`, `500M`, `50K`
- Percentage formatting: `45.2%`

### Interactive Features
- Glass-styled tooltips
- Hover effects
- Animated transitions
- Responsive legends

## 🔄 Replacing Existing Widgets

To replace a Sisense widget with a custom chart:

1. **Find the widget ID** from your dashboard configuration
2. **Choose the appropriate chart type** based on your data
3. **Replace the widget** in your dashboard component:

```tsx
// Before: Using Sisense WidgetById
{widgets.map((widget) => createWidget(widget, dashboardId))}

// After: Using custom chart
<CustomLineChart
  widgetId={widget.id}
  dashboardId={dashboardId}
  title={widget.title}
  position={widget.position}
  formatType="currency"
/>
```

## 🧪 Testing the Implementation

Visit `/dashboard/summary-custom` to see the custom charts in action compared to the original Sisense widgets.

## 🛠️ Advanced Customization

### Custom Colors
```tsx
const customColors = [
  theme.colors.primary,
  theme.colors.secondary,
  theme.colors.accent,
  '#6EDA55',
  '#9b9bd7',
];

<CustomPieChart colors={customColors} />
```

### Custom Data Transformation
```tsx
import { transformSisenseData } from '../../../utils/sisenseDataTransform';

const customTransform = (data) => {
  return transformSisenseData(data, {
    xAxisKey: 'date',
    yAxisKey: 'amount',
    categoryKey: 'period'
  });
};
```

### Error Handling
All charts include built-in error handling:
- Loading states with skeleton animations
- Error boundaries with retry functionality
- Graceful fallbacks for missing data

## 📈 Performance Benefits

- **Reduced Bundle Size**: Only load chart components you need
- **Faster Rendering**: Direct data binding without Sisense widget overhead
- **Better Caching**: Data can be cached and reused across components
- **Optimized Updates**: React's reconciliation for efficient re-renders

## 🔮 Future Enhancements

- Area charts for trend visualization
- Scatter plots for correlation analysis
- Heatmaps for geographic data
- Real-time data streaming
- Export functionality (PNG, SVG, PDF)
- Advanced filtering and drill-down capabilities
