{"name": "econ", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@sisense/sdk-cli": "latest", "@sisense/sdk-data": "latest", "@sisense/sdk-query-client": "^2.5.0", "@sisense/sdk-ui": "latest", "@types/d3": "^7.4.3", "@types/geojson": "^7946.0.16", "@types/react-simple-maps": "^3.0.6", "@types/react-tooltip": "^3.11.0", "@types/topojson-client": "^3.1.5", "@types/topojson-specification": "^1.0.5", "d3": "^7.9.0", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1", "react-simple-maps": "^3.0.0", "react-tooltip": "^5.29.1", "recharts": "^2.15.4", "topojson-client": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "overrides": {"react-custom-scrollbars": {"react": "$react", "react-dom": "$react-dom"}, "leaflet": "1.9.4", "proj4leaflet": "1.0.2"}}