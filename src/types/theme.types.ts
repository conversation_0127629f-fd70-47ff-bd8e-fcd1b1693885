export interface Theme {
  id: string;
  name: string;
  description?: string;
  colors: ThemeColors;
  typography: ThemeTypography;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
  transitions: ThemeTransitions;
  breakpoints: ThemeBreakpoints;
  blur: ThemeBlur;
  sisense?: SisenseThemeConfig;
}

export interface ThemeColors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  accent: string;
  accentLight: string;
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    blur: string;
  };
  backgroundGradient: string;
  surface: string;
  surfaceElevated: string;
  surfaceHover: string;
  glass: {
    primary: string;
    secondary: string;
    tertiary: string;
    border: string;
    dark: string;
  };
  error: string;
  warning: string;
  info: string;
  success: string;
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    disabled: string;
    inverse: string;
  };
  border: {
    light: string;
    DEFAULT: string;
    dark: string;
    glass: string;
  };
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
    warm: string;
    cool: string;
    success: string;
    surface: string;
    glass: string;
    glow: string;
  };
}

export interface ThemeTypography {
  fontFamily: {
    sans: string;
    mono: string;
  };
  fontSize: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
    xxxl: string;
  };
  fontWeight: {
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
  lineHeight: {
    tight: number;
    normal: number;
    relaxed: number;
  };
}

export interface ThemeSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
  widget: number;
  dashboard: number;
  section: number;
}

export interface ThemeBorderRadius {
  xs: number | string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: number;
  widget: number;
  card: number;
  full: string;
}

export interface ThemeShadows {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  widget: string;
  card: string;
  cardHover: string;
  inner: string;
  glass: string;
  glow: string;
  none: string;
}

export interface ThemeTransitions {
  fast: string;
  normal: string;
  slow: string;
  bounce: string;
}

export interface ThemeBreakpoints {
  xs: number;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface ThemeBlur {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface SisenseThemeConfig {
  palette?: {
    name: string;
    colors: string[];
  };
  widgetStyles?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderRadius?: string;
  };
}

export interface ThemeContextValue {
  theme: Theme;
  isDarkMode: boolean;
  isCustomTheme: boolean;
  setTheme: (theme: Theme) => void;
  toggleDarkMode: () => void;
  updateTheme: (updates: Partial<Theme>) => void;
  resetTheme: () => void;
  exportTheme: () => string;
  importTheme: (themeData: string) => void;
}

export interface ThemePreset {
  id: string;
  name: string;
  description: string;
  theme: Theme;
  category: 'default' | 'dark' | 'colorful' | 'minimal' | 'custom';
  preview?: string;
}

export interface ThemeCustomization {
  baseThemeId: string;
  overrides: DeepPartial<Theme>;
}

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export interface ThemeExport {
  version: string;
  timestamp: string;
  theme: Theme;
  metadata?: {
    teamName?: string;
    createdBy?: string;
    description?: string;
  };
}