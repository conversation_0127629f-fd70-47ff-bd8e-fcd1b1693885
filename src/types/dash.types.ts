// Type definitions for Sisense .dash file format

export interface DashFile {
  title: string;
  desc: string;
  source: string;
  type: 'dashboard';
  style?: DashStyle;
  layout: DashLayout;
  widgets: DashWidget[];
  datasource?: DashDataSource;
  filters?: DashFilter[];
  filterRelations?: any;
}

export interface DashStyle {
  palette?: {
    name: string;
    colors: string[];
  };
}

export interface DashDataSource {
  title: string;
  fullname: string;
  id: string;
  address: string;
  database: string;
}

export interface DashLayout {
  instanceid: string;
  type: 'columnar';
  columns: DashColumn[];
}

export interface DashColumn {
  width: number;
  pxlWidth?: number;
  index?: number;
  cells: DashCell[];
}

export interface DashCell {
  subcells: DashSubCell[];
}

export interface DashSubCell {
  elements: DashElement[];
  width: number;
  stretchable: boolean;
  pxlWidth?: number;
  index: number;
}

export interface DashElement {
  minHeight: number;
  maxHeight: number;
  minWidth: number;
  maxWidth: number;
  height: number | string;
  defaultWidth: number;
  widgetid: string;
}

export interface DashWidget {
  oid: string;
  title: string;
  type: string;
  subtype?: string;
  desc?: string | null;
  source?: string;
  datasource?: DashDataSource;
  selection?: any;
  metadata: DashWidgetMetadata;
  style?: any;
  refresh?: number;
  options?: any;
}

export interface DashWidgetMetadata {
  ignore?: {
    dimensions: string[];
    all: boolean;
    ids: string[];
  };
  panels: DashPanel[];
  usedFormulasMapping?: Record<string, any>;
}

export interface DashPanel {
  name: string;
  items: DashPanelItem[];
}

export interface DashPanelItem {
  jaql?: JAQLQuery;
  format?: DashFormat;
  instanceid?: string;
  panel?: string;
  field?: any;
}

export interface JAQLQuery {
  type?: 'measure' | 'dimension';
  dim?: string;
  datatype?: string;
  table?: string;
  column?: string;
  formula?: string;
  context?: Record<string, JAQLContext>;
  title?: string;
  agg?: string;
  level?: string;
  filter?: JAQLFilter;
  sort?: string;
}

export interface JAQLContext {
  table: string;
  column: string;
  dim: string;
  datatype: string;
  title: string;
  agg?: string;
  filter?: JAQLFilter;
  collapsed?: boolean;
  datasource?: DashDataSource;
}

export interface JAQLFilter {
  explicit?: boolean;
  multiSelection?: boolean;
  members?: string[];
  level?: string;
  filter?: any;
  custom?: boolean;
  from?: string;
  to?: string;
  anchor?: string;
  offset?: number;
}

export interface DashFormat {
  mask?: {
    abbreviations?: {
      t?: boolean;
      b?: boolean;
      m?: boolean;
      k?: boolean;
    };
    decimals?: number;
    currency?: {
      symbol: string;
      position: 'pre' | 'post';
    };
    percent?: boolean;
  };
  color?: {
    type: string;
    color?: string;
    colorIndex?: number;
    isHandPickedColor?: boolean;
  };
}

export interface DashFilter {
  jaql: JAQLQuery;
  instanceid?: string;
  isCascading?: boolean;
}

// Widget type mappings
export type DashWidgetType = 
  | 'indicator'
  | 'indicator/numeric'
  | 'indicator/gauge'
  | 'chart/column'
  | 'chart/bar'
  | 'chart/line'
  | 'chart/area'
  | 'chart/pie'
  | 'chart/scatter'
  | 'chart/polar'
  | 'pivot2'
  | 'BloX'
  | 'treemap'
  | 'sunburst'
  | 'map/scatter';

// Subtype mappings
export type DashIndicatorSubtype = 'simple' | 'round';
export type DashChartSubtype = 'column/classic' | 'area/classic' | 'line/classic' | 'pie/classic';