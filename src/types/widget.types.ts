// Basic widget interfaces - no Sisense SDK type imports needed
// We only use WidgetById component from @sisense/sdk-ui

export interface BaseWidgetProps {
  id: string;
  title?: string;
  description?: string;
  className?: string;
}

export interface ChartWidgetProps extends BaseWidgetProps {
  chartType: string;
  dataOptions: Record<string, unknown>;
  dataSource?: Record<string, unknown>;
  filters?: Record<string, unknown>[];
  highlights?: Record<string, unknown>[];
  onDataPointClick?: (point: Record<string, unknown>, event: Event) => void;
}

export interface KPIWidgetProps extends BaseWidgetProps {
  value: number | string;
  previousValue?: number | string;
  format?: 'number' | 'currency' | 'percentage';
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
}

export interface TableWidgetProps extends BaseWidgetProps {
  dataOptions: Record<string, unknown>;
  dataSet?: Record<string, unknown>;
  filters?: Record<string, unknown>[];
}