// Sisense configuration
// Authentication will be implemented later

export const sisenseConfig = {
  url: import.meta.env.VITE_SISENSE_URL || 'https://your-sisense-instance.com',
  defaultDataSource: import.meta.env.VITE_SISENSE_DATA_SOURCE || 'ARTBA Economics',
  // Authentication config will be added later
  // token: import.meta.env.VITE_SISENSE_TOKEN,
};

import { createAttribute } from '@sisense/sdk-data';

// ARTBA Economics data model
// Based on actual dashboard filters from your Sisense instance
export const DM = {
  ARTBAEconomics: {
    // Executive Summary table
    ExecutiveSummary: {
      Mode: createAttribute({
        name: 'mode',
        type: 'text',
        expression: '[executive_summary.csv.mode]',
      }),
      MonthStart: createAttribute({
        name: 'MonthStart',
        type: 'datetime',
        expression: '[executive_summary.csv.MonthStart (Calendar)]',
      }),
      MonthCurrentAwards: createAttribute({
        name: 'Month Current Awards',
        type: 'numeric',
        expression: '[executive_summary.csv.month_current_awards]',
      }),
      MonthCurrentVpip: createAttribute({
        name: 'Month Current VPIP',
        type: 'numeric',
        expression: '[executive_summary.csv.month_current_vpip]',
      }),
    },
    // Awards table
    Awards: {
      State: createAttribute({
        name: 'state',
        type: 'text',
        expression: '[awards.state]',
      }),
    },
    // VPIP table
    VPIP: {
      Year: createAttribute({
        name: 'Year',
        type: 'datetime',
        expression: '[vpip.csv.year]',
      }),
      MajorMode: createAttribute({
        name: 'Major Mode',
        type: 'text',
        expression: '[vpip.csv.major_mode]',
      }),
      MonthStart: createAttribute({
        name: 'MonthStart',
        type: 'datetime',
        expression: '[vpip.csv.MonthStart (Calendar)]',
      }),
    },
    // Ballot table (for election data)
    Ballot: {
      Result: createAttribute({
        name: 'Result',
        type: 'text',
        expression: '[ballot.result]',
      }),
    },
  },
};