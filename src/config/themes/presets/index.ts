import type { Theme, ThemePreset } from '../../../types';
import { createBaseTheme } from '../baseTheme';

export const darkTheme: Theme = createBaseTheme({
  id: 'dark',
  name: 'Dark Theme',
  description: 'Dark theme with deep blue tones',
  colors: {
    primary: '#5b92e5',
    primaryLight: '#7aa5ea',
    primaryDark: '#4a7bc4',
    secondary: '#7c6ff3',
    secondaryLight: '#9a8ff6',
    secondaryDark: '#6356d0',
    accent: '#ff6b9d',
    accentLight: '#ff8db3',
    background: {
      primary: '#0f1419',
      secondary: '#1a1f2e',
      tertiary: '#252a3a',
      blur: 'rgba(15, 20, 25, 0.85)',
    },
    backgroundGradient: 'linear-gradient(135deg, #0f1419 0%, #1a1f2e 25%, #252a3a 50%, #2f3646 75%, #394152 100%)',
    surface: 'rgba(255, 255, 255, 0.05)',
    surfaceElevated: 'rgba(255, 255, 255, 0.08)',
    surfaceHover: 'rgba(255, 255, 255, 0.1)',
    glass: {
      primary: 'rgba(255, 255, 255, 0.05)',
      secondary: 'rgba(255, 255, 255, 0.08)',
      tertiary: 'rgba(255, 255, 255, 0.1)',
      border: 'rgba(255, 255, 255, 0.1)',
      dark: 'rgba(0, 0, 0, 0.4)',
    },
    text: {
      primary: '#e1e8ed',
      secondary: '#9ca3af',
      tertiary: '#6b7280',
      disabled: '#4b5563',
      inverse: '#0f1419',
    },
    border: {
      light: 'rgba(255, 255, 255, 0.1)',
      DEFAULT: 'rgba(255, 255, 255, 0.15)',
      dark: 'rgba(255, 255, 255, 0.2)',
      glass: 'rgba(255, 255, 255, 0.1)',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #5b92e5 0%, #7aa5ea 100%)',
      secondary: 'linear-gradient(135deg, #7c6ff3 0%, #9a8ff6 100%)',
      accent: 'linear-gradient(135deg, #ff6b9d 0%, #ff8db3 100%)',
      warm: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      cool: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      success: 'linear-gradient(135deg, #00b09b 0%, #96c93d 100%)',
      surface: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%)',
      glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
      glow: 'linear-gradient(135deg, rgba(91, 146, 229, 0.2) 0%, rgba(124, 111, 243, 0.2) 100%)',
    },
  },
  sisense: {
    palette: {
      name: 'Dark Mode',
      colors: [
        '#5b92e5',
        '#7c6ff3',
        '#ff6b9d',
        '#feca57',
        '#48dbfb',
        '#0abde3'
      ]
    }
  }
});

export const minimalTheme: Theme = createBaseTheme({
  id: 'minimal',
  name: 'Minimal Theme',
  description: 'Clean and minimal theme with monochrome colors',
  colors: {
    primary: '#000000',
    primaryLight: '#333333',
    primaryDark: '#000000',
    secondary: '#666666',
    secondaryLight: '#888888',
    secondaryDark: '#444444',
    accent: '#000000',
    accentLight: '#333333',
    background: {
      primary: '#ffffff',
      secondary: '#fafafa',
      tertiary: '#f5f5f5',
      blur: 'rgba(255, 255, 255, 0.9)',
    },
    backgroundGradient: 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
    surface: 'rgba(0, 0, 0, 0.02)',
    surfaceElevated: 'rgba(0, 0, 0, 0.04)',
    surfaceHover: 'rgba(0, 0, 0, 0.06)',
    glass: {
      primary: 'rgba(255, 255, 255, 0.8)',
      secondary: 'rgba(255, 255, 255, 0.9)',
      tertiary: 'rgba(255, 255, 255, 0.95)',
      border: 'rgba(0, 0, 0, 0.1)',
      dark: 'rgba(0, 0, 0, 0.05)',
    },
    text: {
      primary: '#000000',
      secondary: '#4a4a4a',
      tertiary: '#7a7a7a',
      disabled: '#9a9a9a',
      inverse: '#ffffff',
    },
    border: {
      light: 'rgba(0, 0, 0, 0.08)',
      DEFAULT: 'rgba(0, 0, 0, 0.12)',
      dark: 'rgba(0, 0, 0, 0.16)',
      glass: 'rgba(0, 0, 0, 0.1)',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
      secondary: 'linear-gradient(135deg, #666666 0%, #888888 100%)',
      accent: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
      warm: 'linear-gradient(135deg, #333333 0%, #666666 100%)',
      cool: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
      success: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
      surface: 'linear-gradient(135deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.02) 100%)',
      glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)',
      glow: 'linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%)',
    },
  },
  shadows: {
    xs: '0 1px 2px rgba(0, 0, 0, 0.04)',
    sm: '0 2px 4px rgba(0, 0, 0, 0.06)',
    md: '0 4px 8px rgba(0, 0, 0, 0.08)',
    lg: '0 8px 16px rgba(0, 0, 0, 0.1)',
    xl: '0 16px 32px rgba(0, 0, 0, 0.12)',
    widget: '0 2px 8px rgba(0, 0, 0, 0.08)',
    card: '0 2px 8px rgba(0, 0, 0, 0.08)',
    cardHover: '0 4px 16px rgba(0, 0, 0, 0.12)',
    inner: 'inset 0 1px 2px 0 rgba(0, 0, 0, 0.04)',
    glass: '0 2px 16px 0 rgba(0, 0, 0, 0.08)',
    glow: '0 0 16px rgba(0, 0, 0, 0.1)',
    none: 'none',
  },
  sisense: {
    palette: {
      name: 'Monochrome',
      colors: [
        '#000000',
        '#333333',
        '#666666',
        '#999999',
        '#cccccc',
        '#eeeeee'
      ]
    }
  }
});

export const oceanTheme: Theme = createBaseTheme({
  id: 'ocean',
  name: 'Ocean Theme',
  description: 'Cool blue ocean-inspired theme',
  colors: {
    primary: '#006ba6',
    primaryLight: '#2196f3',
    primaryDark: '#003d5c',
    secondary: '#00acc1',
    secondaryLight: '#5ddef4',
    secondaryDark: '#007c91',
    accent: '#00e5ff',
    accentLight: '#6effff',
    background: {
      primary: '#e0f7fa',
      secondary: '#b2ebf2',
      tertiary: '#80deea',
      blur: 'rgba(224, 247, 250, 0.85)',
    },
    backgroundGradient: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 25%, #80deea 50%, #4dd0e1 75%, #26c6da 100%)',
    text: {
      primary: '#004d66',
      secondary: '#006380',
      tertiary: '#00798c',
      disabled: '#668a99',
      inverse: '#ffffff',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #006ba6 0%, #2196f3 100%)',
      secondary: 'linear-gradient(135deg, #00acc1 0%, #5ddef4 100%)',
      accent: 'linear-gradient(135deg, #00e5ff 0%, #6effff 100%)',
      warm: 'linear-gradient(135deg, #ff6f00 0%, #ffa726 100%)',
      cool: 'linear-gradient(135deg, #00bcd4 0%, #0097a7 100%)',
      success: 'linear-gradient(135deg, #00c853 0%, #64dd17 100%)',
      surface: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%)',
      glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%)',
      glow: 'linear-gradient(135deg, rgba(0, 107, 166, 0.2) 0%, rgba(0, 172, 193, 0.2) 100%)',
    },
  },
  sisense: {
    palette: {
      name: 'Ocean',
      colors: [
        '#006ba6',
        '#00acc1',
        '#00e5ff',
        '#0288d1',
        '#03a9f4',
        '#40c4ff'
      ]
    }
  }
});

export const forestTheme: Theme = createBaseTheme({
  id: 'forest',
  name: 'Forest Theme',
  description: 'Natural green forest-inspired theme',
  colors: {
    primary: '#2e7d32',
    primaryLight: '#4caf50',
    primaryDark: '#1b5e20',
    secondary: '#689f38',
    secondaryLight: '#8bc34a',
    secondaryDark: '#558b2f',
    accent: '#00e676',
    accentLight: '#69f0ae',
    background: {
      primary: '#f1f8e9',
      secondary: '#dcedc8',
      tertiary: '#c5e1a5',
      blur: 'rgba(241, 248, 233, 0.85)',
    },
    backgroundGradient: 'linear-gradient(135deg, #f1f8e9 0%, #dcedc8 25%, #c5e1a5 50%, #aed581 75%, #9ccc65 100%)',
    text: {
      primary: '#1b5e20',
      secondary: '#2e7d32',
      tertiary: '#388e3c',
      disabled: '#689f38',
      inverse: '#ffffff',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 100%)',
      secondary: 'linear-gradient(135deg, #689f38 0%, #8bc34a 100%)',
      accent: 'linear-gradient(135deg, #00e676 0%, #69f0ae 100%)',
      warm: 'linear-gradient(135deg, #ff6f00 0%, #ffa000 100%)',
      cool: 'linear-gradient(135deg, #00695c 0%, #00897b 100%)',
      success: 'linear-gradient(135deg, #2e7d32 0%, #43a047 100%)',
      surface: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%)',
      glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%)',
      glow: 'linear-gradient(135deg, rgba(46, 125, 50, 0.2) 0%, rgba(104, 159, 56, 0.2) 100%)',
    },
  },
  sisense: {
    palette: {
      name: 'Forest',
      colors: [
        '#2e7d32',
        '#689f38',
        '#00e676',
        '#4caf50',
        '#8bc34a',
        '#cddc39'
      ]
    }
  }
});

export const themePresets: ThemePreset[] = [
  {
    id: 'default',
    name: 'Default Theme',
    description: 'Default light theme with warm orange tones',
    theme: createBaseTheme(),
    category: 'default',
  },
  {
    id: 'dark',
    name: 'Dark Theme',
    description: 'Dark theme with deep blue tones',
    theme: darkTheme,
    category: 'dark',
  },
  {
    id: 'minimal',
    name: 'Minimal Theme',
    description: 'Clean and minimal theme with monochrome colors',
    theme: minimalTheme,
    category: 'minimal',
  },
  {
    id: 'ocean',
    name: 'Ocean Theme',
    description: 'Cool blue ocean-inspired theme',
    theme: oceanTheme,
    category: 'colorful',
  },
  {
    id: 'forest',
    name: 'Forest Theme',
    description: 'Natural green forest-inspired theme',
    theme: forestTheme,
    category: 'colorful',
  },
];

export const getThemePreset = (id: string): Theme => {
  const preset = themePresets.find(p => p.id === id);
  return preset?.theme || createBaseTheme();
};