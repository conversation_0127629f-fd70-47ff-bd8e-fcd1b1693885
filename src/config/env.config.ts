/**
 * Environment configuration with validation
 */

interface EnvConfig {
  INSTANCE_URL: string;
  TOKEN: string;
  ENV?: 'development' | 'production' | 'test';
}

class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  private config: EnvConfig;

  private constructor() {
    this.config = this.validateEnvironment();
  }

  static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  private validateEnvironment(): EnvConfig {
    const errors: string[] = [];

    // Required environment variables
    if (!import.meta.env.VITE_INSTANCE_URL) {
      errors.push('VITE_INSTANCE_URL is required');
    }

    if (!import.meta.env.VITE_TOKEN) {
      errors.push('VITE_TOKEN is required');
    }

    // Validate URL format
    if (import.meta.env.VITE_INSTANCE_URL && !this.isValidUrl(import.meta.env.VITE_INSTANCE_URL)) {
      errors.push('VITE_INSTANCE_URL must be a valid URL');
    }

    // Validate token format (basic check)
    if (import.meta.env.VITE_TOKEN && import.meta.env.VITE_TOKEN.length < 10) {
      errors.push('VITE_TOKEN appears to be invalid');
    }

    if (errors.length > 0) {
      const errorMessage = `Environment validation failed:\n${errors.join('\n')}`;
      
      // In development, show a more helpful error
      if (import.meta.env.DEV) {
        console.error(errorMessage);
        console.error('\nPlease check your .env file and ensure all required variables are set.');
        console.error('Refer to .env.example for the required format.');
      }
      
      throw new Error(errorMessage);
    }

    return {
      INSTANCE_URL: import.meta.env.VITE_INSTANCE_URL,
      TOKEN: import.meta.env.VITE_TOKEN,
      ENV: (import.meta.env.VITE_ENV as EnvConfig['ENV']) || 'development',
    };
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  getConfig(): EnvConfig {
    return this.config;
  }

  get instanceUrl(): string {
    return this.config.INSTANCE_URL;
  }

  get token(): string {
    return this.config.TOKEN;
  }

  get isDevelopment(): boolean {
    return this.config.ENV === 'development' || import.meta.env.DEV;
  }

  get isProduction(): boolean {
    return this.config.ENV === 'production' || import.meta.env.PROD;
  }
}

// Export a singleton instance
export const env = EnvironmentValidator.getInstance();
export type { EnvConfig };