/**
 * Global Filter Configurations
 * 
 * This file contains filter configurations for different dashboard types.
 * Each dashboard can use predefined filter sets or create custom ones.
 */

// Type definitions
export type FilterOption = {
  value: string;
  label: string;
  disabled?: boolean;
}

export type FilterConfig = {
  id: string;
  title: string;
  type: 'select' | 'multiselect' | 'text' | 'dateRange' | 'number';
  options?: FilterOption[] | string[];
  defaultValue?: any;
  placeholder?: string;
  required?: boolean;
  width?: 'auto' | 'small' | 'medium' | 'large' | 'full';
  description?: string;
}

export type DashboardFilterConfig = {
  id: string;
  name: string;
  description?: string;
  filters: FilterConfig[];
  layout?: 'horizontal' | 'vertical' | 'grid';
  showClearAll?: boolean;
  showActiveCount?: boolean;
}

// Common filter options that can be reused across dashboards
export const COMMON_FILTER_OPTIONS = {
  // Transportation modes from ARTBA data
  TRANSPORTATION_MODES: [
    'All Modes',
    'Airport',
    'Bridge & Tunnel', 
    'Highway & Pavement',
    'Rail & Transit',
    'Waterway'
  ],

  // US States
  US_STATES: [
    'All States',
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
    'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
    'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
    'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
    'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
    'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
    'West Virginia', 'Wisconsin', 'Wyoming'
  ],

  // Common year ranges
  RECENT_YEARS: [
    'All Years',
    '2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015'
  ],

  // Common time periods
  TIME_PERIODS: [
    'All Time',
    'Last 30 Days',
    'Last 90 Days',
    'Last 6 Months',
    'Last Year',
    'Year to Date'
  ],

  // Budget ranges (for budget-related dashboards)
  BUDGET_RANGES: [
    'All Amounts',
    'Under $1M',
    '$1M - $10M',
    '$10M - $50M',
    '$50M - $100M',
    'Over $100M'
  ],

  // Project status options
  PROJECT_STATUS: [
    'All Status',
    'Planning',
    'In Progress',
    'Completed',
    'On Hold',
    'Cancelled'
  ]
};

// Predefined dashboard filter configurations
export const DASHBOARD_FILTER_CONFIGS: Record<string, DashboardFilterConfig> = {
  // Contract Awards Dashboard (current implementation)
  CONTRACT_AWARDS: {
    id: 'contract-awards',
    name: 'Contract Awards Filters',
    description: 'Filter contract awards data by state and transportation mode',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'state-filter',
        title: 'State',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.US_STATES,
        defaultValue: 'All States',
        width: 'medium'
      },
      {
        id: 'mode-filter',
        title: 'Transportation Mode',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.TRANSPORTATION_MODES,
        defaultValue: 'All Modes',
        width: 'medium'
      }
    ]
  },

  // Federal Aid Dashboard
  FEDERAL_AID: {
    id: 'federal-aid',
    name: 'Federal Aid Filters',
    description: 'Filter federal aid data by state, program, and time period',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'state-filter',
        title: 'State',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.US_STATES,
        defaultValue: 'All States',
        width: 'medium'
      },
      {
        id: 'year-filter',
        title: 'Year',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.RECENT_YEARS,
        defaultValue: 'All Years',
        width: 'small'
      },
      {
        id: 'program-filter',
        title: 'Program Type',
        type: 'select',
        options: [
          'All Programs',
          'Highway Trust Fund',
          'Bridge Replacement',
          'Transit Formula',
          'Airport Improvement',
          'Rail Infrastructure'
        ],
        defaultValue: 'All Programs',
        width: 'medium'
      }
    ]
  },

  // Material Prices Dashboard
  MATERIAL_PRICES: {
    id: 'material-prices',
    name: 'Material Prices Filters',
    description: 'Filter material prices by region, material type, and time period',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'region-filter',
        title: 'Region',
        type: 'select',
        options: [
          'All Regions',
          'Northeast',
          'Southeast',
          'Midwest',
          'Southwest',
          'West Coast',
          'Mountain States'
        ],
        defaultValue: 'All Regions',
        width: 'medium'
      },
      {
        id: 'material-filter',
        title: 'Material Type',
        type: 'select',
        options: [
          'All Materials',
          'Asphalt',
          'Concrete',
          'Steel',
          'Aggregate',
          'Fuel',
          'Equipment'
        ],
        defaultValue: 'All Materials',
        width: 'medium'
      },
      {
        id: 'period-filter',
        title: 'Time Period',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.TIME_PERIODS,
        defaultValue: 'All Time',
        width: 'medium'
      }
    ]
  },

  // State DOT Budgets Dashboard
  STATE_DOT_BUDGETS: {
    id: 'state-dot-budgets',
    name: 'State DOT Budget Filters',
    description: 'Filter state DOT budget data by state, budget category, and amount',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'state-filter',
        title: 'State',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.US_STATES,
        defaultValue: 'All States',
        width: 'medium'
      },
      {
        id: 'category-filter',
        title: 'Budget Category',
        type: 'select',
        options: [
          'All Categories',
          'Capital Projects',
          'Maintenance',
          'Operations',
          'Planning',
          'Administration'
        ],
        defaultValue: 'All Categories',
        width: 'medium'
      },
      {
        id: 'amount-filter',
        title: 'Budget Range',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.BUDGET_RANGES,
        defaultValue: 'All Amounts',
        width: 'medium'
      }
    ]
  },

  // Summary Dashboard (overview of all data)
  SUMMARY: {
    id: 'summary',
    name: 'Summary Filters',
    description: 'High-level filters for summary dashboard',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'year-filter',
        title: 'Year',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.RECENT_YEARS,
        defaultValue: 'All Years',
        width: 'small'
      },
      {
        id: 'region-filter',
        title: 'Region',
        type: 'select',
        options: [
          'All Regions',
          'Northeast',
          'Southeast', 
          'Midwest',
          'Southwest',
          'West Coast'
        ],
        defaultValue: 'All Regions',
        width: 'medium'
      }
    ]
  },

  // Value Put in Place Dashboard
  VALUE_PUT_IN_PLACE: {
    id: 'value-put-in-place',
    name: 'Value Put in Place Filters',
    description: 'Filter VPIP data by state, mode, and time period',
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    filters: [
      {
        id: 'state-filter',
        title: 'State',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.US_STATES,
        defaultValue: 'All States',
        width: 'medium'
      },
      {
        id: 'mode-filter',
        title: 'Transportation Mode',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.TRANSPORTATION_MODES,
        defaultValue: 'All Modes',
        width: 'medium'
      },
      {
        id: 'year-filter',
        title: 'Year',
        type: 'select',
        options: COMMON_FILTER_OPTIONS.RECENT_YEARS,
        defaultValue: 'All Years',
        width: 'small'
      }
    ]
  }
};

/**
 * Get filter configuration for a specific dashboard
 */
export const getDashboardFilterConfig = (dashboardType: string): DashboardFilterConfig | null => {
  return DASHBOARD_FILTER_CONFIGS[dashboardType] || null;
};

/**
 * Create a custom filter configuration
 */
export const createCustomFilterConfig = (
  id: string,
  name: string,
  filters: FilterConfig[],
  options?: Partial<DashboardFilterConfig>
): DashboardFilterConfig => {
  return {
    id,
    name,
    filters,
    layout: 'horizontal',
    showClearAll: true,
    showActiveCount: true,
    ...options
  };
};
