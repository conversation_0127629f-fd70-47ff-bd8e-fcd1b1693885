/**
 * Dashboard Filter Integration Examples
 * 
 * This file shows how to integrate UniversalDashboardFilters into different dashboards
 */

import React, { useState } from 'react';
import { UniversalDashboardFilters } from '../components/filters/UniversalDashboardFilters';
import type { FilterValue } from '../components/filters/UniversalDashboardFilters';
import { setupDashboardFilters, createQuickFilters } from '../utils/dashboardFilters';

// Example 1: Federal Aid Dashboard Integration
export const FederalAidDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.federalAid();

  const handleFiltersChange = (filters: FilterValue[]) => {
    setDashboardFilters(filters);
    // Apply filters to your widgets here
    console.log('Federal Aid filters:', filters);
  };

  return (
    <div>
      <h1>Federal Aid Dashboard</h1>
      
      {/* Universal Filters */}
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={handleFiltersChange}
        variant="horizontal"
      />
      
      {/* Your dashboard content here */}
      <div>
        {/* Widgets would go here */}
        <p>Active filters: {dashboardFilters.length}</p>
      </div>
    </div>
  );
};

// Example 2: Material Prices Dashboard Integration
export const MaterialPricesDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.materialPrices();

  return (
    <div>
      <h1>Material Prices Dashboard</h1>
      
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Dashboard content */}
    </div>
  );
};

// Example 3: Custom Filter Configuration
export const CustomDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  
  // Create custom filters specific to this dashboard
  const customConfig = createQuickFilters([
    {
      id: 'project-type',
      title: 'Project Type',
      type: 'select',
      options: [
        'All Types',
        'New Construction',
        'Rehabilitation',
        'Maintenance',
        'Planning'
      ]
    },
    {
      id: 'contractor-search',
      title: 'Contractor',
      type: 'text',
      placeholder: 'Search contractors...'
    }
  ]);

  return (
    <div>
      <h1>Custom Dashboard</h1>
      
      <UniversalDashboardFilters
        customConfig={customConfig}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Dashboard content */}
    </div>
  );
};

// Example 4: State DOT Budgets Dashboard Integration
export const StateDOTBudgetsDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.stateDOTBudgets();

  return (
    <div>
      <h1>State DOT Budgets Dashboard</h1>
      
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Dashboard content */}
    </div>
  );
};

// Example 5: Summary Dashboard Integration
export const SummaryDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.summary();

  return (
    <div>
      <h1>Summary Dashboard</h1>
      
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Dashboard content */}
    </div>
  );
};

// Example 6: Value Put in Place Dashboard Integration
export const ValuePutInPlaceDashboardExample: React.FC = () => {
  const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
  const filterSetup = setupDashboardFilters.valuePutInPlace();

  return (
    <div>
      <h1>Value Put in Place Dashboard</h1>
      
      <UniversalDashboardFilters
        dashboardType={filterSetup.dashboardType}
        onFiltersChange={setDashboardFilters}
        variant="horizontal"
      />
      
      {/* Dashboard content */}
    </div>
  );
};

/**
 * Integration Steps for Any Dashboard:
 * 
 * 1. Import the required components:
 *    import { UniversalDashboardFilters } from '../components/filters/UniversalDashboardFilters';
 *    import type { FilterValue } from '../components/filters/UniversalDashboardFilters';
 *    import { setupDashboardFilters } from '../utils/dashboardFilters';
 * 
 * 2. Add state for filters:
 *    const [dashboardFilters, setDashboardFilters] = useState<FilterValue[]>([]);
 * 
 * 3. Get filter setup (choose one):
 *    const filterSetup = setupDashboardFilters.contractAwards();     // For contract awards
 *    const filterSetup = setupDashboardFilters.federalAid();         // For federal aid
 *    const filterSetup = setupDashboardFilters.materialPrices();     // For material prices
 *    const filterSetup = setupDashboardFilters.stateDOTBudgets();    // For state DOT budgets
 *    const filterSetup = setupDashboardFilters.summary();            // For summary
 *    const filterSetup = setupDashboardFilters.valuePutInPlace();    // For VPIP
 * 
 * 4. Add the filter component to your JSX:
 *    <UniversalDashboardFilters
 *      dashboardType={filterSetup.dashboardType}
 *      onFiltersChange={setDashboardFilters}
 *      variant="horizontal"
 *    />
 * 
 * 5. Use the filters in your widgets:
 *    Pass `dashboardFilters` to your FilteredSisenseWidget components
 * 
 * That's it! Your dashboard now has consistent, professional filters.
 */

/**
 * Advanced Usage Examples:
 */

// Vertical layout
export const VerticalFiltersExample: React.FC = () => {
  const [filters, setFilters] = useState<FilterValue[]>([]);
  
  return (
    <UniversalDashboardFilters
      dashboardType="CONTRACT_AWARDS"
      onFiltersChange={setFilters}
      variant="vertical"  // Stacks filters vertically
    />
  );
};

// Custom styling
export const StyledFiltersExample: React.FC = () => {
  const [filters, setFilters] = useState<FilterValue[]>([]);
  
  return (
    <UniversalDashboardFilters
      dashboardType="FEDERAL_AID"
      onFiltersChange={setFilters}
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        padding: '24px'
      }}
      className="custom-filters"
    />
  );
};

// Multiple filter sections
export const MultiSectionFiltersExample: React.FC = () => {
  const [primaryFilters, setPrimaryFilters] = useState<FilterValue[]>([]);
  const [secondaryFilters, setSecondaryFilters] = useState<FilterValue[]>([]);
  
  const primaryConfig = createQuickFilters([
    {
      id: 'state',
      title: 'State',
      type: 'select',
      options: ['All States', 'California', 'Texas', 'New York']
    }
  ]);
  
  const secondaryConfig = createQuickFilters([
    {
      id: 'search',
      title: 'Search',
      type: 'text',
      placeholder: 'Search projects...'
    }
  ]);
  
  return (
    <div>
      {/* Primary filters */}
      <UniversalDashboardFilters
        customConfig={primaryConfig}
        onFiltersChange={setPrimaryFilters}
        variant="horizontal"
      />
      
      {/* Secondary filters */}
      <UniversalDashboardFilters
        customConfig={secondaryConfig}
        onFiltersChange={setSecondaryFilters}
        variant="horizontal"
        style={{ marginTop: '16px' }}
      />
    </div>
  );
};
