import { useEffect } from 'react';

/**
 * Hook to persistently override Sisense widget styles
 * Monitors for DOM changes and reapplies transparent backgrounds
 */
export const useSisenseStyleOverride = () => {
  useEffect(() => {
    // Function to apply transparent backgrounds to all Sisense widgets
    const applyTransparentStyles = () => {
      // Remove ALL backgrounds from ANY element within widgets
      const removeAllBackgrounds = (element: HTMLElement) => {
        // Remove all background-related styles
        element.style.backgroundColor = 'transparent';
        element.style.background = 'transparent';
        element.style.backgroundImage = 'none';
        element.style.boxShadow = 'none';
        element.style.borderColor = 'transparent';
        
        // Also set via setAttribute to override any inline styles
        element.setAttribute('style', 
          (element.getAttribute('style') || '')
            .replace(/background-color:\s*[^;]+;?/gi, 'background-color: transparent !important;')
            .replace(/background:\s*[^;]+;?/gi, 'background: transparent !important;')
            .replace(/background-image:\s*[^;]+;?/gi, 'background-image: none !important;')
            .replace(/box-shadow:\s*[^;]+;?/gi, 'box-shadow: none !important;')
            .replace(/border:\s*[^;]+;?/gi, 'border: 1px solid transparent !important;')
        );
        
        // Recursively process all children
        Array.from(element.children).forEach(child => {
          removeAllBackgrounds(child as HTMLElement);
        });
      };

      // Target all possible Sisense widget containers and their parents
      const widgetSelectors = [
        '.csdk-widget',
        '.csdk-chart-widget',
        '.csdk-indicator-widget',
        '.csdk-table-widget',
        '.widget-container',
        '.widget-chart',
        '.widget-kpi',
        '.widget-table',
        '.widget-pivot',
        '[class*="widget"]',
        '[class*="sisense"]',
        '[class*="csdk"]',
        '[data-oid]',
        // More specific Sisense internal classes
        '.widget-wrapper',
        '.widget-content',
        '.widget-body',
        '.chart-container',
        '.indicator-container',
        '.numeric-indicator',
        '.gauge-indicator',
        '.indicator-value-container'
      ];

      widgetSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element: Element) => {
          removeAllBackgrounds(element as HTMLElement);
        });
      });

      // Target ALL divs and spans that might contain backgrounds
      const allElements = document.querySelectorAll('div, span, section, article, aside, header, footer');
      allElements.forEach((element: Element) => {
        const el = element as HTMLElement;
        const style = window.getComputedStyle(el);
        
        // Check if element has a non-transparent background
        if (style.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
            style.backgroundColor !== 'transparent' &&
            style.backgroundColor !== '') {
          // Check if it's within a widget
          if (el.closest('.widget-chart') || 
              el.closest('.widget-kpi') || 
              el.closest('[class*="widget"]') || 
              el.closest('[class*="csdk"]') ||
              el.closest('[data-oid*="widget"]')) {
            removeAllBackgrounds(el);
          }
        }
      });

      // Force remove white backgrounds specifically
      const whiteBackgrounds = document.querySelectorAll(
        '[style*="background-color: rgb(255, 255, 255)"], ' +
        '[style*="background-color: white"], ' +
        '[style*="background-color:#fff"], ' +
        '[style*="background-color: #fff"], ' +
        '[style*="background-color:#ffffff"], ' +
        '[style*="background-color: #ffffff"], ' +
        '[style*="background: rgb(255, 255, 255)"], ' +
        '[style*="background: white"], ' +
        '[style*="background:#fff"], ' +
        '[style*="background: #fff"], ' +
        '[style*="background:#ffffff"], ' +
        '[style*="background: #ffffff"]'
      );
      
      whiteBackgrounds.forEach((element: Element) => {
        removeAllBackgrounds(element as HTMLElement);
      });

      // Target indicator value colors
      const indicatorSelectors = [
        '.csdk-indicator-value',
        '.csdk-indicator-widget .value',
        '[class*="indicator"] [class*="value"]',
        '.widget-kpi svg text',
        '[style*="color: rgb(0, 206, 230)"]',
        '[style*="fill: rgb(0, 206, 230)"]'
      ];

      indicatorSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element: Element) => {
          const el = element as HTMLElement;
          el.style.color = '#ff6b35';
          if (el.tagName === 'text' || el.tagName === 'TEXT') {
            el.setAttribute('fill', '#ff6b35');
          }
        });
      });
    };

    // Apply styles immediately
    applyTransparentStyles();

    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver(() => {
      // Always apply styles on any DOM change
      // This is aggressive but ensures backgrounds never stick
      applyTransparentStyles();
    });

    // Start observing the document for changes
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    // Also listen for any Sisense-specific events or filter changes
    const handleFilterChange = () => {
      // Apply multiple times to catch delayed renders
      applyTransparentStyles();
      setTimeout(applyTransparentStyles, 100);
      setTimeout(applyTransparentStyles, 300);
      setTimeout(applyTransparentStyles, 500);
      setTimeout(applyTransparentStyles, 1000);
    };

    // Listen for various events that might trigger re-renders
    window.addEventListener('resize', handleFilterChange);
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      // Check if click was on a filter or similar control
      if (target.closest('[class*="filter"]') || 
          target.closest('[class*="dropdown"]') ||
          target.closest('button') ||
          target.closest('[role="button"]')) {
        handleFilterChange();
      }
    });

    // Aggressive interval to continuously remove backgrounds
    // This ensures they never stick even with delayed renders
    const interval = setInterval(applyTransparentStyles, 1000);

    // Also run more frequently for the first few seconds
    const aggressiveInterval = setInterval(applyTransparentStyles, 100);
    setTimeout(() => clearInterval(aggressiveInterval), 5000);

    // Cleanup
    return () => {
      observer.disconnect();
      window.removeEventListener('resize', handleFilterChange);
      clearInterval(interval);
      clearInterval(aggressiveInterval);
    };
  }, []);
};