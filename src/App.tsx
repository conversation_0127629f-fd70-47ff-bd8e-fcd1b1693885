import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./contexts/ThemeContext";
import { Landing } from "./pages/Landing/Landing";
import { SummaryDashboard } from "./pages/dashboards/SummaryDashboard/SummaryDashboard";
import { SummaryDashboardWithCustomCharts } from "./pages/dashboards/SummaryDashboard/SummaryDashboardWithCustomCharts";
import { ContractAwardsDashboard } from "./pages/dashboards/ContractAwardsDashboard/ContractAwardsDashboard";
import { ContractAwardsDashboardWithCustomCharts } from "./pages/dashboards/ContractAwardsDashboard/ContractAwardsDashboardWithCustomCharts";
import { ValuePutInPlaceDashboard } from "./pages/dashboards/ValuePutInPlaceDashboard/ValuePutInPlaceDashboard";
import { FederalAidDashboard } from "./pages/dashboards/FederalAidDashboard/FederalAidDashboard";
import { MaterialPricesDashboard } from "./pages/dashboards/MaterialPricesDashboard/MaterialPricesDashboard";
import { StateLegislativeInitiativesDashboard } from "./pages/dashboards/StateLegislativeInitiativesDashboard/StateLegislativeInitiativesDashboard";
import { StateDOTBudgetsDashboard } from "./pages/dashboards/StateDOTBudgetsDashboard/StateDOTBudgetsDashboard";
import { DashboardImporter } from "./pages/DashboardImporter/DashboardImporter";
import { DataModelInspectorPage } from "./pages/DataModelInspector";
import { ThemeShowcase } from "./pages/ThemeShowcase/ThemeShowcase";

import "./App.css";

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <Router data-oid="5_c2xna">
      <Routes data-oid="6st2wt4">
        <Route
          path="/"
          element={<Landing data-oid="mhwi6.r" />}
          data-oid="-9bekv7"
        />

        <Route
          path="/import"
          element={<DashboardImporter data-oid="qxjm.xl" />}
          data-oid="s9yd6qr"
        />

        <Route
          path="/data-model-inspector"
          element={<DataModelInspectorPage />}
        />

        <Route
          path="/theme-showcase"
          element={<ThemeShowcase />}
        />

        <Route
          path="/dashboard/summary"
          element={<SummaryDashboard data-oid="eq3ethf" />}
          data-oid="qc6c0ns"
        />

        <Route
          path="/dashboard/summary-custom"
          element={<SummaryDashboardWithCustomCharts data-oid="custom-charts" />}
          data-oid="custom-charts-route"
        />

        <Route
          path="/dashboard/contract-awards"
          element={<ContractAwardsDashboard data-oid="0du.4-s" />}
          data-oid="mr.h6cd"
        />

        <Route
          path="/dashboard/contract-awards-custom"
          element={<ContractAwardsDashboardWithCustomCharts data-oid="contract-awards-custom" />}
          data-oid="contract-awards-custom-route"
        />

        <Route
          path="/dashboard/value-put-in-place"
          element={<ValuePutInPlaceDashboard data-oid="fga_kfo" />}
          data-oid="87ih27g"
        />

        <Route
          path="/dashboard/federal-aid"
          element={<FederalAidDashboard data-oid="gy1v2db" />}
          data-oid="3cyuni:"
        />

        <Route
          path="/dashboard/state-legislative-initiatives"
          element={<StateLegislativeInitiativesDashboard data-oid="_m1jlzw" />}
          data-oid="0aa_e9i"
        />

        <Route
          path="/dashboard/state-dot-budgets"
          element={<StateDOTBudgetsDashboard data-oid="trantus" />}
          data-oid="v6..y_w"
        />

        <Route
          path="/dashboard/material-prices"
          element={<MaterialPricesDashboard data-oid=".oftp03" />}
          data-oid="3piup8s"
        />


       
      </Routes>
      </Router>
    </ThemeProvider>
  );
};

export default App;
