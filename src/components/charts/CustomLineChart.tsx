import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { CustomChart, ChartContainer } from './CustomChart';
import { transformSisenseData, formatValue } from '../../utils/sisenseDataTransform';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface CustomLineChartProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  xAxisKey?: string;
  yAxisKey?: string;
  lineColor?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  formatType?: 'currency' | 'number' | 'percentage';
  strokeWidth?: number;
}

export const CustomLineChart: React.FC<CustomLineChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  xAxisKey = 'category',
  yAxisKey = 'value',
  lineColor = theme.colors.primary,
  showGrid = true,
  showLegend = false,
  formatType = 'number',
  strokeWidth = 3,
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformSisenseData(data, {
      categoryKey: xAxisKey,
      valueKey: yAxisKey,
    });

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    return (
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={transformedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            {showGrid && (
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeOpacity={0.3}
              />
            )}
            <XAxis
              dataKey={xAxisKey}
              axisLine={false}
              tickLine={false}
              tick={{
                fill: theme.colors.text.secondary,
                fontSize: 12,
              }}
              tickFormatter={(value) => {
                if (value instanceof Date) {
                  return value.toLocaleDateString();
                }
                return String(value).length > 10 ? String(value).substring(0, 10) + '...' : String(value);
              }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{
                fill: theme.colors.text.secondary,
                fontSize: 12,
              }}
              tickFormatter={(value) => formatValue(value, formatType)}
            />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: `blur(${theme.blur.md})`,
                WebkitBackdropFilter: `blur(${theme.blur.md})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                boxShadow: theme.shadows.glass,
                color: theme.colors.text.primary,
                fontSize: theme.typography.fontSize.sm,
              }}
              labelStyle={{
                color: theme.colors.text.primary,
                fontWeight: theme.typography.fontWeight.semibold,
              }}
              formatter={(value: any, name: string) => [
                formatValue(Number(value), formatType),
                name
              ]}
            />
            {showLegend && (
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
              />
            )}
            <Line
              type="monotone"
              dataKey={yAxisKey}
              stroke={lineColor}
              strokeWidth={strokeWidth}
              dot={{
                fill: lineColor,
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
                fill: lineColor,
                stroke: 'rgba(255, 255, 255, 0.8)',
                strokeWidth: 2,
              }}
              connectNulls={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={widgetId}
      dashboardId={dashboardId}
      title={title}
      position={position}
    >
      {renderChart}
    </CustomChart>
  );
};

// Multi-line chart variant
interface CustomMultiLineChartProps extends Omit<CustomLineChartProps, 'lineColor'> {
  lines: Array<{
    dataKey: string;
    color: string;
    name?: string;
  }>;
}

export const CustomMultiLineChart: React.FC<CustomMultiLineChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  xAxisKey = 'category',
  lines,
  showGrid = true,
  showLegend = true,
  formatType = 'number',
  strokeWidth = 3,
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformSisenseData(data, {
      categoryKey: xAxisKey,
    });

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    return (
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={transformedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            {showGrid && (
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeOpacity={0.3}
              />
            )}
            <XAxis
              dataKey={xAxisKey}
              axisLine={false}
              tickLine={false}
              tick={{
                fill: theme.colors.text.secondary,
                fontSize: 12,
              }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{
                fill: theme.colors.text.secondary,
                fontSize: 12,
              }}
              tickFormatter={(value) => formatValue(value, formatType)}
            />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: `blur(${theme.blur.md})`,
                WebkitBackdropFilter: `blur(${theme.blur.md})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                boxShadow: theme.shadows.glass,
                color: theme.colors.text.primary,
                fontSize: theme.typography.fontSize.sm,
              }}
              formatter={(value: any, name: string) => [
                formatValue(Number(value), formatType),
                name
              ]}
            />
            {showLegend && (
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
              />
            )}
            {lines.map((line, index) => (
              <Line
                key={line.dataKey}
                type="monotone"
                dataKey={line.dataKey}
                stroke={line.color}
                strokeWidth={strokeWidth}
                name={line.name || line.dataKey}
                dot={{
                  fill: line.color,
                  strokeWidth: 2,
                  r: 4,
                }}
                activeDot={{
                  r: 6,
                  fill: line.color,
                  stroke: 'rgba(255, 255, 255, 0.8)',
                  strokeWidth: 2,
                }}
                connectNulls={false}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={widgetId}
      dashboardId={dashboardId}
      title={title}
      position={position}
    >
      {renderChart}
    </CustomChart>
  );
};
