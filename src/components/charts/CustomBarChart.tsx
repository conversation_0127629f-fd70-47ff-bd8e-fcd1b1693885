import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell,
} from 'recharts';
import { CustomChart, ChartContainer } from './CustomChart';
import { transformSisenseData, formatValue } from '../../utils/sisenseDataTransform';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface CustomBarChartProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  xAxisKey?: string;
  yAxisKey?: string;
  barColor?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  formatType?: 'currency' | 'number' | 'percentage';
  layout?: 'horizontal' | 'vertical';
  gradient?: boolean;
}

export const CustomBarChart: React.FC<CustomBarChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  xAxisKey = 'category',
  yAxisKey = 'value',
  barColor = theme.colors.primary,
  showGrid = true,
  showLegend = false,
  formatType = 'number',
  layout = 'vertical',
  gradient = true,
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformSisenseData(data, {
      categoryKey: xAxisKey,
      valueKey: yAxisKey,
    });

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    // Generate gradient colors for bars
    const colors = transformedData.map((_, index) => {
      const opacity = 0.8 - (index * 0.1);
      return `${barColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
    });

    return (
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={transformedData}
            layout={layout}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            <defs>
              <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={barColor} stopOpacity={0.8} />
                <stop offset="100%" stopColor={barColor} stopOpacity={0.3} />
              </linearGradient>
            </defs>
            
            {showGrid && (
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeOpacity={0.3}
              />
            )}
            
            {layout === 'vertical' ? (
              <>
                <XAxis
                  dataKey={xAxisKey}
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  tickFormatter={(value) => {
                    return String(value).length > 15 ? String(value).substring(0, 15) + '...' : String(value);
                  }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  tickFormatter={(value) => formatValue(value, formatType)}
                />
              </>
            ) : (
              <>
                <XAxis
                  type="number"
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  tickFormatter={(value) => formatValue(value, formatType)}
                />
                <YAxis
                  type="category"
                  dataKey={xAxisKey}
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  width={100}
                  tickFormatter={(value) => {
                    return String(value).length > 12 ? String(value).substring(0, 12) + '...' : String(value);
                  }}
                />
              </>
            )}
            
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: `blur(${theme.blur.md})`,
                WebkitBackdropFilter: `blur(${theme.blur.md})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                boxShadow: theme.shadows.glass,
                color: theme.colors.text.primary,
                fontSize: theme.typography.fontSize.sm,
              }}
              labelStyle={{
                color: theme.colors.text.primary,
                fontWeight: theme.typography.fontWeight.semibold,
              }}
              formatter={(value: any, name: string) => [
                formatValue(Number(value), formatType),
                name
              ]}
            />
            
            {showLegend && (
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
              />
            )}
            
            <Bar
              dataKey={yAxisKey}
              fill={gradient ? 'url(#barGradient)' : barColor}
              radius={[4, 4, 0, 0]}
            >
              {!gradient && transformedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index] || barColor} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={widgetId}
      dashboardId={dashboardId}
      title={title}
      position={position}
    >
      {renderChart}
    </CustomChart>
  );
};

// Stacked bar chart variant
interface CustomStackedBarChartProps extends Omit<CustomBarChartProps, 'barColor'> {
  bars: Array<{
    dataKey: string;
    color: string;
    name?: string;
  }>;
}

export const CustomStackedBarChart: React.FC<CustomStackedBarChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  xAxisKey = 'category',
  bars,
  showGrid = true,
  showLegend = true,
  formatType = 'number',
  layout = 'vertical',
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformSisenseData(data, {
      categoryKey: xAxisKey,
    });

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    return (
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={transformedData}
            layout={layout}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            {showGrid && (
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeOpacity={0.3}
              />
            )}
            
            {layout === 'vertical' ? (
              <>
                <XAxis
                  dataKey={xAxisKey}
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  tickFormatter={(value) => formatValue(value, formatType)}
                />
              </>
            ) : (
              <>
                <XAxis
                  type="number"
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  tickFormatter={(value) => formatValue(value, formatType)}
                />
                <YAxis
                  type="category"
                  dataKey={xAxisKey}
                  axisLine={false}
                  tickLine={false}
                  tick={{
                    fill: theme.colors.text.secondary,
                    fontSize: 12,
                  }}
                  width={100}
                />
              </>
            )}
            
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: `blur(${theme.blur.md})`,
                WebkitBackdropFilter: `blur(${theme.blur.md})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                boxShadow: theme.shadows.glass,
                color: theme.colors.text.primary,
                fontSize: theme.typography.fontSize.sm,
              }}
              formatter={(value: any, name: string) => [
                formatValue(Number(value), formatType),
                name
              ]}
            />
            
            {showLegend && (
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
              />
            )}
            
            {bars.map((bar, index) => (
              <Bar
                key={bar.dataKey}
                dataKey={bar.dataKey}
                stackId="stack"
                fill={bar.color}
                name={bar.name || bar.dataKey}
                radius={index === bars.length - 1 ? [4, 4, 0, 0] : [0, 0, 0, 0]}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={widgetId}
      dashboardId={dashboardId}
      title={title}
      position={position}
    >
      {renderChart}
    </CustomChart>
  );
};
