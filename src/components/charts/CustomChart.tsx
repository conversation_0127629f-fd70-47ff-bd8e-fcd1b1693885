import React from 'react';
import { useExecuteQueryByWidgetId } from '@sisense/sdk-ui';
import { WidgetContainer } from '../layout/WidgetContainer';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface CustomChartProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  children: (data: any, isLoading: boolean, error: any) => React.ReactNode;
  className?: string;
}

export const CustomChart: React.FC<CustomChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  children,
  className
}) => {
  const { data, isLoading, isError, error } = useExecuteQueryByWidgetId({
    widgetId,
    dashboardId,
    includeDashboardFilters: true,
  });

  return (
    <WidgetContainer title={title} position={position} loading={isLoading} error={isError ? error : null}>
      <div
        className={className}
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
        }}
      >
        {isLoading && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: theme.colors.text.secondary,
              fontSize: theme.typography.fontSize.sm,
            }}
          >
            Loading chart data...
          </div>
        )}
        
        {isError && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: theme.colors.error,
              fontSize: theme.typography.fontSize.sm,
              textAlign: 'center',
              padding: theme.spacing.md,
            }}
          >
            <div>
              <div style={{ marginBottom: theme.spacing.sm }}>⚠️</div>
              <div>Unable to load chart data</div>
              {error?.message && (
                <div style={{ 
                  fontSize: theme.typography.fontSize.xs,
                  marginTop: theme.spacing.xs,
                  opacity: 0.7
                }}>
                  {error.message}
                </div>
              )}
            </div>
          </div>
        )}
        
        {!isLoading && !isError && (
          <div style={{ flex: 1, minHeight: 0 }}>
            {children(data, isLoading, error)}
          </div>
        )}
      </div>
    </WidgetContainer>
  );
};

// Chart container with glass styling
export const ChartContainer: React.FC<{ 
  children: React.ReactNode;
  height?: number | string;
}> = ({ children, height = '100%' }) => (
  <div
    style={{
      height,
      width: '100%',
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: `blur(${theme.blur.sm})`,
      WebkitBackdropFilter: `blur(${theme.blur.sm})`,
      borderRadius: theme.borderRadius.md,
      border: `1px solid ${theme.colors.border.glass}`,
      padding: theme.spacing.md,
      position: 'relative',
      overflow: 'hidden',
    }}
  >
    {children}
  </div>
);

// Loading skeleton for charts
export const ChartSkeleton: React.FC<{ height?: number }> = ({ height = 200 }) => (
  <div
    style={{
      height,
      width: '100%',
      background: `linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 25%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0.1) 75%
      )`,
      backgroundSize: '200% 100%',
      animation: 'shimmer 2s infinite',
      borderRadius: theme.borderRadius.md,
    }}
  />
);

// Error fallback component
export const ChartError: React.FC<{ 
  error?: Error;
  onRetry?: () => void;
}> = ({ error, onRetry }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      padding: theme.spacing.lg,
      textAlign: 'center',
    }}
  >
    <div
      style={{
        fontSize: '48px',
        marginBottom: theme.spacing.md,
        opacity: 0.5,
      }}
    >
      📊
    </div>
    <div
      style={{
        color: theme.colors.text.secondary,
        fontSize: theme.typography.fontSize.md,
        marginBottom: theme.spacing.sm,
      }}
    >
      Chart data unavailable
    </div>
    {error?.message && (
      <div
        style={{
          color: theme.colors.text.tertiary,
          fontSize: theme.typography.fontSize.sm,
          marginBottom: theme.spacing.md,
        }}
      >
        {error.message}
      </div>
    )}
    {onRetry && (
      <button
        onClick={onRetry}
        style={{
          background: theme.colors.primary,
          color: 'white',
          border: 'none',
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: theme.typography.fontSize.sm,
          cursor: 'pointer',
          transition: theme.transitions.fast,
        }}
      >
        Retry
      </button>
    )}
  </div>
);
