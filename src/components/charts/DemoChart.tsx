import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { WidgetContainer } from '../layout/WidgetContainer';
import { ChartContainer } from './CustomChart';
import { formatValue } from '../../utils/sisenseDataTransform';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface DemoChartProps {
  title: string;
  position: WidgetPosition;
  type: 'line' | 'bar' | 'pie';
  color: string;
  formatType?: 'currency' | 'number' | 'percentage';
  data?: any[];
}

// Sample data for different chart types
const sampleLineData = [
  { month: 'Jan', value: 8500000000 },
  { month: 'Feb', value: 9200000000 },
  { month: 'Mar', value: 10100000000 },
  { month: 'Apr', value: 11300000000 },
  { month: 'May', value: 10900000000 },
  { month: 'Jun', value: 12100000000 },
  { month: 'Jul', value: 11800000000 },
  { month: 'Aug', value: 10900000000 },
  { month: 'Sep', value: 11500000000 },
  { month: 'Oct', value: 12200000000 },
  { month: 'Nov', value: 10900000000 },
  { month: 'Dec', value: 11700000000 },
];

const sampleBarData = [
  { state: 'California', value: 15200000000 },
  { state: 'Texas', value: 12800000000 },
  { state: 'Florida', value: 8900000000 },
  { state: 'New York', value: 7600000000 },
  { state: 'Pennsylvania', value: 6400000000 },
  { state: 'Illinois', value: 5800000000 },
  { state: 'Ohio', value: 5200000000 },
  { state: 'Georgia', value: 4900000000 },
  { state: 'North Carolina', value: 4600000000 },
  { state: 'Michigan', value: 4200000000 },
];

const samplePieData = [
  { name: 'Highway', value: 45.2, amount: 56200000000 },
  { name: 'Bridge', value: 23.8, amount: 29600000000 },
  { name: 'Transit', value: 18.5, amount: 23000000000 },
  { name: 'Airport', value: 8.3, amount: 10300000000 },
  { name: 'Other', value: 4.2, amount: 5200000000 },
];

const pieColors = [
  '#00cee6',
  '#9b9bd7',
  '#6EDA55',
  '#fc7570',
  '#fbb755',
];

export const DemoChart: React.FC<DemoChartProps> = ({
  title,
  position,
  type,
  color,
  formatType = 'currency',
  data,
}) => {
  const renderChart = () => {
    switch (type) {
      case 'line':
        const lineData = data || sampleLineData;
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={lineData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.2)" strokeOpacity={0.3} />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fill: theme.colors.text.secondary, fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: theme.colors.text.secondary, fontSize: 12 }}
                tickFormatter={(value) => formatValue(value, formatType)}
              />
              <Tooltip
                contentStyle={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: `blur(${theme.blur.md})`,
                  WebkitBackdropFilter: `blur(${theme.blur.md})`,
                  border: `1px solid ${theme.colors.border.glass}`,
                  borderRadius: theme.borderRadius.md,
                  boxShadow: theme.shadows.glass,
                  color: theme.colors.text.primary,
                  fontSize: theme.typography.fontSize.sm,
                }}
                formatter={(value: any) => [formatValue(Number(value), formatType), 'Value']}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={color}
                strokeWidth={3}
                dot={{ fill: color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, fill: color, stroke: 'rgba(255, 255, 255, 0.8)', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'bar':
        const barData = data || sampleBarData;
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={barData} layout="horizontal" margin={{ top: 20, right: 30, left: 80, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.2)" strokeOpacity={0.3} />
              <XAxis
                type="number"
                axisLine={false}
                tickLine={false}
                tick={{ fill: theme.colors.text.secondary, fontSize: 12 }}
                tickFormatter={(value) => formatValue(value, formatType)}
              />
              <YAxis
                type="category"
                dataKey="state"
                axisLine={false}
                tickLine={false}
                tick={{ fill: theme.colors.text.secondary, fontSize: 12 }}
                width={80}
              />
              <Tooltip
                contentStyle={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: `blur(${theme.blur.md})`,
                  WebkitBackdropFilter: `blur(${theme.blur.md})`,
                  border: `1px solid ${theme.colors.border.glass}`,
                  borderRadius: theme.borderRadius.md,
                  boxShadow: theme.shadows.glass,
                  color: theme.colors.text.primary,
                  fontSize: theme.typography.fontSize.sm,
                }}
                formatter={(value: any) => [formatValue(Number(value), formatType), 'Value']}
              />
              <defs>
                <linearGradient id={`barGradient-${color}`} x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="100%" stopColor={color} stopOpacity={0.3} />
                </linearGradient>
              </defs>
              <Bar dataKey="value" fill={`url(#barGradient-${color})`} radius={[0, 4, 4, 0]} />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'pie':
        const pieData = data || samplePieData;
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ value }) => `${value}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeWidth={2}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={pieColors[index % pieColors.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: `blur(${theme.blur.md})`,
                  WebkitBackdropFilter: `blur(${theme.blur.md})`,
                  border: `1px solid ${theme.colors.border.glass}`,
                  borderRadius: theme.borderRadius.md,
                  boxShadow: theme.shadows.glass,
                  color: theme.colors.text.primary,
                  fontSize: theme.typography.fontSize.sm,
                }}
                formatter={(value: any, name: string) => [
                  `${value}% (${formatValue(pieData.find(d => d.name === name)?.amount || 0, formatType)})`,
                  name
                ]}
              />
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return <div>Unsupported chart type</div>;
    }
  };

  return (
    <WidgetContainer title={title} position={position}>
      <ChartContainer>
        {renderChart()}
      </ChartContainer>
    </WidgetContainer>
  );
};
