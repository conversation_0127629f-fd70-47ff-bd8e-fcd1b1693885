import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Toolt<PERSON>,
  Legend,
} from 'recharts';
import { CustomChart, ChartContainer } from './CustomChart';
import { transformPieChartData, formatValue } from '../../utils/sisenseDataTransform';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface CustomPieChartProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  nameKey?: string;
  valueKey?: string;
  colors?: string[];
  showLegend?: boolean;
  showLabels?: boolean;
  formatType?: 'currency' | 'number' | 'percentage';
  innerRadius?: number;
  outerRadius?: number;
}

const DEFAULT_COLORS = [
  theme.colors.primary,
  theme.colors.secondary,
  theme.colors.accent,
  '#6EDA55',
  '#9b9bd7',
  '#fc7570',
  '#fbb755',
  '#218A8C',
];

export const CustomPieChart: React.FC<CustomPieChartProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  nameKey = 'name',
  valueKey = 'value',
  colors = DEFAULT_COLORS,
  showLegend = true,
  showLabels = true,
  formatType = 'number',
  innerRadius = 0,
  outerRadius = 80,
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformPieChartData(data, nameKey, valueKey);

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    const renderCustomLabel = (entry: any) => {
      if (!showLabels) return null;
      
      const percentage = entry.percentage;
      if (percentage < 5) return null; // Don't show labels for small slices
      
      return `${percentage}%`;
    };

    return (
      <ChartContainer>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={transformedData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={outerRadius}
              innerRadius={innerRadius}
              fill="#8884d8"
              dataKey={valueKey}
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth={2}
            >
              {transformedData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={colors[index % colors.length]}
                />
              ))}
            </Pie>
            
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: `blur(${theme.blur.md})`,
                WebkitBackdropFilter: `blur(${theme.blur.md})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                boxShadow: theme.shadows.glass,
                color: theme.colors.text.primary,
                fontSize: theme.typography.fontSize.sm,
              }}
              formatter={(value: any, name: string) => [
                formatValue(Number(value), formatType),
                name
              ]}
              labelFormatter={(label) => `${label}`}
            />
            
            {showLegend && (
              <Legend
                wrapperStyle={{
                  color: theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                }}
                formatter={(value, entry) => (
                  <span style={{ color: theme.colors.text.secondary }}>
                    {value}
                  </span>
                )}
              />
            )}
          </PieChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={widgetId}
      dashboardId={dashboardId}
      title={title}
      position={position}
    >
      {renderChart}
    </CustomChart>
  );
};

// Donut chart variant
export const CustomDonutChart: React.FC<CustomPieChartProps> = (props) => {
  return (
    <CustomPieChart
      {...props}
      innerRadius={40}
      outerRadius={80}
    />
  );
};

// Pie chart with custom center content
interface CustomPieChartWithCenterProps extends CustomPieChartProps {
  centerContent?: React.ReactNode;
}

export const CustomPieChartWithCenter: React.FC<CustomPieChartWithCenterProps> = ({
  centerContent,
  ...props
}) => {
  const renderChart = (data: any) => {
    const transformedData = transformPieChartData(data, props.nameKey, props.valueKey);

    if (transformedData.length === 0) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          No data available
        </div>
      );
    }

    const total = transformedData.reduce((sum, item) => sum + Number(item[props.valueKey || 'value']), 0);

    return (
      <ChartContainer>
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={transformedData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                fill="#8884d8"
                dataKey={props.valueKey || 'value'}
                stroke="rgba(255, 255, 255, 0.2)"
                strokeWidth={2}
              >
                {transformedData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={props.colors?.[index % (props.colors?.length || DEFAULT_COLORS.length)] || DEFAULT_COLORS[index % DEFAULT_COLORS.length]}
                  />
                ))}
              </Pie>
              
              <Tooltip
                contentStyle={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: `blur(${theme.blur.md})`,
                  WebkitBackdropFilter: `blur(${theme.blur.md})`,
                  border: `1px solid ${theme.colors.border.glass}`,
                  borderRadius: theme.borderRadius.md,
                  boxShadow: theme.shadows.glass,
                  color: theme.colors.text.primary,
                  fontSize: theme.typography.fontSize.sm,
                }}
                formatter={(value: any, name: string) => [
                  formatValue(Number(value), props.formatType),
                  name
                ]}
              />
              
              {props.showLegend && (
                <Legend
                  wrapperStyle={{
                    color: theme.colors.text.secondary,
                    fontSize: theme.typography.fontSize.sm,
                  }}
                />
              )}
            </PieChart>
          </ResponsiveContainer>
          
          {/* Center content */}
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              pointerEvents: 'none',
            }}
          >
            {centerContent || (
              <div>
                <div
                  style={{
                    fontSize: theme.typography.fontSize.xl,
                    fontWeight: theme.typography.fontWeight.bold,
                    color: theme.colors.text.primary,
                    marginBottom: theme.spacing.xs,
                  }}
                >
                  {formatValue(total, props.formatType)}
                </div>
                <div
                  style={{
                    fontSize: theme.typography.fontSize.sm,
                    color: theme.colors.text.secondary,
                  }}
                >
                  Total
                </div>
              </div>
            )}
          </div>
        </div>
      </ChartContainer>
    );
  };

  return (
    <CustomChart
      widgetId={props.widgetId}
      dashboardId={props.dashboardId}
      title={props.title}
      position={props.position}
    >
      {renderChart}
    </CustomChart>
  );
};
