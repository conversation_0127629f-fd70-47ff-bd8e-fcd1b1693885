import React from "react";
import { theme } from "../../config/theme.config";

export const BackgroundOrbs: React.FC = () => {
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        overflow: "hidden",
        pointerEvents: "none",
        zIndex: 0,
      }}
    >
      {/* Animated Orb 1 */}
      <div
        style={{
          position: "absolute",
          top: "10%",
          left: "5%",
          width: "400px",
          height: "400px",
          background: `radial-gradient(circle, ${theme.colors.primary}20 0%, transparent 70%)`,
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 20s ease-in-out infinite",
          opacity: 0.5,
        }}
      />
      
      {/* Animated Orb 2 */}
      <div
        style={{
          position: "absolute",
          bottom: "20%",
          right: "10%",
          width: "300px",
          height: "300px",
          background: `radial-gradient(circle, ${theme.colors.secondary}20 0%, transparent 70%)`,
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 15s ease-in-out infinite reverse",
          animationDelay: "5s",
          opacity: 0.5,
        }}
      />
      
      {/* Animated Orb 3 */}
      <div
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "500px",
          height: "500px",
          background: `radial-gradient(circle, ${theme.colors.accent}15 0%, transparent 70%)`,
          borderRadius: "50%",
          filter: "blur(60px)",
          animation: "pulse 10s ease-in-out infinite",
          opacity: 0.4,
        }}
      />
      
      {/* Grid Pattern */}
      <svg
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          opacity: 0.03,
        }}
      >
        <defs>
          <pattern
            id="grid"
            width="40"
            height="40"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 40 0 L 0 0 0 40"
              fill="none"
              stroke={theme.colors.text.primary}
              strokeWidth="1"
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
      
      {/* CSS Animations */}
      <style>
        {`
          @keyframes float {
            0%, 100% {
              transform: translate(0, 0) scale(1);
            }
            25% {
              transform: translate(30px, -30px) scale(1.1);
            }
            50% {
              transform: translate(-20px, 20px) scale(0.9);
            }
            75% {
              transform: translate(20px, -10px) scale(1.05);
            }
          }
          
          @keyframes pulse {
            0%, 100% {
              transform: translate(-50%, -50%) scale(1);
              opacity: 0.4;
            }
            50% {
              transform: translate(-50%, -50%) scale(1.1);
              opacity: 0.6;
            }
          }
        `}
      </style>
    </div>
  );
};