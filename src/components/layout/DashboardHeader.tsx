import React from "react";
import { theme } from "../../config/theme.config";

interface DashboardHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  description,
  actions,
  className = "",
}) => {
  return (
    <div
      className={className}
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "flex-start",
        paddingBottom: theme.spacing.xl,
        borderBottom: `2px solid ${theme.colors.border.light}`,
        marginBottom: theme.spacing.xl,
        background: theme.colors.gradients.surface,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.xl,
        boxShadow: theme.shadows.card,
        position: "relative",
        overflow: "hidden",
        flexDirection: "row",
        gap: theme.spacing.lg,
        flexWrap: "wrap",
      }}
      data-oid="og9epyx"
    >
      {/* Background decoration */}
      <div
        style={{
          position: "absolute",
          top: 0,
          right: 0,
          width: "200px",
          height: "200px",
          background: theme.colors.gradients.primary,
          opacity: 0.05,
          borderRadius: "50%",
          transform: "translate(50%, -50%)",
          pointerEvents: "none",
        }}
        data-oid="ixeno.d"
      />

      <div style={{ flex: 1, zIndex: 1 }} data-oid="x6m54fv">
        <h1
          style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: "36px",
            fontWeight: 800,
            letterSpacing: "-0.8px",
            lineHeight: 1.2,
            background: theme.colors.gradients.primary,
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            backgroundClip: "text",
            marginBottom: theme.spacing.sm,
          }}
          data-oid="257ex:5"
        >
          {title}
        </h1>
        {description && (
          <p
            style={{
              margin: 0,
              color: theme.colors.text.secondary,
              fontSize: "18px",
              lineHeight: 1.6,
              maxWidth: "700px",
              fontWeight: 400,
            }}
            data-oid="hg4vh_0"
          >
            {description}
          </p>
        )}
      </div>

      {actions && (
        <div
          style={{
            display: "flex",
            gap: theme.spacing.md,
            alignItems: "center",
            flexShrink: 0,
            zIndex: 1,
          }}
          data-oid="vq_56vz"
        >
          {actions}
        </div>
      )}
    </div>
  );
};
