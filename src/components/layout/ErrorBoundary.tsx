import React from "react";
import { theme } from "../../config/theme.config";

interface ErrorDisplayProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
  icon?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title = "Something went wrong",
  message = "We encountered an error while loading this content. Please try again.",
  onRetry,
  showRetry = true,
  icon = "⚠️",
}) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: theme.spacing.xxl,
        textAlign: "center",
        background: theme.colors.background.secondary,
        borderRadius: theme.borderRadius.lg,
        border: `1px solid ${theme.colors.border.light}`,
        margin: theme.spacing.lg,
      }}
      data-oid="-05w0fi"
    >
      <div
        style={{
          fontSize: "48px",
          marginBottom: theme.spacing.lg,
          opacity: 0.7,
        }}
        data-oid="lnu544x"
      >
        {icon}
      </div>

      <h3
        style={{
          margin: `0 0 ${theme.spacing.md}px 0`,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.xl,
          fontWeight: theme.typography.fontWeight.semibold,
        }}
        data-oid="mkb2nur"
      >
        {title}
      </h3>

      <p
        style={{
          margin: `0 0 ${theme.spacing.lg}px 0`,
          color: theme.colors.text.secondary,
          fontSize: theme.typography.fontSize.md,
          lineHeight: theme.typography.lineHeight.normal,
          maxWidth: "400px",
        }}
        data-oid="3j76xjg"
      >
        {message}
      </p>

      {showRetry && onRetry && (
        <button
          onClick={onRetry}
          style={{
            background: theme.colors.gradients.primary,
            color: "white",
            border: "none",
            borderRadius: theme.borderRadius.lg,
            padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
            fontSize: theme.typography.fontSize.md,
            fontWeight: theme.typography.fontWeight.semibold,
            cursor: "pointer",
            transition: `all ${theme.transitions.fast}`,
            boxShadow: theme.shadows.md,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = "translateY(-1px)";
            e.currentTarget.style.boxShadow = theme.shadows.lg;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = "translateY(0)";
            e.currentTarget.style.boxShadow = theme.shadows.md;
          }}
          data-oid="0.53z7:"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; onRetry?: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent
            error={this.state.error}
            onRetry={this.handleRetry}
            data-oid=":dwr99l"
          />
        );
      }

      return (
        <ErrorDisplay
          title="Dashboard Error"
          message="An error occurred while rendering the dashboard. Please refresh the page or try again."
          onRetry={this.handleRetry}
          data-oid="cmyiaez"
        />
      );
    }

    return this.props.children;
  }
}
