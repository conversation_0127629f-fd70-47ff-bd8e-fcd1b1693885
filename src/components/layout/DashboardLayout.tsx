import React from "react";
import { theme } from "../../config/theme.config";

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  description,
  actions,
}) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: theme.spacing.xl,
        padding: `${theme.spacing.lg}px`,
        background: theme.colors.backgroundGradient,
        minHeight: "100vh",
        position: "relative",
        overflow: "hidden",
      }}
      data-oid="_hlh3l1"
    >
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.colors.gradients.glow,
          opacity: 0.4,
          pointerEvents: "none",
          zIndex: 0,
        }}
        data-oid="jgooksq"
      />

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          paddingBottom: theme.spacing.lg,
          borderBottom: `1px solid ${theme.colors.border.glass}`,
          position: "relative",
          zIndex: 1,
          background: theme.colors.surface,
          backdropFilter: `blur(${theme.blur.md})`,
          WebkitBackdropFilter: `blur(${theme.blur.md})`,
          borderRadius: theme.borderRadius.lg,
          padding: theme.spacing.xl,
          marginBottom: theme.spacing.md,
          boxShadow: theme.shadows.glass,
        }}
        data-oid="fv5q3qj"
      >
        <div data-oid="gu8roo7">
          <h2
            style={{
              margin: 0,
              color: theme.colors.text.primary,
              fontSize: "32px",
              fontWeight: 700,
              letterSpacing: "-0.5px",
            }}
            data-oid="9e9oc6d"
          >
            {title}
          </h2>
          {description && (
            <p
              style={{
                margin: `${theme.spacing.sm}px 0 0 0`,
                color: theme.colors.text.secondary,
                fontSize: "16px",
                lineHeight: 1.5,
                maxWidth: "600px",
              }}
              data-oid="6jny4rx"
            >
              {description}
            </p>
          )}
        </div>
        {actions && (
          <div
            style={{
              display: "flex",
              gap: theme.spacing.md,
              alignItems: "center",
            }}
            data-oid="mif6iky"
          >
            {actions}
          </div>
        )}
      </div>
      <div
        className="dashboard-grid"
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(12, 1fr)",
          gap: theme.spacing.lg,
          gridAutoRows: "minmax(120px, auto)",
          width: "100%",
          maxWidth: "1400px",
          margin: "0 auto",
          padding: `0 ${theme.spacing.lg}px`,
          position: "relative",
          zIndex: 1,
        }}
        data-oid="i-80o01"
      >
        {children}
      </div>
    </div>
  );
};
