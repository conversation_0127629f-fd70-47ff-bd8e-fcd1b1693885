import React from "react";
import { theme } from "../../config/theme.config";

interface KPIItem {
  id: string;
  title: string;
  value: number | string;
  subtitle?: string;
  format?: "currency" | "number" | "percentage";
  prefix?: string;
  suffix?: string;
  color?: string;
  trend?: {
    value: number;
    direction: "up" | "down" | "neutral";
    label?: string;
  };
  icon?: string;
}

interface KPISectionProps {
  items: KPIItem[];
  title?: string;
  className?: string;
}

export const KPISection: React.FC<KPISectionProps> = ({
  items,
  title,
  className = "",
}) => {
  const formatValue = (item: KPIItem): string => {
    const { value, format, prefix = "", suffix = "" } = item;

    if (typeof value === "string") return `${prefix}${value}${suffix}`;

    switch (format) {
      case "currency":
        return `${prefix}${value.toLocaleString()}${suffix}`;
      case "percentage":
        return `${value}%`;
      case "number":
      default:
        return `${prefix}${value.toLocaleString()}${suffix}`;
    }
  };

  const getTrendIcon = (direction: "up" | "down" | "neutral"): string => {
    switch (direction) {
      case "up":
        return "↗️";
      case "down":
        return "↘️";
      case "neutral":
        return "→";
    }
  };

  const getTrendColor = (direction: "up" | "down" | "neutral"): string => {
    switch (direction) {
      case "up":
        return theme.colors.success;
      case "down":
        return theme.colors.error;
      case "neutral":
        return theme.colors.text.secondary;
    }
  };

  return (
    <div
      className={className}
      style={{ marginBottom: theme.spacing.xl }}
      data-oid="zr:ew:0"
    >
      {title && (
        <h2
          style={{
            margin: `0 0 ${theme.spacing.lg}px 0`,
            color: theme.colors.text.primary,
            fontSize: "24px",
            fontWeight: 700,
            textAlign: "center",
          }}
          data-oid="wzz2sa7"
        >
          {title}
        </h2>
      )}

      <div
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(auto-fit, minmax(280px, 1fr))`,
          gap: theme.spacing.lg,
          marginBottom: theme.spacing.xl,
        }}
        data-oid="mrqhxiq"
      >
        {items.map((item) => (
          <div
            key={item.id}
            style={{
              background: theme.colors.gradients.surface,
              borderRadius: theme.borderRadius.xl,
              padding: theme.spacing.xl,
              boxShadow: theme.shadows.card,
              border: `1px solid ${theme.colors.border.light}`,
              position: "relative",
              overflow: "hidden",
              transition: `all ${theme.transitions.fast}`,
              cursor: "pointer",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = "translateY(-2px)";
              e.currentTarget.style.boxShadow = theme.shadows.cardHover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = "translateY(0)";
              e.currentTarget.style.boxShadow = theme.shadows.card;
            }}
            data-oid="xi-qd1:"
          >
            {/* Background accent */}
            <div
              style={{
                position: "absolute",
                top: 0,
                right: 0,
                width: "100px",
                height: "100px",
                background: item.color || theme.colors.primary,
                opacity: 0.1,
                borderRadius: "50%",
                transform: "translate(30%, -30%)",
                pointerEvents: "none",
              }}
              data-oid="mskiz:."
            />

            {/* Icon */}
            {item.icon && (
              <div
                style={{
                  fontSize: "24px",
                  marginBottom: theme.spacing.sm,
                  opacity: 0.8,
                }}
                data-oid="w2i.saw"
              >
                {item.icon}
              </div>
            )}

            {/* Title */}
            <h3
              style={{
                margin: `0 0 ${theme.spacing.sm}px 0`,
                color: theme.colors.text.secondary,
                fontSize: "14px",
                fontWeight: 600,
                textTransform: "uppercase",
                letterSpacing: "0.5px",
              }}
              data-oid="4f8cla6"
            >
              {item.title}
            </h3>

            {/* Value */}
            <div
              style={{
                fontSize: "32px",
                fontWeight: 800,
                color: item.color || theme.colors.text.primary,
                lineHeight: 1.1,
                marginBottom: theme.spacing.sm,
                fontFamily: theme.typography.fontFamily.sans,
              }}
              data-oid="sg_jdmp"
            >
              {formatValue(item)}
            </div>

            {/* Subtitle */}
            {item.subtitle && (
              <p
                style={{
                  margin: `0 0 ${theme.spacing.sm}px 0`,
                  color: theme.colors.text.secondary,
                  fontSize: "14px",
                  lineHeight: 1.4,
                }}
                data-oid="tygyet8"
              >
                {item.subtitle}
              </p>
            )}

            {/* Trend */}
            {item.trend && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: theme.spacing.xs,
                  marginTop: theme.spacing.sm,
                }}
                data-oid="yzolz.:"
              >
                <span style={{ fontSize: "16px" }} data-oid="w8_yv_w">
                  {getTrendIcon(item.trend.direction)}
                </span>
                <span
                  style={{
                    color: getTrendColor(item.trend.direction),
                    fontSize: "14px",
                    fontWeight: 600,
                  }}
                  data-oid="dgok9oe"
                >
                  {item.trend.value > 0 ? "+" : ""}
                  {item.trend.value}%
                </span>
                {item.trend.label && (
                  <span
                    style={{
                      color: theme.colors.text.secondary,
                      fontSize: "12px",
                    }}
                    data-oid="25h.6sy"
                  >
                    {item.trend.label}
                  </span>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
