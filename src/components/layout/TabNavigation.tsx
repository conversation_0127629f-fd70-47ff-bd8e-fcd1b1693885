import React from "react";
import { theme } from "../../config/theme.config";

interface Tab {
  id: string;
  label: string;
  icon?: string;
  disabled?: boolean;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: "default" | "pills" | "underline";
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  variant = "pills",
  size = "md",
  className = "",
}) => {
  const sizeStyles = {
    sm: {
      padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
      fontSize: "12px",
      gap: "1px",
    },
    md: {
      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
      fontSize: "14px",
      gap: "2px",
    },
    lg: {
      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
      fontSize: "16px",
      gap: "3px",
    },
  };

  const containerStyle = {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    gap: sizeStyles[size].gap,
    backgroundColor:
      variant === "pills" ? theme.colors.background.secondary : "transparent",
    borderRadius: variant === "pills" ? theme.borderRadius.xl : 0,
    padding: variant === "pills" ? theme.spacing.xs : 0,
    margin: `${theme.spacing.xl}px 0`,
    boxShadow: variant === "pills" ? theme.shadows.card : "none",
    borderBottom:
      variant === "underline"
        ? `2px solid ${theme.colors.border.light}`
        : "none",
    position: "relative" as const,
    flexWrap: "wrap" as const,
  };

  const getTabStyle = (tab: Tab, isActive: boolean) => {
    const baseStyle = {
      backgroundColor: "transparent",
      color: isActive ? theme.colors.text.primary : theme.colors.text.secondary,
      border: "none",
      borderRadius: variant === "pills" ? theme.borderRadius.lg : 0,
      padding: sizeStyles[size].padding,
      fontSize: sizeStyles[size].fontSize,
      fontWeight: isActive ? 700 : 500,
      cursor: tab.disabled ? "not-allowed" : "pointer",
      transition: `all ${theme.transitions.fast}`,
      position: "relative" as const,
      display: "flex",
      alignItems: "center",
      gap: theme.spacing.xs,
      opacity: tab.disabled ? 0.5 : 1,
      fontFamily: theme.typography.fontFamily.sans,
      minWidth: "80px",
      justifyContent: "center",
    };

    if (variant === "pills" && isActive) {
      return {
        ...baseStyle,
        background: theme.colors.gradients.primary,
        color: "white",
        boxShadow: theme.shadows.md,
        transform: "translateY(-1px)",
      };
    }

    if (variant === "underline" && isActive) {
      return {
        ...baseStyle,
        borderBottom: `3px solid ${theme.colors.primary}`,
        color: theme.colors.primary,
        fontWeight: 700,
      };
    }

    return baseStyle;
  };

  const getHoverStyle = (tab: Tab, isActive: boolean) => {
    if (tab.disabled || isActive) return {};

    if (variant === "pills") {
      return {
        backgroundColor: theme.colors.background.tertiary,
        transform: "translateY(-1px)",
        boxShadow: theme.shadows.sm,
      };
    }

    if (variant === "underline") {
      return {
        borderBottom: `2px solid ${theme.colors.primary}`,
        color: theme.colors.primary,
      };
    }

    return {
      backgroundColor: theme.colors.background.tertiary,
    };
  };

  return (
    <div className={className} style={containerStyle} data-oid="fhd.ybv">
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onTabChange(tab.id)}
            style={getTabStyle(tab, isActive)}
            onMouseEnter={(e) => {
              const hoverStyle = getHoverStyle(tab, isActive);
              Object.assign(e.currentTarget.style, hoverStyle);
            }}
            onMouseLeave={(e) => {
              Object.assign(e.currentTarget.style, getTabStyle(tab, isActive));
            }}
            disabled={tab.disabled}
            role="tab"
            aria-selected={isActive}
            aria-disabled={tab.disabled}
            data-oid="b5nkqo-"
          >
            {tab.icon && (
              <span style={{ fontSize: "16px" }} data-oid="7ru.3j2">
                {tab.icon}
              </span>
            )}
            {tab.label}
          </button>
        );
      })}
    </div>
  );
};
