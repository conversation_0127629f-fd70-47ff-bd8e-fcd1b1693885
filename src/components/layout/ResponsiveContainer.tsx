import React from "react";
import { theme } from "../../config/theme.config";

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: string;
  padding?: "none" | "sm" | "md" | "lg" | "xl";
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = "",
  maxWidth = "1400px",
  padding = "lg",
}) => {
  const paddingMap = {
    none: "0",
    sm: theme.spacing.sm,
    md: theme.spacing.md,
    lg: theme.spacing.lg,
    xl: theme.spacing.xl,
  };

  return (
    <div
      className={className}
      style={{
        width: "100%",
        maxWidth,
        margin: "0 auto",
        padding: `0 ${paddingMap[padding]}px`,
        // Responsive padding
        "@media (max-width: 768px)": {
          padding: `0 ${theme.spacing.md}px`,
        },
        "@media (max-width: 480px)": {
          padding: `0 ${theme.spacing.sm}px`,
        },
      }}
      data-oid="7e3rj6e"
    >
      {children}
    </div>
  );
};
