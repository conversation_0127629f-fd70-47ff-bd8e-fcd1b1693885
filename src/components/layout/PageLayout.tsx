import React, { useState } from "react";
import { theme } from "../../config/theme.config";
import { Sidebar } from "../navigation/Sidebar";
import { BackgroundOrbs } from "../visual/BackgroundOrbs";

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
  showSidebar?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  showHeader = true,
  showSidebar = true,
}) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  return (
    <>
      {showSidebar && (
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          data-oid="mdq749_"
        />
      )}
      <div
        style={{
          minHeight: "100vh",
          background: theme.colors.backgroundGradient,
          display: "flex",
          flexDirection: "column",
          position: "relative",
          overflow: "hidden",
          marginLeft: showSidebar ? (isSidebarCollapsed ? "80px" : "280px") : 0,
          transition: theme.transitions.normal,
        }}
        data-oid="3xv0vs0"
      >
        <BackgroundOrbs data-oid="k1g:ca3" />
        {showHeader && (
          <header
            style={{
              background: theme.colors.surfaceElevated,
              backdropFilter: `blur(${theme.blur.xl})`,
              WebkitBackdropFilter: `blur(${theme.blur.xl})`,
              boxShadow: theme.shadows.glass,
              padding: `${theme.spacing.lg}px ${theme.spacing.xl}px`,
              position: "sticky",
              top: 0,
              zIndex: 50,
              borderBottom: `1px solid ${theme.colors.border.glass}`,
            }}
            data-oid="pjtw1lj"
          >
            <div
              style={{
                maxWidth: "1400px",
                margin: "0 auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                gap: theme.spacing.xl,
              }}
              data-oid="kvy.kx4"
            >
              {/* Search Bar */}
              <div
                style={{
                  flex: 1,
                  maxWidth: "600px",
                  position: "relative",
                }}
                data-oid="1.0tmbl"
              >
                <input
                  type="text"
                  placeholder="Search..."
                  style={{
                    width: "100%",
                    padding: `${theme.spacing.md}px ${theme.spacing.xl}px ${theme.spacing.md}px ${theme.spacing.xxl + 20}px`,
                    background: theme.colors.surface,
                    backdropFilter: `blur(${theme.blur.md})`,
                    WebkitBackdropFilter: `blur(${theme.blur.md})`,
                    border: `1px solid ${theme.colors.border.glass}`,
                    borderRadius: theme.borderRadius.lg,
                    fontSize: theme.typography.fontSize.sm,
                    color: theme.colors.text.primary,
                    outline: "none",
                    transition: theme.transitions.fast,
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = theme.colors.primary;
                    e.currentTarget.style.boxShadow = `0 0 0 3px ${theme.colors.primary}20`;
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor =
                      theme.colors.border.glass;
                    e.currentTarget.style.boxShadow = "none";
                  }}
                  data-oid="jg:l9je"
                />

                <span
                  style={{
                    position: "absolute",
                    left: theme.spacing.lg,
                    top: "50%",
                    transform: "translateY(-50%)",
                    color: theme.colors.text.tertiary,
                    fontSize: "18px",
                  }}
                  data-oid="5kg2nl4"
                >
                  🔍
                </span>
              </div>

              {/* Header Actions */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: theme.spacing.lg,
                }}
                data-oid="5dfwl.-"
              >
                {/* Notifications */}
                <div
                  style={{
                    position: "relative",
                    cursor: "pointer",
                  }}
                  data-oid="23y:1d6"
                >
                  <button
                    style={{
                      width: 40,
                      height: 40,
                      borderRadius: theme.borderRadius.lg,
                      background: theme.colors.surface,
                      backdropFilter: `blur(${theme.blur.sm})`,
                      WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                      border: `1px solid ${theme.colors.border.glass}`,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      transition: theme.transitions.fast,
                      fontSize: "18px",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background =
                        theme.colors.surfaceElevated;
                      e.currentTarget.style.transform = "scale(1.05)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = theme.colors.surface;
                      e.currentTarget.style.transform = "scale(1)";
                    }}
                    data-oid="o_aogld"
                  >
                    🔔
                  </button>
                  <span
                    style={{
                      position: "absolute",
                      top: -4,
                      right: -4,
                      width: 18,
                      height: 18,
                      background: theme.colors.error,
                      borderRadius: "50%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontSize: "10px",
                      fontWeight: theme.typography.fontWeight.bold,
                      border: `2px solid ${theme.colors.background.primary}`,
                    }}
                    data-oid="k9n6d__"
                  >
                    3
                  </span>
                </div>

                {/* User Profile */}
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: theme.spacing.md,
                    padding: `${theme.spacing.sm}px ${theme.spacing.md}px ${theme.spacing.sm}px ${theme.spacing.sm}px`,
                    background: theme.colors.surface,
                    backdropFilter: `blur(${theme.blur.sm})`,
                    WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                    borderRadius: theme.borderRadius.full,
                    border: `1px solid ${theme.colors.border.glass}`,
                    cursor: "pointer",
                    transition: theme.transitions.fast,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background =
                      theme.colors.surfaceElevated;
                    e.currentTarget.style.transform = "scale(1.02)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = theme.colors.surface;
                    e.currentTarget.style.transform = "scale(1)";
                  }}
                  data-oid="7m3_o-f"
                >
                  <div
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      background: theme.colors.gradients.primary,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontSize: theme.typography.fontSize.sm,
                      fontWeight: theme.typography.fontWeight.semibold,
                    }}
                    data-oid="-9_xs6b"
                  >
                    JD
                  </div>
                  <span
                    style={{
                      fontSize: theme.typography.fontSize.sm,
                      color: theme.colors.text.secondary,
                    }}
                    data-oid="3wj0a6_"
                  >
                    ▼
                  </span>
                </div>
              </div>
            </div>
          </header>
        )}
        <main
          style={{
            flex: 1,
            padding: `${theme.spacing.xl}px ${theme.spacing.lg}px`,
            maxWidth: "1400px",
            margin: "0 auto",
            width: "100%",
          }}
          data-oid="526q8if"
        >
          {children}
        </main>
      </div>
    </>
  );
};
