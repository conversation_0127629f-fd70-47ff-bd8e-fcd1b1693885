import React from "react";
import { theme } from "../../config/theme.config";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  color?: string;
  message?: string;
  fullScreen?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  color = theme.colors.primary,
  message = "Loading...",
  fullScreen = false,
}) => {
  const sizeMap = {
    sm: 24,
    md: 40,
    lg: 64,
  };

  const spinnerSize = sizeMap[size];

  const spinnerStyle = {
    width: `${spinnerSize}px`,
    height: `${spinnerSize}px`,
    border: `3px solid ${theme.colors.border.light}`,
    borderTop: `3px solid ${color}`,
    borderRadius: "50%",
    animation: "spin 1s linear infinite",
  };

  const containerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    justifyContent: "center",
    gap: theme.spacing.md,
    ...(fullScreen && {
      position: "fixed" as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      backdropFilter: "blur(4px)",
      zIndex: 9999,
    }),
    ...(!fullScreen && {
      padding: theme.spacing.xl,
    }),
  };

  return (
    <>
      {/* CSS Animation */}
      <style data-oid="s1rqn-_">
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>

      <div style={containerStyle} data-oid="p1wuec:">
        <div style={spinnerStyle} data-oid="x4hu7io" />
        {message && (
          <p
            style={{
              margin: 0,
              color: theme.colors.text.secondary,
              fontSize: theme.typography.fontSize.md,
              fontWeight: theme.typography.fontWeight.medium,
              textAlign: "center",
            }}
            data-oid="h:qb591"
          >
            {message}
          </p>
        )}
      </div>
    </>
  );
};
