import React, { Component, ErrorInfo, ReactNode } from "react";
import { WidgetById } from "@sisense/sdk-ui";
import { WidgetContainer } from "../../layout/WidgetContainer";
import { createWidgetStylesByType } from "../../../utils/widgetStyling";
import type { WidgetPosition } from "../../../types/dashboard.types";
import { theme } from "../../../config/theme.config";

interface SisenseWidgetProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  includeDashboardFilters?: boolean;
  widgetType?: 'chart' | 'kpi' | 'table' | 'pivot';
  styleOptions?: {
    height?: number;
    width?: number;
    [key: string]: unknown;
  };
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class WidgetErrorBoundary extends Component<
  { children: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Widget Error:", error, errorInfo);
    // Check if it's the CRS error
    if (error.message?.includes("CRS")) {
      console.error("Map-related CRS error detected in widget");
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            width: "100%",
            padding: theme.spacing.xl,
            textAlign: "center",
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
            minHeight: "200px",
          }}
          data-oid="jbj054b"
        >
          <div
            style={{
              fontSize: "48px",
              marginBottom: theme.spacing.md,
              opacity: 0.6,
            }}
            data-oid="5cdwxn8"
          >
            🚧
          </div>
          <div
            style={{
              fontSize: theme.typography.fontSize.lg,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.sm,
            }}
            data-oid="zfj:t.v"
          >
            Coming Soon
          </div>
          <div
            style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              maxWidth: "300px",
              lineHeight: theme.typography.lineHeight.relaxed,
            }}
            data-oid="hgmojyu"
          >
            This widget is currently being updated and will be available soon.
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export const SisenseWidget: React.FC<SisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  includeDashboardFilters = true,
  widgetType = 'chart',
  styleOptions,
}) => {
  // Calculate dynamic height based on widget position
  const defaultHeight = position.h * 120; // Approximate height per grid row

  // Create widget style options using the utility function
  const widgetStyleOptions = createWidgetStylesByType(
    theme,
    widgetType,
    defaultHeight,
    styleOptions // Apply any custom overrides
  );

  return (
    <WidgetContainer title={title} position={position} widgetType={widgetType} data-oid="bkb4_7z">
      <WidgetErrorBoundary data-oid="c8:hac1">
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
          data-oid="widget-wrapper"
        >
          <style>
            {`
              /* Hide Sisense widget borders and containers */
              [data-oid="widget-wrapper"] .widget-container,
              [data-oid="widget-wrapper"] .sisense-widget,
              [data-oid="widget-wrapper"] .widget-content,
              [data-oid="widget-wrapper"] .chart-container,
              [data-oid="widget-wrapper"] .highcharts-container,
              [data-oid="widget-wrapper"] .widget-wrapper {
                border: none !important;
                box-shadow: none !important;
                background: transparent !important;
              }

              /* Hide specific Sisense border elements */
              [data-oid="widget-wrapper"] .widget-border,
              [data-oid="widget-wrapper"] .widget-frame,
              [data-oid="widget-wrapper"] .sisense-border {
                border: none !important;
                outline: none !important;
              }
            `}
          </style>
          <WidgetById
            widgetOid={widgetId}
            dashboardOid={dashboardId}
            includeDashboardFilters={includeDashboardFilters}
            styleOptions={widgetStyleOptions}
            data-oid="namlriz"
          />
        </div>
      </WidgetErrorBoundary>
    </WidgetContainer>
  );
};
