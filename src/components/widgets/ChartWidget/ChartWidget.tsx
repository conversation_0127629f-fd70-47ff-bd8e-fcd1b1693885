import React from "react";
import { ChartWidget as SisenseChartWidget } from "@sisense/sdk-ui";
import type { ChartType } from "@sisense/sdk-ui";
import type { ChartWidgetProps } from "./ChartWidget.types";
import { WidgetContainer } from "../../layout/WidgetContainer";

export const ChartWidget: React.FC<ChartWidgetProps> = ({
  id,
  title,
  position,
  chartType,
  dataOptions,
  dataSource,
  filters,
  highlights,
  onDataPointClick,
  ...rest
}) => {
  return (
    <WidgetContainer title={title} position={position} data-oid="7elg3j8">
      <SisenseChartWidget
        chartType={chartType as ChartType}
        dataOptions={dataOptions}
        dataSource={dataSource}
        filters={filters}
        highlights={highlights}
        onDataPointClick={onDataPointClick}
        {...rest}
        data-oid="fb4uasg"
      />
    </WidgetContainer>
  );
};
