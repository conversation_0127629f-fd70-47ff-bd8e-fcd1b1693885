import type { ChartDataOptions, ChartType } from '@sisense/sdk-ui';
import type { WidgetPosition } from '../../../types/dashboard.types';

export interface ChartWidgetProps {
  id: string;
  title?: string;
  position: WidgetPosition;
  chartType: ChartType | string;
  dataOptions: ChartDataOptions;
  dataSource?: any;
  filters?: any[];
  highlights?: any[];
  onDataPointClick?: (point: any, event: any) => void;
}