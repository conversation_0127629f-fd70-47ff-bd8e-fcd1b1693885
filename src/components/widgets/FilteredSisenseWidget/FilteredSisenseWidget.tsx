import React, { Component, ErrorInfo, ReactNode, useMemo } from "react";
import { WidgetById } from "@sisense/sdk-ui";
import { filterFactory } from "@sisense/sdk-data";
import { WidgetContainer } from "../../layout/WidgetContainer";
import type { WidgetPosition } from "../../../types/dashboard.types";
import { theme } from "../../../config/theme.config";
import { DM } from "../../../config/sisense.config";
import type { FilterValue } from "../../filters/UniversalDashboardFilters";

interface FilteredSisenseWidgetProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  includeDashboardFilters?: boolean;
  widgetType?: 'chart' | 'kpi' | 'table' | 'pivot';
  styleOptions?: {
    height?: number;
    width?: number;
    [key: string]: unknown;
  };
  // Additional filters to apply to this widget
  filters?: FilterValue[];
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class WidgetErrorBoundary extends Component<
  { children: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Widget Error:", error, errorInfo);
    if (error.message?.includes("CRS")) {
      console.error("Map-related CRS error detected in widget");
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            width: "100%",
            padding: theme.spacing.xl,
            textAlign: "center",
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
            minHeight: "200px",
          }}
        >
          <div
            style={{
              fontSize: "48px",
              marginBottom: theme.spacing.md,
              opacity: 0.6,
            }}
          >
            🚧
          </div>
          <div
            style={{
              fontSize: theme.typography.fontSize.lg,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.sm,
            }}
          >
            Coming Soon
          </div>
          <div
            style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              maxWidth: "300px",
              lineHeight: theme.typography.lineHeight.relaxed,
            }}
          >
            This widget is currently being updated and will be available soon.
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export const FilteredSisenseWidget: React.FC<FilteredSisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  includeDashboardFilters = true,
  widgetType = 'chart',
  styleOptions,
  filters = [],
}) => {
  // Calculate dynamic height based on widget position
  const defaultHeight = position.h * 120;

  const widgetStyleOptions = {
    height: defaultHeight,
    width: '100%',
    ...styleOptions,
  };

  // Convert custom filters to Sisense filter format
  const sisenseFilters = useMemo(() => {
    if (filters.length === 0) return undefined;

    console.log('Dashboard filters applied:', filters);

    // Convert FilterValue[] to Sisense filters using the correct data model
    try {
      return filters.map(filter => {
        // Handle both old format (filter.id) and new format (filter.id)
        const filterId = filter.id;
        const filterValue = filter.value;

        switch (filterId) {
          case 'state-filter':
            if (filterValue && filterValue !== 'All States') {
              return filterFactory.members(DM.ARTBAEconomics.Awards.State, [filterValue]);
            }
            return null;
          case 'mode-filter':
            if (filterValue && filterValue !== 'All Modes') {
              return filterFactory.members(DM.ARTBAEconomics.ExecutiveSummary.Mode, [filterValue]);
            }
            return null;
          case 'year-filter':
            if (filterValue && filterValue !== 'All Years') {
              // Convert year to date range
              const startDate = `${filterValue}-01-01`;
              const endDate = `${filterValue}-12-31`;
              return filterFactory.dateRange(DM.ARTBAEconomics.ExecutiveSummary.MonthStart, startDate, endDate);
            }
            return null;
          // Add support for new filter types
          case 'region-filter':
          case 'category-filter':
          case 'program-filter':
          case 'material-filter':
          case 'amount-filter':
            // These would need proper data model mappings
            console.log(`Filter ${filterId} not yet mapped to data model:`, filterValue);
            return null;
          default:
            console.log(`Unknown filter type: ${filterId}`, filterValue);
            return null;
        }
      }).filter(Boolean);
    } catch (error) {
      console.warn('Error applying custom filters, falling back to dashboard filters:', error);
      return undefined;
    }
  }, [filters]);

  return (
    <WidgetContainer title={title} position={position} widgetType={widgetType}>
      <WidgetErrorBoundary>
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <WidgetById
            widgetOid={widgetId}
            dashboardOid={dashboardId}
            includeDashboardFilters={includeDashboardFilters}
            styleOptions={widgetStyleOptions}
            // Apply additional filters if provided
            filters={sisenseFilters}
          />
        </div>
      </WidgetErrorBoundary>
    </WidgetContainer>
  );
};
