import React from 'react';
import { WidgetById } from '@sisense/sdk-ui';
import { useTheme } from '../../hooks/useTheme';
import { createWidgetStyleOptions, widgetStylePresets } from '../../utils/widgetStyling';

/**
 * Example component showing different widget styling approaches
 * This demonstrates the various ways to style Sisense widgets
 */
export const StyledWidgetExamples: React.FC = () => {
  const { theme } = useTheme();

  // Example dashboard and widget IDs (replace with your actual IDs)
  const dashboardId = "5defb5024faf0a20b09a2141";
  const widgetId = "5df106fc1d1da61c08f09137";

  return (
    <div style={{ 
      padding: theme.spacing.xl,
      display: 'grid',
      gap: theme.spacing.xl,
      gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))'
    }}>
      
      {/* 1. Basic Styled Widget */}
      <div style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Basic Styled Widget
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={{
            height: 300,
            width: '100%',
            backgroundColor: 'transparent',
            border: 'none',
            seriesColors: [theme.colors.primary, theme.colors.secondary],
          }}
        />
      </div>

      {/* 2. Enhanced Glass Effect Widget */}
      <div style={{
        background: `linear-gradient(135deg, ${theme.colors.surface}, ${theme.colors.surfaceElevated})`,
        backdropFilter: `blur(${theme.blur.xl})`,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
        boxShadow: theme.shadows.glass,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Enhanced Glass Effect
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={widgetStylePresets.glassEnhanced(theme)}
        />
      </div>

      {/* 3. Custom Chart Colors Widget */}
      <div style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Custom Chart Colors
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={createWidgetStyleOptions(theme, {
            height: 300,
            seriesColors: [
              '#ff6b35', // Orange
              '#00cee6', // Cyan
              '#6EDA55', // Green
              '#9b9bd7', // Purple
              '#ff4757', // Red
            ],
            chart: {
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              plotBackgroundColor: 'rgba(255, 107, 53, 0.05)',
            }
          })}
        />
      </div>

      {/* 4. Minimal Style Widget */}
      <div style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Minimal Style
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={widgetStylePresets.minimal(theme)}
        />
      </div>

      {/* 5. Custom Axis and Grid Styling */}
      <div style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Custom Axis & Grid
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={createWidgetStyleOptions(theme, {
            height: 300,
            xAxis: {
              enabled: true,
              gridLines: true,
              lineColor: theme.colors.primary,
              labels: {
                color: theme.colors.primary,
                fontSize: '14px',
                fontWeight: 'bold',
              }
            },
            yAxis: {
              enabled: true,
              gridLines: true,
              gridLineColor: `${theme.colors.secondary}30`,
              lineColor: theme.colors.secondary,
              labels: {
                color: theme.colors.secondary,
                fontSize: '14px',
                fontWeight: 'bold',
              }
            },
          })}
        />
      </div>

      {/* 6. Custom Tooltip Styling */}
      <div style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.lg,
        border: `1px solid ${theme.colors.border.glass}`,
      }}>
        <h3 style={{ 
          color: theme.colors.text.primary,
          marginBottom: theme.spacing.md,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          Custom Tooltip
        </h3>
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          styleOptions={createWidgetStyleOptions(theme, {
            height: 300,
            tooltip: {
              backgroundColor: theme.colors.primary,
              borderRadius: '20px',
              borderColor: theme.colors.primaryDark,
              borderWidth: 2,
              shadow: true,
              style: {
                color: 'white',
                fontSize: '14px',
                fontWeight: 'bold',
              }
            }
          })}
        />
      </div>

    </div>
  );
};

/**
 * Example of how to create a custom styled widget wrapper
 */
export const CustomStyledWidget: React.FC<{
  widgetId: string;
  dashboardId: string;
  title: string;
  variant?: 'default' | 'glass' | 'minimal' | 'dark';
}> = ({ widgetId, dashboardId, title, variant = 'default' }) => {
  const { theme } = useTheme();

  const getStyleOptions = () => {
    switch (variant) {
      case 'glass':
        return widgetStylePresets.glassEnhanced(theme);
      case 'minimal':
        return widgetStylePresets.minimal(theme);
      case 'dark':
        return widgetStylePresets.darkMode(theme);
      default:
        return createWidgetStyleOptions(theme);
    }
  };

  return (
    <div style={{
      background: variant === 'glass' 
        ? `linear-gradient(135deg, ${theme.colors.surface}, ${theme.colors.surfaceElevated})`
        : theme.colors.surface,
      backdropFilter: variant === 'glass' ? `blur(${theme.blur.xl})` : 'none',
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing.lg,
      border: `1px solid ${theme.colors.border.glass}`,
      boxShadow: variant === 'glass' ? theme.shadows.glass : theme.shadows.sm,
    }}>
      <h3 style={{ 
        color: theme.colors.text.primary,
        marginBottom: theme.spacing.md,
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.semibold,
      }}>
        {title}
      </h3>
      <WidgetById
        widgetOid={widgetId}
        dashboardOid={dashboardId}
        styleOptions={getStyleOptions()}
      />
    </div>
  );
};
