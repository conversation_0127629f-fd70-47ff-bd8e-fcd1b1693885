import React from "react";
import { Table } from "@sisense/sdk-ui";
import { WidgetContainer } from "../../layout/WidgetContainer";
import type { WidgetPosition } from "../../../types/dashboard.types";
import type { TableDataOptions } from "@sisense/sdk-ui";

interface TableWidgetProps {
  id: string;
  title?: string;
  position: WidgetPosition;
  dataOptions: TableDataOptions;
  dataSet?: any;
  filters?: any[];
}

export const TableWidget: React.FC<TableWidgetProps> = ({
  id,
  title,
  position,
  dataOptions,
  dataSet,
  filters,
}) => {
  return (
    <WidgetContainer title={title} position={position} data-oid="rmwnsvm">
      <Table
        dataOptions={dataOptions}
        dataSet={dataSet}
        filters={filters}
        data-oid="4jm86js"
      />
    </WidgetContainer>
  );
};
