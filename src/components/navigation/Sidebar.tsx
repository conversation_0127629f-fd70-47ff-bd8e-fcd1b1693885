import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTheme } from "../../hooks/useTheme";
import {
  Home,
  BarChart3,
  FileText,
  Palette,
  Building2,
  Landmark,
  ScrollText,
  DollarSign,
  TrendingUp,
  Download,
  Settings,
  ChevronRight,
  Menu,
  X
} from "lucide-react";

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path: string;
  badge?: number;
  subItems?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    id: "home",
    label: "Home",
    icon: Home,
    path: "/",
  },
  {
    id: "summary",
    label: "Summary Dashboard",
    icon: BarChart3,
    path: "/dashboard/summary",
  },
  {
    id: "contract-awards",
    label: "Contract Awards",
    icon: FileText,
    path: "/dashboard/contract-awards",
  },
  {
    id: "contract-awards-custom",
    label: "Contract Awards (Custom)",
    icon: Palette,
    path: "/dashboard/contract-awards-custom",
  },
  {
    id: "value-put-in-place",
    label: "Value Put in Place",
    icon: Building2,
    path: "/dashboard/value-put-in-place",
  },
  {
    id: "federal-aid",
    label: "Federal-Aid Obligations",
    icon: Landmark,
    path: "/dashboard/federal-aid",
  },
  {
    id: "state-legislative",
    label: "State Legislative Initiatives",
    icon: ScrollText,
    path: "/dashboard/state-legislative-initiatives",
  },
  {
    id: "state-dot-budgets",
    label: "State DOT Budgets 2025",
    icon: DollarSign,
    path: "/dashboard/state-dot-budgets",
  },
  {
    id: "material-prices",
    label: "Material Prices",
    icon: TrendingUp,
    path: "/dashboard/material-prices",
  },
  {
    id: "import",
    label: "Import Dashboard",
    icon: Download,
    path: "/import",
  },
  {
    id: "theme-showcase",
    label: "Theme Settings",
    icon: Settings,
    path: "/theme-showcase",
  },

];

interface SidebarProps {
  isCollapsed?: boolean;
  onToggle?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed = false, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme } = useTheme();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]
    );
  };

  const isActive = (path: string) => location.pathname === path;

  const renderSidebarItem = (item: SidebarItem, depth = 0) => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.path);
    const IconComponent = item.icon;

    return (
      <div key={item.id} style={{ position: "relative" }}>
        <div
          onClick={() => {
            if (hasSubItems) {
              toggleExpanded(item.id);
            } else {
              navigate(item.path);
            }
          }}
          onMouseEnter={() => setHoveredItem(item.id)}
          onMouseLeave={() => setHoveredItem(null)}
          style={{
            display: "flex",
            alignItems: "center",
            padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
            marginLeft: depth * theme.spacing.lg,
            marginBottom: theme.spacing.xs,
            borderRadius: theme.borderRadius.xl,
            cursor: "pointer",
            transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
            background: active
              ? `linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.primary}08)`
              : "transparent",
            backdropFilter: active ? `blur(${theme.blur.md})` : "none",
            WebkitBackdropFilter: active ? `blur(${theme.blur.md})` : "none",
            border: `1px solid ${active ? `${theme.colors.primary}30` : "transparent"}`,
            position: "relative",
            overflow: "hidden",
            boxShadow: active ? `0 2px 8px ${theme.colors.primary}20` : "none",
          }}
          onMouseEnter={(e) => {
            if (!active) {
              e.currentTarget.style.background = `${theme.colors.surfaceElevated}80`;
              e.currentTarget.style.backdropFilter = `blur(${theme.blur.sm})`;
              e.currentTarget.style.WebkitBackdropFilter = `blur(${theme.blur.sm})`;
              e.currentTarget.style.border = `1px solid ${theme.colors.border.glass}`;
              e.currentTarget.style.transform = "translateX(4px)";
            }
          }}
          onMouseLeave={(e) => {
            if (!active) {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.backdropFilter = "none";
              e.currentTarget.style.WebkitBackdropFilter = "none";
              e.currentTarget.style.border = "1px solid transparent";
              e.currentTarget.style.transform = "translateX(0px)";
            }
          }}
        >
          {active && (
            <div
              style={{
                position: "absolute",
                left: 0,
                top: "50%",
                transform: "translateY(-50%)",
                width: "3px",
                height: "60%",
                background: `linear-gradient(180deg, ${theme.colors.primary}, ${theme.colors.primaryDark})`,
                borderRadius: "0 2px 2px 0",
                boxShadow: `0 0 8px ${theme.colors.primary}60`,
              }}
            />
          )}
          
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "40px",
              height: "40px",
              marginRight: isCollapsed ? 0 : theme.spacing.md,
              borderRadius: theme.borderRadius.lg,
              background: active
                ? `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryDark})`
                : `${theme.colors.surfaceElevated}60`,
              color: active ? "white" : theme.colors.text.secondary,
              transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
              boxShadow: active
                ? `0 4px 12px ${theme.colors.primary}40`
                : `0 2px 4px ${theme.colors.text.secondary}20`,
            }}
          >
            <IconComponent size={20} />
          </div>
          
          {!isCollapsed && (
            <div style={{ flex: 1, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <span
                style={{
                  color: active ? theme.colors.text.primary : theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: active ? theme.typography.fontWeight.semibold : theme.typography.fontWeight.medium,
                  transition: "all 0.2s ease",
                  letterSpacing: "0.01em",
                }}
              >
                {item.label}
              </span>
              
              <div style={{ display: "flex", alignItems: "center", gap: theme.spacing.sm }}>
                {item.badge && (
                  <div
                    style={{
                      background: `linear-gradient(135deg, ${theme.colors.accent}, ${theme.colors.accentLight})`,
                      color: "white",
                      fontSize: theme.typography.fontSize.xs,
                      fontWeight: theme.typography.fontWeight.bold,
                      padding: `3px 8px`,
                      borderRadius: theme.borderRadius.full,
                      minWidth: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: `0 2px 4px ${theme.colors.accent}40`,
                    }}
                  >
                    {item.badge}
                  </div>
                )}

                {hasSubItems && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: "20px",
                      height: "20px",
                      transform: isExpanded ? "rotate(90deg)" : "rotate(0deg)",
                      transition: "transform 0.2s ease",
                      color: theme.colors.text.secondary,
                    }}
                  >
                    <ChevronRight size={16} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Tooltip for collapsed state */}
        {isCollapsed && hoveredItem === item.id && (
          <div
            style={{
              position: "fixed",
              left: "90px",
              top: "50%",
              transform: "translateY(-50%)",
              background: theme.colors.surfaceElevated,
              backdropFilter: `blur(${theme.blur.md})`,
              WebkitBackdropFilter: `blur(${theme.blur.md})`,
              border: `1px solid ${theme.colors.border.glass}`,
              borderRadius: theme.borderRadius.lg,
              padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
              fontSize: theme.typography.fontSize.sm,
              fontWeight: theme.typography.fontWeight.medium,
              color: theme.colors.text.primary,
              boxShadow: `0 4px 12px ${theme.colors.text.secondary}20`,
              zIndex: 1000,
              whiteSpace: "nowrap",
              pointerEvents: "none",
            }}
          >
            {item.label}
            {/* Arrow pointing to sidebar */}
            <div
              style={{
                position: "absolute",
                left: "-6px",
                top: "50%",
                transform: "translateY(-50%)",
                width: 0,
                height: 0,
                borderTop: "6px solid transparent",
                borderBottom: "6px solid transparent",
                borderRight: `6px solid ${theme.colors.surfaceElevated}`,
              }}
            />
          </div>
        )}

        {hasSubItems && isExpanded && !isCollapsed && (
          <div style={{ marginLeft: theme.spacing.lg, paddingLeft: theme.spacing.md }}>
            {item.subItems!.map((subItem) => renderSidebarItem(subItem, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      style={{
        width: isCollapsed ? "80px" : "300px",
        height: "100vh",
        background: `linear-gradient(180deg, ${theme.colors.surface}, ${theme.colors.background.secondary}40)`,
        backdropFilter: `blur(${theme.blur.xl})`,
        WebkitBackdropFilter: `blur(${theme.blur.xl})`,
        borderRight: `1px solid ${theme.colors.border.glass}`,
        display: "flex",
        flexDirection: "column",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        position: "fixed",
        left: 0,
        top: 0,
        zIndex: 100,
        boxShadow: `4px 0 20px ${theme.colors.text.secondary}10`,
      }}
    >
      {/* Logo Section */}
      <div
        style={{
          padding: `${theme.spacing.xl}px ${theme.spacing.lg}px`,
          borderBottom: `1px solid ${theme.colors.border.glass}`,
          display: "flex",
          flexDirection: "column",
          gap: theme.spacing.md,
          background: `linear-gradient(135deg, ${theme.colors.surfaceElevated}60, transparent)`,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
        <div style={{ display: "flex", alignItems: "center", gap: theme.spacing.md }}>
          <div
            style={{
              width: 44,
              height: 44,
              background: `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryDark})`,
              borderRadius: theme.borderRadius.xl,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "22px",
              color: "white",
              fontWeight: theme.typography.fontWeight.bold,
              boxShadow: `0 6px 20px ${theme.colors.primary}40`,
              border: `2px solid ${theme.colors.primary}20`,
            }}
          >
            A
          </div>
          {!isCollapsed && (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <span
                style={{
                  fontSize: theme.typography.fontSize.lg,
                  fontWeight: theme.typography.fontWeight.bold,
                  color: theme.colors.text.primary,
                  letterSpacing: "0.02em",
                }}
              >
                ARTBA
              </span>
              <span
                style={{
                  fontSize: theme.typography.fontSize.xs,
                  color: theme.colors.text.secondary,
                  fontWeight: theme.typography.fontWeight.medium,
                }}
              >
                Economics Dashboard
              </span>
            </div>
          )}
        </div>
        
        <button
          onClick={onToggle}
          style={{
            background: "transparent",
            border: "none",
            cursor: "pointer",
            padding: theme.spacing.sm,
            borderRadius: theme.borderRadius.lg,
            color: theme.colors.text.secondary,
            transition: "all 0.2s ease",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: "36px",
            height: "36px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = `${theme.colors.surfaceElevated}80`;
            e.currentTarget.style.color = theme.colors.text.primary;
            e.currentTarget.style.transform = "scale(1.1)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent";
            e.currentTarget.style.color = theme.colors.text.secondary;
            e.currentTarget.style.transform = "scale(1)";
          }}
        >
          {isCollapsed ? <Menu size={18} /> : <X size={18} />}
        </button>
        </div>
      </div>

      {/* Navigation Items */}
      <div
        style={{
          flex: 1,
          padding: `${theme.spacing.lg}px ${theme.spacing.md}px`,
          overflowY: "auto",
          overflowX: "hidden",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: theme.spacing.xs }}>
          {sidebarItems.map((item) => renderSidebarItem(item))}
        </div>
      </div>

      {/* User Section */}
      <div
        style={{
          padding: theme.spacing.lg,
          borderTop: `1px solid ${theme.colors.border.glass}`,
          background: theme.colors.surfaceElevated,
          backdropFilter: `blur(${theme.blur.md})`,
          WebkitBackdropFilter: `blur(${theme.blur.md})`,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: theme.spacing.md,
            padding: `${theme.spacing.sm}px 0`,
            cursor: "pointer",
            borderRadius: theme.borderRadius.md,
            transition: theme.transitions.fast,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceHover;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent";
          }}
        >
          <div
            style={{
              width: 36,
              height: 36,
              borderRadius: "50%",
              background: theme.colors.gradients.primary,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: theme.typography.fontSize.sm,
              fontWeight: theme.typography.fontWeight.semibold,
              flexShrink: 0,
            }}
          >
            JD
          </div>
          {!isCollapsed && (
            <div style={{ flex: 1 }}>
              <div
                style={{
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.semibold,
                  color: theme.colors.text.primary,
                }}
              >
                John Doe
              </div>
              <div
                style={{
                  fontSize: theme.typography.fontSize.xs,
                  color: theme.colors.text.secondary,
                }}
              >
                Administrator
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};