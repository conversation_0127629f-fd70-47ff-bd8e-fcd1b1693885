/**
 * Unified Error Boundary component for the application
 * Handles all error scenarios with proper logging and user-friendly messages
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logger } from '../../services/logger';
import { theme } from '../../config/theme.config';
import { env } from '../../config/env.config';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode | ((error: Error, retry: () => void) => ReactNode);
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  context?: string; // Where this error boundary is used (e.g., 'widget', 'dashboard', 'app')
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { context = 'unknown', onError } = this.props;

    // Log the error with context
    logger.error(`Error in ${context}`, error, {
      context,
      componentStack: errorInfo.componentStack,
      ...this.getErrorContext(error),
    });

    this.setState({ errorInfo });
    onError?.(error, errorInfo);
  }

  private getErrorContext(error: Error) {
    const errorMessage = error.message || '';
    
    // Categorize the error
    const errorContext: Record<string, any> = {};

    if (errorMessage.includes('CRS')) {
      errorContext.type = 'map_error';
      errorContext.severity = 'low'; // CRS errors are non-critical
    } else if (errorMessage.includes('dimension') && errorMessage.includes('not found')) {
      errorContext.type = 'data_model_error';
      errorContext.severity = 'medium';
    } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      errorContext.type = 'auth_error';
      errorContext.severity = 'high';
    } else if (errorMessage.includes('404')) {
      errorContext.type = 'not_found_error';
      errorContext.severity = 'medium';
    } else {
      errorContext.type = 'unknown_error';
      errorContext.severity = 'high';
    }

    return errorContext;
  }

  private getUserFriendlyMessage(error: Error): string {
    const errorMessage = error.message || '';

    if (errorMessage.includes('dimension') && errorMessage.includes('not found')) {
      return 'The data model referenced by this component is not available in your Sisense instance.';
    } else if (errorMessage.includes('CRS')) {
      return 'This component requires geographic/mapping features that are currently unavailable.';
    } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      return 'Authentication failed. Please check your Sisense configuration.';
    } else if (errorMessage.includes('404')) {
      return 'The requested resource could not be found. Please verify the configuration.';
    } else if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {
      return 'Unable to connect to the server. Please check your network connection.';
    }

    return 'An unexpected error occurred. Please try refreshing the page.';
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const { fallback, showDetails = env.isDevelopment } = this.props;
      const { error } = this.state;

      // If custom fallback is provided
      if (fallback) {
        if (typeof fallback === 'function') {
          return fallback(error!, this.handleRetry);
        }
        return fallback;
      }

      // Default error UI
      const userMessage = this.getUserFriendlyMessage(error!);
      const isCRSError = error?.message?.includes('CRS');

      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: theme.spacing.xl,
            textAlign: 'center',
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
            minHeight: '200px',
            margin: theme.spacing.md,
          }}
        >
          <div
            style={{
              fontSize: '48px',
              marginBottom: theme.spacing.md,
              opacity: 0.6,
            }}
          >
            {isCRSError ? '🗺️' : '⚠️'}
          </div>
          
          <h3
            style={{
              fontSize: theme.typography.fontSize.lg,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.sm,
              margin: 0,
            }}
          >
            {isCRSError ? 'Map Feature Unavailable' : 'Something went wrong'}
          </h3>
          
          <p
            style={{
              fontSize: theme.typography.fontSize.md,
              color: theme.colors.text.secondary,
              maxWidth: '400px',
              lineHeight: theme.typography.lineHeight.relaxed,
              margin: `${theme.spacing.md}px 0`,
            }}
          >
            {userMessage}
          </p>

          <button
            onClick={this.handleRetry}
            style={{
              background: theme.colors.primary,
              color: 'white',
              border: 'none',
              borderRadius: theme.borderRadius.md,
              padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
              fontSize: theme.typography.fontSize.sm,
              fontWeight: theme.typography.fontWeight.medium,
              cursor: 'pointer',
              transition: `all ${theme.transitions.fast}`,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
          >
            Try Again
          </button>

          {showDetails && error && (
            <details
              style={{
                marginTop: theme.spacing.lg,
                textAlign: 'left',
                width: '100%',
                maxWidth: '600px',
              }}
            >
              <summary
                style={{
                  cursor: 'pointer',
                  fontSize: theme.typography.fontSize.sm,
                  color: theme.colors.text.secondary,
                  marginBottom: theme.spacing.sm,
                }}
              >
                Technical Details
              </summary>
              <pre
                style={{
                  padding: theme.spacing.md,
                  background: theme.colors.background.secondary,
                  borderRadius: theme.borderRadius.sm,
                  fontSize: '12px',
                  overflow: 'auto',
                  maxHeight: '200px',
                  color: theme.colors.text.secondary,
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                }}
              >
                {error.stack || error.message}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap any component with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}