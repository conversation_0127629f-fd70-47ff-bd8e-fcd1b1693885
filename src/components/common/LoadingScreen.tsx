import React from "react";
import { theme } from "../../config/theme.config";

export const LoadingScreen: React.FC = () => {
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: theme.colors.background.blur,
        backdropFilter: `blur(${theme.blur.xl})`,
        WebkitBackdropFilter: `blur(${theme.blur.xl})`,
        zIndex: 9999,
      }}
      data-oid="6vujfnv"
    >
      <div
        style={{
          position: "relative",
          width: "80px",
          height: "80px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
        data-oid="7nbcs6g"
      >
        <div
          style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            background: theme.colors.surface,
            backdropFilter: `blur(${theme.blur.md})`,
            WebkitBackdropFilter: `blur(${theme.blur.md})`,
            borderRadius: "50%",
            border: `2px solid ${theme.colors.border.glass}`,
            boxShadow: theme.shadows.glow,
          }}
          data-oid="qc:n:jh"
        />

        <div
          style={{
            width: "48px",
            height: "48px",
            border: `3px solid ${theme.colors.border.glass}`,
            borderTopColor: theme.colors.primary,
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
          }}
          data-oid="bc21b73"
        />
      </div>

      <p
        style={{
          marginTop: theme.spacing.xl,
          color: theme.colors.text.primary,
          fontSize: "16px",
          fontWeight: theme.typography.fontWeight.medium,
          textShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        }}
        data-oid="2030dn1"
      >
        Authenticating with ARTBA...
      </p>
      <style data-oid="6-xhefh">
        {`
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};
