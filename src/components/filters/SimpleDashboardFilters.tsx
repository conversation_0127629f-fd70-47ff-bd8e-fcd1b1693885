import React, { useState } from 'react';
import { theme } from '../../config/theme.config';

interface SimpleDashboardFiltersProps {
  onFiltersChange?: (filters: any[]) => void;
  variant?: 'horizontal' | 'vertical';
}

interface FilterState {
  state: string;
  mode: string;
}

/**
 * Simplified Dashboard Filters Component
 * 
 * This component provides a visual filter interface without trying to apply
 * filters directly to Sisense widgets. Instead, it relies on the built-in
 * dashboard filters (includeDashboardFilters=true) and provides a UI for
 * demonstration and future enhancement.
 */
export const SimpleDashboardFilters: React.FC<SimpleDashboardFiltersProps> = ({
  onFiltersChange,
  variant = 'horizontal',
}) => {
  const [filters, setFilters] = useState<FilterState>({
    state: 'All States',
    mode: 'All Modes',
  });

  const handleFilterChange = (filterType: keyof FilterState, value: string) => {
    const newFilters = { ...filters, [filterType]: value };
    setFilters(newFilters);

    // Convert to array format for parent component
    const filterArray = Object.entries(newFilters)
      .filter(([_, value]) => value && !value.startsWith('All'))
      .map(([key, value]) => ({ id: `${key}-filter`, value }));

    onFiltersChange?.(filterArray);

    // Log for debugging
    console.log('Dashboard filters updated:', filterArray);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      state: 'All States',
      mode: 'All Modes',
    };
    setFilters(clearedFilters);
    onFiltersChange?.([]);
  };

  const activeFilterCount = Object.values(filters).filter(
    value => value && !value.startsWith('All') && value.trim() !== ''
  ).length;

  // For now, we'll use the modes we know exist from the screenshot
  // TODO: Implement dynamic fetching once Sisense connection is properly configured
  const modeOptions = [
    'All Modes',
    'Airport',
    'Bridge & Tunnel',
    'Highway & Pavement',
    'Rail & Transit',
    'Waterway'
  ];

  const stateOptions = [
    'All States',
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
    'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
    'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
    'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
    'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
    'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
    'West Virginia', 'Wisconsin', 'Wyoming'
  ];



  return (
    <div
      style={{
        display: 'flex',
        flexDirection: variant === 'horizontal' ? 'row' : 'column',
        gap: theme.spacing.md,
        padding: theme.spacing.lg,
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.md})`,
        WebkitBackdropFilter: `blur(${theme.blur.md})`,
        border: `1px solid ${theme.colors.border.glass}`,
        borderRadius: theme.borderRadius.lg,
        marginBottom: theme.spacing.xl,
        flexWrap: variant === 'horizontal' ? 'wrap' : 'nowrap',
        alignItems: variant === 'horizontal' ? 'center' : 'stretch',
      }}
    >
      {/* Filter Label */}
      <div
        style={{
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
          color: theme.colors.text.primary,
          marginBottom: variant === 'vertical' ? theme.spacing.md : 0,
          marginRight: variant === 'horizontal' ? theme.spacing.lg : 0,
          minWidth: 'fit-content',
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing.sm,
        }}
      >
        Filters:
        {activeFilterCount > 0 && (
          <span
            style={{
              background: theme.colors.primary,
              color: 'white',
              padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
              borderRadius: theme.borderRadius.full,
              fontSize: theme.typography.fontSize.xs,
              fontWeight: theme.typography.fontWeight.medium,
            }}
          >
            {activeFilterCount} active
          </span>
        )}
      </div>

      {/* State Filter */}
      <div style={{ minWidth: '200px', flex: variant === 'horizontal' ? '1 1 200px' : 'none' }}>
        <label
          style={{
            display: 'block',
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing.xs,
          }}
        >
          State
        </label>
        <select
          value={filters.state}
          onChange={(e) => handleFilterChange('state', e.target.value)}
          style={{
            width: '100%',
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            background: theme.colors.surfaceElevated,
            backdropFilter: `blur(${theme.blur.sm})`,
            WebkitBackdropFilter: `blur(${theme.blur.sm})`,
            border: `1px solid ${theme.colors.border.glass}`,
            borderRadius: theme.borderRadius.md,
            fontSize: theme.typography.fontSize.sm,
            color: theme.colors.text.primary,
            outline: 'none',
            cursor: 'pointer',
            transition: theme.transitions.fast,
          }}
          onFocus={(e) => {
            e.currentTarget.style.borderColor = theme.colors.primary;
          }}
          onBlur={(e) => {
            e.currentTarget.style.borderColor = theme.colors.border.glass;
          }}
        >
          {stateOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      </div>

      {/* Transportation Mode Filter */}
      <div style={{ minWidth: '200px', flex: variant === 'horizontal' ? '1 1 200px' : 'none' }}>
        <label
          style={{
            display: 'block',
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing.xs,
          }}
        >
          Transportation Mode
        </label>
        <select
          value={filters.mode}
          onChange={(e) => handleFilterChange('mode', e.target.value)}
          style={{
            width: '100%',
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            background: theme.colors.surfaceElevated,
            backdropFilter: `blur(${theme.blur.sm})`,
            WebkitBackdropFilter: `blur(${theme.blur.sm})`,
            border: `1px solid ${theme.colors.border.glass}`,
            borderRadius: theme.borderRadius.md,
            fontSize: theme.typography.fontSize.sm,
            color: theme.colors.text.primary,
            outline: 'none',
            cursor: 'pointer',
            transition: theme.transitions.fast,
          }}
          onFocus={(e) => {
            e.currentTarget.style.borderColor = theme.colors.primary;
          }}
          onBlur={(e) => {
            e.currentTarget.style.borderColor = theme.colors.border.glass;
          }}
        >
          {modeOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      </div>



      {/* Clear All Button */}
      {activeFilterCount > 0 && (
        <button
          onClick={clearAllFilters}
          style={{
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            background: 'transparent',
            color: theme.colors.text.secondary,
            border: `1px solid ${theme.colors.border.light}`,
            borderRadius: theme.borderRadius.md,
            fontSize: theme.typography.fontSize.sm,
            cursor: 'pointer',
            transition: theme.transitions.fast,
            minWidth: 'fit-content',
            height: 'fit-content',
            alignSelf: variant === 'horizontal' ? 'flex-end' : 'stretch',
            marginTop: variant === 'horizontal' ? '20px' : '0',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceElevated;
            e.currentTarget.style.borderColor = theme.colors.primary;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.borderColor = theme.colors.border.light;
          }}
        >
          Clear All
        </button>
      )}
    </div>
  );
};
