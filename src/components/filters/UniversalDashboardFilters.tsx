import React, { useState, useEffect } from 'react';
import { theme } from '../../config/theme.config';
import type { 
  DashboardFilterConfig, 
  FilterConfig
} from '../../config/filters.config';
import { getDashboardFilterConfig } from '../../config/filters.config';

interface UniversalDashboardFiltersProps {
  /** Dashboard type from DASHBOARD_FILTER_CONFIGS or custom config */
  dashboardType?: string;
  /** Custom filter configuration (overrides dashboardType) */
  customConfig?: DashboardFilterConfig;
  /** Callback when filters change */
  onFiltersChange?: (filters: FilterValue[]) => void;
  /** Layout variant */
  variant?: 'horizontal' | 'vertical' | 'grid';
  /** Additional CSS class */
  className?: string;
  /** Custom styling */
  style?: React.CSSProperties;
}

export type FilterValue = {
  id: string;
  value: any;
  label?: string;
};

interface FilterState {
  [filterId: string]: any;
}

/**
 * Universal Dashboard Filters Component
 * 
 * This component can be used across all dashboards with predefined configurations
 * or custom filter setups. It provides a consistent UI and behavior.
 */
export const UniversalDashboardFilters: React.FC<UniversalDashboardFiltersProps> = ({
  dashboardType,
  customConfig,
  onFiltersChange,
  variant,
  className,
  style
}) => {
  // Get filter configuration
  const config = customConfig || (dashboardType ? getDashboardFilterConfig(dashboardType) : null);
  
  if (!config) {
    console.warn('UniversalDashboardFilters: No valid configuration provided');
    return null;
  }

  const [filters, setFilters] = useState<FilterState>(() => {
    // Initialize with default values
    const initialState: FilterState = {};
    config.filters.forEach(filter => {
      initialState[filter.id] = filter.defaultValue || '';
    });
    return initialState;
  });

  const layout = variant || config.layout || 'horizontal';

  const handleFilterChange = (filterId: string, value: any) => {
    const newFilters = { ...filters, [filterId]: value };
    setFilters(newFilters);

    // Convert to array format for parent component
    const filterArray: FilterValue[] = Object.entries(newFilters)
      .filter(([_, filterValue]) => {
        // Filter out "All" values and empty values
        return filterValue && 
               filterValue !== '' && 
               !String(filterValue).startsWith('All');
      })
      .map(([id, filterValue]) => {
        const filterConfig = config.filters.find(f => f.id === id);
        return {
          id: `${id}`,
          value: filterValue,
          label: filterConfig?.title || id
        };
      });

    onFiltersChange?.(filterArray);

    // Log for debugging
    console.log(`${config.name} filters updated:`, filterArray);
  };

  const clearAllFilters = () => {
    const clearedFilters: FilterState = {};
    config.filters.forEach(filter => {
      clearedFilters[filter.id] = filter.defaultValue || '';
    });
    setFilters(clearedFilters);
    onFiltersChange?.([]);
  };

  const activeFilterCount = Object.values(filters).filter(
    value => value && value !== '' && !String(value).startsWith('All')
  ).length;

  const getFilterWidth = (width?: string) => {
    switch (width) {
      case 'small': return '150px';
      case 'medium': return '200px';
      case 'large': return '300px';
      case 'full': return '100%';
      default: return 'auto';
    }
  };

  const renderFilter = (filterConfig: FilterConfig) => {
    const currentValue = filters[filterConfig.id] || filterConfig.defaultValue || '';

    switch (filterConfig.type) {
      case 'select':
        const options = Array.isArray(filterConfig.options) ? filterConfig.options : [];
        
        return (
          <div 
            key={filterConfig.id}
            style={{ 
              minWidth: getFilterWidth(filterConfig.width),
              flex: layout === 'horizontal' ? '1 1 auto' : 'none'
            }}
          >
            <label
              style={{
                display: 'block',
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.medium,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.xs,
              }}
            >
              {filterConfig.title}
              {filterConfig.required && <span style={{ color: theme.colors.error }}>*</span>}
            </label>
            <select
              value={currentValue}
              onChange={(e) => handleFilterChange(filterConfig.id, e.target.value)}
              style={{
                width: '100%',
                padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                background: theme.colors.surfaceElevated,
                backdropFilter: `blur(${theme.blur.sm})`,
                WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.primary,
                outline: 'none',
                cursor: 'pointer',
                transition: theme.transitions.fast,
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = theme.colors.primary;
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = theme.colors.border.glass;
              }}
            >
              {options.map((option) => {
                const optionValue = typeof option === 'string' ? option : option.value;
                const optionLabel = typeof option === 'string' ? option : option.label;
                const isDisabled = typeof option === 'object' && option.disabled;
                
                return (
                  <option key={optionValue} value={optionValue} disabled={isDisabled}>
                    {optionLabel}
                  </option>
                );
              })}
            </select>
            {filterConfig.description && (
              <div style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.secondary,
                marginTop: theme.spacing.xs
              }}>
                {filterConfig.description}
              </div>
            )}
          </div>
        );

      case 'text':
        return (
          <div 
            key={filterConfig.id}
            style={{ 
              minWidth: getFilterWidth(filterConfig.width),
              flex: layout === 'horizontal' ? '1 1 auto' : 'none'
            }}
          >
            <label
              style={{
                display: 'block',
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.medium,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.xs,
              }}
            >
              {filterConfig.title}
              {filterConfig.required && <span style={{ color: theme.colors.error }}>*</span>}
            </label>
            <input
              type="text"
              value={currentValue}
              onChange={(e) => handleFilterChange(filterConfig.id, e.target.value)}
              placeholder={filterConfig.placeholder || `Enter ${filterConfig.title.toLowerCase()}...`}
              style={{
                width: '100%',
                padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                background: theme.colors.surfaceElevated,
                backdropFilter: `blur(${theme.blur.sm})`,
                WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.primary,
                outline: 'none',
                transition: theme.transitions.fast,
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = theme.colors.primary;
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = theme.colors.border.glass;
              }}
            />
            {filterConfig.description && (
              <div style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.secondary,
                marginTop: theme.spacing.xs
              }}>
                {filterConfig.description}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={className}
      style={{
        display: 'flex',
        flexDirection: layout === 'horizontal' ? 'row' : 'column',
        gap: theme.spacing.md,
        padding: theme.spacing.lg,
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.md})`,
        WebkitBackdropFilter: `blur(${theme.blur.md})`,
        border: `1px solid ${theme.colors.border.glass}`,
        borderRadius: theme.borderRadius.lg,
        marginBottom: theme.spacing.xl,
        flexWrap: layout === 'horizontal' ? 'wrap' : 'nowrap',
        alignItems: layout === 'horizontal' ? 'center' : 'stretch',
        ...style
      }}
    >
      {/* Filter Label */}
      {config.showActiveCount !== false && (
        <div
          style={{
            fontSize: theme.typography.fontSize.lg,
            fontWeight: theme.typography.fontWeight.semibold,
            color: theme.colors.text.primary,
            marginBottom: layout === 'vertical' ? theme.spacing.md : 0,
            marginRight: layout === 'horizontal' ? theme.spacing.lg : 0,
            minWidth: 'fit-content',
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.sm,
          }}
        >
          Filters:
          {activeFilterCount > 0 && (
            <span
              style={{
                background: theme.colors.primary,
                color: 'white',
                padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                borderRadius: theme.borderRadius.full,
                fontSize: theme.typography.fontSize.xs,
                fontWeight: theme.typography.fontWeight.medium,
              }}
            >
              {activeFilterCount} active
            </span>
          )}
        </div>
      )}

      {/* Render all filters */}
      {config.filters.map(renderFilter)}

      {/* Clear All Button */}
      {config.showClearAll !== false && activeFilterCount > 0 && (
        <button
          onClick={clearAllFilters}
          style={{
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            background: 'transparent',
            color: theme.colors.text.secondary,
            border: `1px solid ${theme.colors.border.light}`,
            borderRadius: theme.borderRadius.md,
            fontSize: theme.typography.fontSize.sm,
            cursor: 'pointer',
            transition: theme.transitions.fast,
            minWidth: 'fit-content',
            height: 'fit-content',
            alignSelf: layout === 'horizontal' ? 'flex-end' : 'stretch',
            marginTop: layout === 'horizontal' ? '20px' : '0',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceElevated;
            e.currentTarget.style.borderColor = theme.colors.primary;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.borderColor = theme.colors.border.light;
          }}
        >
          Clear All
        </button>
      )}
    </div>
  );
};
