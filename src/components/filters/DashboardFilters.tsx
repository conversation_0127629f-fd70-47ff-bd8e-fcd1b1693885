import React, { useState } from 'react';
import { theme } from '../../config/theme.config';

interface DashboardFiltersProps {
  onFiltersChange?: (filters: any[]) => void;
  availableFilters?: FilterConfig[];
  variant?: 'horizontal' | 'vertical';
}

interface FilterConfig {
  id: string;
  title: string;
  type: 'select' | 'dateRange' | 'text';
  options?: string[];
  defaultValue?: any;
}

export const DashboardFilters: React.FC<DashboardFiltersProps> = ({
  onFiltersChange,
  availableFilters = [],
  variant = 'horizontal',
}) => {
  const [activeFilters, setActiveFilters] = useState<any[]>([]);

  const handleFilterChange = (filterId: string, filterValue: any) => {
    const updatedFilters = activeFilters.filter(f => f.id !== filterId);
    if (filterValue) {
      updatedFilters.push({ id: filterId, value: filterValue });
    }
    
    setActiveFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const defaultFilters: FilterConfig[] = [
    {
      id: 'mode-filter',
      title: 'Transportation Mode',
      type: 'select',
      options: [
        'All Modes',
        'Highway',
        'Bridge',
        'Transit',
        'Airport',
        'Port',
        'Rail',
        'Multimodal'
      ],
      defaultValue: 'All Modes',
    },
    {
      id: 'year-filter',
      title: 'Year',
      type: 'select',
      options: ['All Years', '2024', '2023', '2022', '2021', '2020', '2019', '2018'],
      defaultValue: 'All Years',
    },
    {
      id: 'search-filter',
      title: 'Search',
      type: 'text',
      defaultValue: '',
    },
  ];

  const filtersToRender = availableFilters.length > 0 ? availableFilters : defaultFilters;

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: variant === 'horizontal' ? 'row' : 'column',
        gap: theme.spacing.md,
        padding: theme.spacing.lg,
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.md})`,
        WebkitBackdropFilter: `blur(${theme.blur.md})`,
        border: `1px solid ${theme.colors.border.glass}`,
        borderRadius: theme.borderRadius.lg,
        marginBottom: theme.spacing.xl,
        flexWrap: variant === 'horizontal' ? 'wrap' : 'nowrap',
      }}
    >
      <div
        style={{
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
          color: theme.colors.text.primary,
          marginBottom: variant === 'vertical' ? theme.spacing.md : 0,
          marginRight: variant === 'horizontal' ? theme.spacing.lg : 0,
          minWidth: 'fit-content',
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing.sm,
        }}
      >
        Filters:
        {activeFilters.length > 0 && (
          <span
            style={{
              background: theme.colors.primary,
              color: 'white',
              padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
              borderRadius: theme.borderRadius.full,
              fontSize: theme.typography.fontSize.xs,
              fontWeight: theme.typography.fontWeight.medium,
            }}
          >
            {activeFilters.length} active
          </span>
        )}
      </div>
      
      {filtersToRender.map((filterConfig) => {
        const currentValue = activeFilters.find(f => f.id === filterConfig.id)?.value || filterConfig.defaultValue;

        const renderFilter = () => {
          switch (filterConfig.type) {
            case 'select':
              return (
                <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing.xs }}>
                  <label
                    style={{
                      fontSize: theme.typography.fontSize.sm,
                      fontWeight: theme.typography.fontWeight.medium,
                      color: theme.colors.text.primary,
                    }}
                  >
                    {filterConfig.title}
                  </label>
                  <select
                    value={currentValue}
                    onChange={(e) => {
                      const value = e.target.value === filterConfig.defaultValue ? null : e.target.value;
                      handleFilterChange(filterConfig.id, value);
                    }}
                    style={{
                      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                      background: theme.colors.surfaceElevated,
                      backdropFilter: `blur(${theme.blur.sm})`,
                      WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                      border: `1px solid ${theme.colors.border.glass}`,
                      borderRadius: theme.borderRadius.md,
                      fontSize: theme.typography.fontSize.sm,
                      color: theme.colors.text.primary,
                      outline: 'none',
                      cursor: 'pointer',
                      transition: theme.transitions.fast,
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = theme.colors.primary;
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = theme.colors.border.glass;
                    }}
                  >
                    {filterConfig.options?.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              );
            case 'text':
              return (
                <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing.xs }}>
                  <label
                    style={{
                      fontSize: theme.typography.fontSize.sm,
                      fontWeight: theme.typography.fontWeight.medium,
                      color: theme.colors.text.primary,
                    }}
                  >
                    {filterConfig.title}
                  </label>
                  <input
                    type="text"
                    value={currentValue || ''}
                    onChange={(e) => handleFilterChange(filterConfig.id, e.target.value || null)}
                    placeholder={`Filter by ${filterConfig.title.toLowerCase()}...`}
                    style={{
                      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                      background: theme.colors.surfaceElevated,
                      backdropFilter: `blur(${theme.blur.sm})`,
                      WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                      border: `1px solid ${theme.colors.border.glass}`,
                      borderRadius: theme.borderRadius.md,
                      fontSize: theme.typography.fontSize.sm,
                      color: theme.colors.text.primary,
                      outline: 'none',
                      transition: theme.transitions.fast,
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = theme.colors.primary;
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = theme.colors.border.glass;
                    }}
                  />
                </div>
              );
            default:
              return null;
          }
        };

        return (
          <div
            key={filterConfig.id}
            style={{
              minWidth: '200px',
              flex: variant === 'horizontal' ? '1 1 200px' : 'none',
            }}
          >
            {renderFilter()}
          </div>
        );
      })}
      
      {activeFilters.length > 0 && (
        <button
          onClick={() => {
            setActiveFilters([]);
            onFiltersChange?.([]);
          }}
          style={{
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            background: 'transparent',
            color: theme.colors.text.secondary,
            border: `1px solid ${theme.colors.border.light}`,
            borderRadius: theme.borderRadius.md,
            fontSize: theme.typography.fontSize.sm,
            cursor: 'pointer',
            transition: theme.transitions.fast,
            minWidth: 'fit-content',
            height: 'fit-content',
            alignSelf: 'center',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceElevated;
            e.currentTarget.style.borderColor = theme.colors.primary;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
            e.currentTarget.style.borderColor = theme.colors.border.light;
          }}
        >
          Clear All
        </button>
      )}
    </div>
  );
};
