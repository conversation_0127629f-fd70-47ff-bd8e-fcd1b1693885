import React, { Component, ErrorInfo, ReactNode } from "react";
import { theme } from "../../config/theme.config";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorMessage = this.state.error?.message || "";
      let userFriendlyMessage = "This widget requires a connection to Sisense.";

      // Provide more specific error messages
      if (
        errorMessage.includes("dimension") &&
        errorMessage.includes("not found")
      ) {
        userFriendlyMessage =
          "The data model referenced by this widget is not available in your Sisense instance.";
      } else if (errorMessage.includes("CRS")) {
        userFriendlyMessage =
          "This widget requires geographic/mapping features that are not configured.";
      } else if (
        errorMessage.includes("401") ||
        errorMessage.includes("Unauthorized")
      ) {
        userFriendlyMessage =
          "Authentication failed. Please check your Sisense API token.";
      } else if (errorMessage.includes("404")) {
        userFriendlyMessage =
          "Widget or dashboard not found. Please verify the IDs are correct.";
      }

      return (
        <div
          style={{
            padding: theme.spacing.xl,
            background: theme.colors.surfaceElevated,
            backdropFilter: `blur(${theme.blur.lg})`,
            WebkitBackdropFilter: `blur(${theme.blur.lg})`,
            borderRadius: theme.borderRadius.xl,
            border: `1px solid ${theme.colors.border.glass}`,
            textAlign: "center",
            boxShadow: theme.shadows.glass,
          }}
          data-oid="h1erqw-"
        >
          <div
            style={{ fontSize: "36px", marginBottom: theme.spacing.sm }}
            data-oid="l3zrx9u"
          >
            ⚠️
          </div>
          <h3
            style={{
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}
            data-oid="233i15k"
          >
            Widget Loading Error
          </h3>
          <p
            style={{
              color: theme.colors.text.secondary,
              marginBottom: theme.spacing.sm,
            }}
            data-oid="9-mkb07"
          >
            {userFriendlyMessage}
          </p>
          {!import.meta.env.VITE_INSTANCE_URL && (
            <p
              style={{ color: theme.colors.text.secondary, fontSize: "14px" }}
              data-oid="9p3fvzh"
            >
              Please configure your Sisense instance URL and API token in the
              .env file.
            </p>
          )}
          {this.state.error && (
            <details
              style={{ marginTop: theme.spacing.md, textAlign: "left" }}
              data-oid="9id84x."
            >
              <summary
                style={{
                  cursor: "pointer",
                  color: theme.colors.text.secondary,
                }}
                data-oid="19ocp88"
              >
                Technical Details
              </summary>
              <pre
                style={{
                  marginTop: theme.spacing.sm,
                  padding: theme.spacing.md,
                  background: theme.colors.glass.dark,
                  backdropFilter: `blur(${theme.blur.sm})`,
                  WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                  borderRadius: theme.borderRadius.md,
                  fontSize: "12px",
                  overflow: "auto",
                  maxHeight: "200px",
                  border: `1px solid ${theme.colors.border.glass}`,
                  color: theme.colors.text.secondary,
                }}
                data-oid="5650uh3"
              >
                {this.state.error.message}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}
