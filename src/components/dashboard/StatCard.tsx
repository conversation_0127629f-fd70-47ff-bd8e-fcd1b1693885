import React from "react";
import { theme } from "../../config/theme.config";

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  trend?: number[];
  icon?: string;
  color?: string;
  subtitle?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  trend = [],
  icon,
  color = theme.colors.primary,
  subtitle,
}) => {
  const isPositive = change && change > 0;
  const changeColor = isPositive ? theme.colors.success : theme.colors.error;
  
  // Create sparkline path
  const createSparkline = () => {
    if (trend.length < 2) return "";
    
    const width = 80;
    const height = 40;
    const max = Math.max(...trend);
    const min = Math.min(...trend);
    const range = max - min || 1;
    
    const points = trend.map((val, i) => {
      const x = (i / (trend.length - 1)) * width;
      const y = height - ((val - min) / range) * height;
      return `${x},${y}`;
    });
    
    return `M ${points.join(" L ")}`;
  };

  return (
    <div
      style={{
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.lg})`,
        WebkitBackdropFilter: `blur(${theme.blur.lg})`,
        borderRadius: theme.borderRadius.xl,
        padding: theme.spacing.xl,
        border: `1px solid ${theme.colors.border.glass}`,
        boxShadow: theme.shadows.card,
        transition: theme.transitions.normal,
        cursor: "pointer",
        position: "relative",
        overflow: "hidden",
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = "translateY(-4px)";
        e.currentTarget.style.boxShadow = theme.shadows.cardHover;
        e.currentTarget.style.borderColor = `${color}40`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = "translateY(0)";
        e.currentTarget.style.boxShadow = theme.shadows.card;
        e.currentTarget.style.borderColor = theme.colors.border.glass;
      }}
    >
      {/* Background decoration */}
      <div
        style={{
          position: "absolute",
          top: -20,
          right: -20,
          width: 100,
          height: 100,
          background: `radial-gradient(circle, ${color}15 0%, transparent 70%)`,
          borderRadius: "50%",
        }}
      />
      
      <div style={{ position: "relative", zIndex: 1 }}>
        <div
          style={{
            display: "flex",
            alignItems: "flex-start",
            justifyContent: "space-between",
            marginBottom: theme.spacing.lg,
          }}
        >
          <div>
            <div
              style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.tertiary,
                textTransform: "uppercase",
                letterSpacing: "0.5px",
                marginBottom: theme.spacing.xs,
                fontWeight: theme.typography.fontWeight.medium,
              }}
            >
              {title}
            </div>
            <div
              style={{
                fontSize: "28px",
                fontWeight: theme.typography.fontWeight.bold,
                color: theme.colors.text.primary,
                lineHeight: 1,
              }}
            >
              {value}
            </div>
            {subtitle && (
              <div
                style={{
                  fontSize: theme.typography.fontSize.xs,
                  color: theme.colors.text.secondary,
                  marginTop: theme.spacing.xs,
                }}
              >
                {subtitle}
              </div>
            )}
          </div>
          
          {icon && (
            <div
              style={{
                width: 48,
                height: 48,
                background: `linear-gradient(135deg, ${color}20, ${color}10)`,
                borderRadius: theme.borderRadius.lg,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "24px",
              }}
            >
              {icon}
            </div>
          )}
        </div>
        
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: theme.spacing.md,
          }}
        >
          {change !== undefined && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: theme.spacing.xs,
                fontSize: theme.typography.fontSize.sm,
                color: changeColor,
                fontWeight: theme.typography.fontWeight.semibold,
              }}
            >
              <span style={{ fontSize: "12px" }}>
                {isPositive ? "▲" : "▼"}
              </span>
              {Math.abs(change)}%
            </div>
          )}
          
          {trend.length > 0 && (
            <svg
              width="80"
              height="40"
              style={{ overflow: "visible" }}
            >
              <path
                d={createSparkline()}
                fill="none"
                stroke={color}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <defs>
                <linearGradient id={`gradient-${title}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor={color} stopOpacity="0.3" />
                  <stop offset="100%" stopColor={color} stopOpacity="0" />
                </linearGradient>
              </defs>
              <path
                d={`${createSparkline()} L 80,40 L 0,40 Z`}
                fill={`url(#gradient-${title})`}
                opacity="0.3"
              />
            </svg>
          )}
        </div>
      </div>
    </div>
  );
};