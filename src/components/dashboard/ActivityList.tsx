import React from "react";
import { theme } from "../../config/theme.config";

interface Activity {
  id: string;
  type: "task" | "update" | "comment" | "alert";
  title: string;
  description?: string;
  timestamp: Date;
  user?: {
    name: string;
    avatar?: string;
    initials?: string;
  };
  priority?: "low" | "medium" | "high";
  status?: "pending" | "in_progress" | "completed";
}

interface ActivityListProps {
  activities: Activity[];
  title?: string;
  showFilters?: boolean;
}

export const ActivityList: React.FC<ActivityListProps> = ({
  activities,
  title = "Recent Activity",
  showFilters = true,
}) => {
  const getActivityIcon = (type: Activity["type"]) => {
    switch (type) {
      case "task": return "📋";
      case "update": return "🔄";
      case "comment": return "💬";
      case "alert": return "🔔";
      default: return "📌";
    }
  };

  const getPriorityColor = (priority?: Activity["priority"]) => {
    switch (priority) {
      case "high": return theme.colors.error;
      case "medium": return theme.colors.warning;
      case "low": return theme.colors.info;
      default: return theme.colors.text.secondary;
    }
  };

  const getStatusBadge = (status?: Activity["status"]) => {
    if (!status) return null;
    
    const statusConfig = {
      pending: { color: theme.colors.warning, label: "Pending" },
      in_progress: { color: theme.colors.info, label: "In Progress" },
      completed: { color: theme.colors.success, label: "Completed" },
    };

    const config = statusConfig[status];
    
    return (
      <span
        style={{
          display: "inline-flex",
          alignItems: "center",
          padding: `2px 8px`,
          background: `${config.color}20`,
          color: config.color,
          fontSize: "11px",
          fontWeight: theme.typography.fontWeight.semibold,
          borderRadius: theme.borderRadius.full,
          marginLeft: theme.spacing.sm,
        }}
      >
        {config.label}
      </span>
    );
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div
      style={{
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.lg})`,
        WebkitBackdropFilter: `blur(${theme.blur.lg})`,
        borderRadius: theme.borderRadius.xl,
        border: `1px solid ${theme.colors.border.glass}`,
        boxShadow: theme.shadows.card,
        overflow: "hidden",
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: theme.spacing.xl,
          borderBottom: `1px solid ${theme.colors.border.glass}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <h3
          style={{
            margin: 0,
            fontSize: theme.typography.fontSize.lg,
            fontWeight: theme.typography.fontWeight.semibold,
            color: theme.colors.text.primary,
          }}
        >
          {title}
        </h3>
        
        {showFilters && (
          <div style={{ display: "flex", gap: theme.spacing.sm }}>
            {["All", "Tasks", "Updates", "Comments"].map((filter) => (
              <button
                key={filter}
                style={{
                  padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
                  background: filter === "All" ? theme.colors.primary : "transparent",
                  color: filter === "All" ? "white" : theme.colors.text.secondary,
                  border: `1px solid ${filter === "All" ? theme.colors.primary : theme.colors.border.glass}`,
                  borderRadius: theme.borderRadius.md,
                  fontSize: theme.typography.fontSize.sm,
                  cursor: "pointer",
                  transition: theme.transitions.fast,
                }}
                onMouseEnter={(e) => {
                  if (filter !== "All") {
                    e.currentTarget.style.background = theme.colors.surfaceElevated;
                    e.currentTarget.style.borderColor = theme.colors.primary;
                    e.currentTarget.style.color = theme.colors.primary;
                  }
                }}
                onMouseLeave={(e) => {
                  if (filter !== "All") {
                    e.currentTarget.style.background = "transparent";
                    e.currentTarget.style.borderColor = theme.colors.border.glass;
                    e.currentTarget.style.color = theme.colors.text.secondary;
                  }
                }}
              >
                {filter}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Activity List */}
      <div
        style={{
          maxHeight: "500px",
          overflowY: "auto",
        }}
      >
        {activities.map((activity, index) => (
          <div
            key={activity.id}
            style={{
              padding: theme.spacing.lg,
              borderBottom: index < activities.length - 1 ? `1px solid ${theme.colors.border.light}` : "none",
              display: "flex",
              gap: theme.spacing.md,
              transition: theme.transitions.fast,
              cursor: "pointer",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = theme.colors.surfaceElevated;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent";
            }}
          >
            {/* Timeline Line */}
            {index < activities.length - 1 && (
              <div
                style={{
                  position: "absolute",
                  left: theme.spacing.lg + 20,
                  top: theme.spacing.lg + 40,
                  width: 2,
                  height: `calc(100% - ${theme.spacing.lg * 2}px)`,
                  background: theme.colors.border.light,
                  zIndex: 0,
                }}
              />
            )}
            
            {/* Activity Icon */}
            <div
              style={{
                width: 40,
                height: 40,
                background: theme.colors.surfaceElevated,
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "18px",
                flexShrink: 0,
                position: "relative",
                zIndex: 1,
                border: `2px solid ${theme.colors.background.primary}`,
              }}
            >
              {getActivityIcon(activity.type)}
            </div>
            
            {/* Content */}
            <div style={{ flex: 1 }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  justifyContent: "space-between",
                  marginBottom: theme.spacing.xs,
                }}
              >
                <div>
                  <span
                    style={{
                      fontSize: theme.typography.fontSize.md,
                      fontWeight: theme.typography.fontWeight.medium,
                      color: theme.colors.text.primary,
                    }}
                  >
                    {activity.title}
                  </span>
                  {activity.status && getStatusBadge(activity.status)}
                  {activity.priority && (
                    <span
                      style={{
                        display: "inline-block",
                        width: 6,
                        height: 6,
                        borderRadius: "50%",
                        background: getPriorityColor(activity.priority),
                        marginLeft: theme.spacing.sm,
                        verticalAlign: "middle",
                      }}
                    />
                  )}
                </div>
                <span
                  style={{
                    fontSize: theme.typography.fontSize.xs,
                    color: theme.colors.text.tertiary,
                    whiteSpace: "nowrap",
                  }}
                >
                  {formatTimestamp(activity.timestamp)}
                </span>
              </div>
              
              {activity.description && (
                <p
                  style={{
                    margin: 0,
                    fontSize: theme.typography.fontSize.sm,
                    color: theme.colors.text.secondary,
                    lineHeight: theme.typography.lineHeight.relaxed,
                    marginBottom: theme.spacing.sm,
                  }}
                >
                  {activity.description}
                </p>
              )}
              
              {activity.user && (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: theme.spacing.sm,
                    marginTop: theme.spacing.sm,
                  }}
                >
                  <div
                    style={{
                      width: 24,
                      height: 24,
                      borderRadius: "50%",
                      background: theme.colors.primary,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      fontSize: "10px",
                      color: "white",
                      fontWeight: theme.typography.fontWeight.semibold,
                    }}
                  >
                    {activity.user.initials || activity.user.name.substring(0, 2).toUpperCase()}
                  </div>
                  <span
                    style={{
                      fontSize: theme.typography.fontSize.xs,
                      color: theme.colors.text.secondary,
                    }}
                  >
                    {activity.user.name}
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Footer */}
      <div
        style={{
          padding: theme.spacing.lg,
          borderTop: `1px solid ${theme.colors.border.glass}`,
          textAlign: "center",
        }}
      >
        <button
          style={{
            background: "transparent",
            border: "none",
            color: theme.colors.primary,
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            cursor: "pointer",
            padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
            borderRadius: theme.borderRadius.md,
            transition: theme.transitions.fast,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceElevated;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent";
          }}
        >
          View All Activity →
        </button>
      </div>
    </div>
  );
};