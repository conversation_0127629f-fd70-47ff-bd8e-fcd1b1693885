import React, { useState } from "react";
import { theme } from "../../config/theme.config";

interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: "left" | "center" | "right";
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  title?: string;
  searchable?: boolean;
  pageSize?: number;
}

export const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  title,
  searchable = true,
  pageSize = 10,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data based on search
  const filteredData = data.filter((row) =>
    Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortBy) return 0;
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (aVal < bVal) return sortOrder === "asc" ? -1 : 1;
    if (aVal > bVal) return sortOrder === "asc" ? 1 : -1;
    return 0;
  });

  // Paginate data
  const totalPages = Math.ceil(sortedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = sortedData.slice(startIndex, startIndex + pageSize);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  return (
    <div
      style={{
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.lg})`,
        WebkitBackdropFilter: `blur(${theme.blur.lg})`,
        borderRadius: theme.borderRadius.xl,
        border: `1px solid ${theme.colors.border.glass}`,
        boxShadow: theme.shadows.card,
        overflow: "hidden",
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: theme.spacing.xl,
          borderBottom: `1px solid ${theme.colors.border.glass}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          gap: theme.spacing.lg,
        }}
      >
        {title && (
          <h3
            style={{
              margin: 0,
              fontSize: theme.typography.fontSize.lg,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
            }}
          >
            {title}
          </h3>
        )}
        
        {searchable && (
          <div style={{ position: "relative", minWidth: "250px" }}>
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              style={{
                width: "100%",
                padding: `${theme.spacing.sm}px ${theme.spacing.lg}px ${theme.spacing.sm}px ${theme.spacing.xl}px`,
                background: theme.colors.surfaceElevated,
                backdropFilter: `blur(${theme.blur.sm})`,
                WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.primary,
                outline: "none",
                transition: theme.transitions.fast,
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = theme.colors.primary;
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = theme.colors.border.glass;
              }}
            />
            <span
              style={{
                position: "absolute",
                left: theme.spacing.md,
                top: "50%",
                transform: "translateY(-50%)",
                color: theme.colors.text.tertiary,
                fontSize: "14px",
              }}
            >
              🔍
            </span>
          </div>
        )}
      </div>

      {/* Table */}
      <div style={{ overflowX: "auto" }}>
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse",
            fontSize: theme.typography.fontSize.sm,
          }}
        >
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  onClick={() => column.sortable && handleSort(column.key)}
                  style={{
                    padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                    textAlign: column.align || "left",
                    background: theme.colors.surfaceElevated,
                    borderBottom: `1px solid ${theme.colors.border.glass}`,
                    color: theme.colors.text.secondary,
                    fontWeight: theme.typography.fontWeight.semibold,
                    textTransform: "uppercase",
                    fontSize: "11px",
                    letterSpacing: "0.5px",
                    cursor: column.sortable ? "pointer" : "default",
                    transition: theme.transitions.fast,
                    width: column.width,
                    whiteSpace: "nowrap",
                  }}
                  onMouseEnter={(e) => {
                    if (column.sortable) {
                      e.currentTarget.style.color = theme.colors.text.primary;
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = theme.colors.text.secondary;
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: theme.spacing.sm,
                      justifyContent: column.align === "center" ? "center" : column.align === "right" ? "flex-end" : "flex-start",
                    }}
                  >
                    {column.label}
                    {column.sortable && sortBy === column.key && (
                      <span style={{ fontSize: "10px" }}>
                        {sortOrder === "asc" ? "▲" : "▼"}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                style={{
                  transition: theme.transitions.fast,
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = theme.colors.surfaceElevated;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = "transparent";
                }}
              >
                {columns.map((column) => (
                  <td
                    key={column.key}
                    style={{
                      padding: `${theme.spacing.lg}px`,
                      borderBottom: `1px solid ${theme.colors.border.light}`,
                      color: theme.colors.text.primary,
                      textAlign: column.align || "left",
                    }}
                  >
                    {row[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div
          style={{
            padding: theme.spacing.lg,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            borderTop: `1px solid ${theme.colors.border.glass}`,
          }}
        >
          <div
            style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
            }}
          >
            Showing {startIndex + 1} to {Math.min(startIndex + pageSize, sortedData.length)} of {sortedData.length} entries
          </div>
          
          <div style={{ display: "flex", gap: theme.spacing.sm }}>
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              style={{
                padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                background: theme.colors.surface,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                color: currentPage === 1 ? theme.colors.text.disabled : theme.colors.text.primary,
                cursor: currentPage === 1 ? "not-allowed" : "pointer",
                transition: theme.transitions.fast,
                fontSize: theme.typography.fontSize.sm,
              }}
              onMouseEnter={(e) => {
                if (currentPage !== 1) {
                  e.currentTarget.style.background = theme.colors.surfaceElevated;
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = theme.colors.surface;
              }}
            >
              Previous
            </button>
            
            {[...Array(Math.min(5, totalPages))].map((_, i) => {
              const pageNum = i + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                    background: currentPage === pageNum ? theme.colors.primary : theme.colors.surface,
                    border: `1px solid ${currentPage === pageNum ? theme.colors.primary : theme.colors.border.glass}`,
                    borderRadius: theme.borderRadius.md,
                    color: currentPage === pageNum ? "white" : theme.colors.text.primary,
                    cursor: "pointer",
                    transition: theme.transitions.fast,
                    fontSize: theme.typography.fontSize.sm,
                    minWidth: "36px",
                  }}
                >
                  {pageNum}
                </button>
              );
            })}
            
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              style={{
                padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                background: theme.colors.surface,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                color: currentPage === totalPages ? theme.colors.text.disabled : theme.colors.text.primary,
                cursor: currentPage === totalPages ? "not-allowed" : "pointer",
                transition: theme.transitions.fast,
                fontSize: theme.typography.fontSize.sm,
              }}
              onMouseEnter={(e) => {
                if (currentPage !== totalPages) {
                  e.currentTarget.style.background = theme.colors.surfaceElevated;
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = theme.colors.surface;
              }}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};