/* Import animations - must be at the top */
@import './styles/animations.css';

/* Import Sisense widget styling overrides */
@import './styles/sisense-overrides.css';

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-size: 16px;

  color-scheme: light;
  color: #1e293b;
  background-color: #f8fafc;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #f8fafc;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: #1e293b;
}

a {
  font-weight: 500;
  color: #2563eb;
  text-decoration: none;
  transition: color 150ms ease-in-out;
}

a:hover {
  color: #1d4ed8;
}

button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  cursor: pointer;
  transition: all 150ms ease-in-out;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Utility classes */
.gradient-text {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sisense SDK overrides for consistent styling */
.csdk-chart-widget {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.csdk-table-container {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.csdk-pivot-table {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Responsive Dashboard Grid */
.dashboard-grid {
  /* Default 12-column grid for large screens */
  grid-template-columns: repeat(12, 1fr);
}

/* Large tablets and small desktops (1200px and below) */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(8, 1fr);
    max-width: 1000px;
  }

  /* Adjust widget spans for 8-column grid - widgets wider than 8 columns get clamped */
  .dashboard-grid > * {
    grid-column: span min(var(--widget-width, 4), 8);
  }
}

/* Tablets (768px and below) */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    max-width: 100%;
    padding: 0 16px;
    gap: 16px;
  }

  /* Adjust widget spans for 4-column grid */
  .dashboard-grid > * {
    grid-column: span min(var(--widget-width, 4), 4);
  }

  /* Wide widgets (6+ columns) take full width on tablets */
  .dashboard-grid > *[style*="--widget-width: 6"],
  .dashboard-grid > *[style*="--widget-width: 7"],
  .dashboard-grid > *[style*="--widget-width: 8"],
  .dashboard-grid > *[style*="--widget-width: 9"],
  .dashboard-grid > *[style*="--widget-width: 10"],
  .dashboard-grid > *[style*="--widget-width: 11"],
  .dashboard-grid > *[style*="--widget-width: 12"] {
    grid-column: span 4;
  }
}

/* Mobile phones (480px and below) */
@media (max-width: 480px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 12px;
  }

  /* Most widgets take full width on mobile */
  .dashboard-grid > * {
    grid-column: span 2;
  }

  /* KPI widgets can be smaller */
  .dashboard-grid > .widget-kpi {
    grid-column: span 1;
  }
}

/* Extra small screens (320px and below) */
@media (max-width: 320px) {
  .dashboard-grid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
    padding: 0 8px !important;
  }

  /* All widgets take full width */
  .dashboard-grid > * {
    grid-column: span 1 !important;
  }
}

/* Responsive chart containers */
.dashboard-grid .widget-chart {
  min-height: 300px;
  overflow: hidden; /* Prevent content from overflowing */
}

.dashboard-grid .widget-kpi {
  min-height: 150px;
}

.dashboard-grid .widget-table,
.dashboard-grid .widget-pivot {
  min-height: 400px;
  overflow: auto; /* Allow scrolling for tables */
}

/* Ensure widgets maintain proper aspect ratios */
.dashboard-grid > * {
  display: flex;
  flex-direction: column;
}

/* Prevent widget content from being compressed */
.dashboard-grid .widget-chart > div,
.dashboard-grid .widget-table > div,
.dashboard-grid .widget-pivot > div,
.dashboard-grid .widget-kpi > div {
  flex: 1;
  min-height: 0;
  width: 100%;
}

/* Ensure all widgets fit within their containers */
.dashboard-grid > * {
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Ensure charts are responsive */
.dashboard-grid .widget-chart > div,
.dashboard-grid .widget-chart canvas,
.dashboard-grid .widget-chart svg {
  max-width: 100%;
  height: auto;
}

/* Special handling for pie charts */
.dashboard-grid .widget-chart[data-chart-type="pie"] {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-grid .widget-chart[data-chart-type="pie"] > div {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sisense widget responsive overrides */
.csdk-chart-widget,
.csdk-chart-widget > div,
.csdk-table-container,
.csdk-table-container > div,
.csdk-pivot-table,
.csdk-pivot-table > div {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  min-height: 100% !important;
}

/* Ensure Sisense widgets fill their containers completely */
[data-oid="namlriz"],
[data-oid="namlriz"] > div,
[data-oid="widget-wrapper"],
[data-oid="widget-wrapper"] > div {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Fix table widget compression issues */
.dashboard-grid .widget-table [data-oid="namlriz"],
.dashboard-grid .widget-pivot [data-oid="namlriz"] {
  min-height: 400px !important;
}

/* Ensure table content is not compressed and uses full width */
.csdk-table-container,
.csdk-table-container table,
.csdk-pivot-table,
.csdk-pivot-table table {
  width: 100% !important;
  table-layout: auto !important;
}

/* Force all Sisense table containers to use full width */
.csdk-table-container > div,
.csdk-pivot-table > div,
.csdk-table-container .csdk-table,
.csdk-pivot-table .csdk-pivot {
  width: 100% !important;
  max-width: 100% !important;
}

.csdk-table-container th,
.csdk-table-container td,
.csdk-pivot-table th,
.csdk-pivot-table td {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  min-width: fit-content !important;
  padding: 8px 12px !important;
}

/* Ensure table headers and data cells expand properly */
.csdk-table-container thead,
.csdk-table-container tbody,
.csdk-pivot-table thead,
.csdk-pivot-table tbody {
  width: 100% !important;
}

/* Force table rows to use full width */
.csdk-table-container tr,
.csdk-pivot-table tr {
  width: 100% !important;
  display: table-row !important;
}

/* Additional table width enforcement for all possible Sisense table classes */
[class*="csdk-table"],
[class*="table-container"],
[class*="pivot-table"],
[class*="sisense-table"] {
  width: 100% !important;
  max-width: 100% !important;
}

/* Ensure any nested table elements use full width */
.dashboard-grid .widget-table table,
.dashboard-grid .widget-pivot table,
.widget-table table,
.widget-pivot table {
  width: 100% !important;
  table-layout: auto !important;
  border-collapse: collapse !important;
}

/* Force table containers within widgets to expand */
.dashboard-grid .widget-table > div > div,
.dashboard-grid .widget-pivot > div > div {
  width: 100% !important;
  overflow-x: auto !important;
}

/* Ensure table wrapper divs don't constrain width */
.csdk-table-container > div > div,
.csdk-pivot-table > div > div {
  width: 100% !important;
  min-width: 100% !important;
}

/* Prevent horizontal scrolling */
.dashboard-grid {
  overflow-x: hidden;
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .dashboard-grid .widget-chart {
    min-height: 250px;
  }

  .dashboard-grid .widget-kpi {
    min-height: 120px;
  }

  .dashboard-grid .widget-table,
  .dashboard-grid .widget-pivot {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .dashboard-grid .widget-chart {
    min-height: 200px;
  }

  .dashboard-grid .widget-kpi {
    min-height: 100px;
  }

  .dashboard-grid .widget-table,
  .dashboard-grid .widget-pivot {
    min-height: 250px;
  }
}