/**
 * Data Model Inspector Utility
 * 
 * This utility helps identify the correct data model attributes from your Sisense instance
 * to enable proper filter functionality.
 */

export interface DataModelAttribute {
  name: string;
  type: string;
  expression: string;
  table?: string;
  column?: string;
}

export interface DataModelInspectionResult {
  dataSource: string;
  tables: {
    [tableName: string]: {
      columns: {
        [columnName: string]: DataModelAttribute;
      };
    };
  };
}

/**
 * Extracts data model information from .dash files
 * This helps identify the correct attribute expressions for filters
 */
export class DataModelInspector {
  
  /**
   * Analyzes a .dash file to extract data model information
   */
  static analyzeDashFile(dashContent: any): DataModelInspectionResult {
    const result: DataModelInspectionResult = {
      dataSource: dashContent.datasource?.title || 'Unknown',
      tables: {},
    };

    // Extract from widgets
    if (dashContent.widgets) {
      dashContent.widgets.forEach((widget: any) => {
        this.extractFromWidget(widget, result);
      });
    }

    // Extract from filters
    if (dashContent.filters) {
      dashContent.filters.forEach((filter: any) => {
        this.extractFromFilter(filter, result);
      });
    }

    return result;
  }

  /**
   * Extracts data model info from a widget
   */
  private static extractFromWidget(widget: any, result: DataModelInspectionResult) {
    if (widget.metadata?.panels) {
      widget.metadata.panels.forEach((panel: any) => {
        if (panel.items) {
          panel.items.forEach((item: any) => {
            this.extractFromJAQL(item.jaql, result);
            
            // Extract from context if available
            if (item.jaql?.context) {
              Object.values(item.jaql.context).forEach((contextItem: any) => {
                this.extractFromJAQL(contextItem, result);
              });
            }
          });
        }
      });
    }
  }

  /**
   * Extracts data model info from a filter
   */
  private static extractFromFilter(filter: any, result: DataModelInspectionResult) {
    if (filter.jaql) {
      this.extractFromJAQL(filter.jaql, result);
    }
  }

  /**
   * Extracts data model info from a JAQL object
   */
  private static extractFromJAQL(jaql: any, result: DataModelInspectionResult) {
    if (jaql?.table && jaql?.column) {
      const tableName = jaql.table;
      const columnName = jaql.column;
      
      if (!result.tables[tableName]) {
        result.tables[tableName] = { columns: {} };
      }

      result.tables[tableName].columns[columnName] = {
        name: jaql.title || columnName,
        type: jaql.datatype || 'unknown',
        expression: jaql.dim || `[${tableName}.${columnName}]`,
        table: tableName,
        column: columnName,
      };
    }
  }

  /**
   * Generates TypeScript code for the data model configuration
   */
  static generateDataModelCode(inspection: DataModelInspectionResult): string {
    let code = `// Generated data model configuration for ${inspection.dataSource}\n`;
    code += `import { createAttribute } from '@sisense/sdk-data';\n\n`;
    code += `export const DM = {\n`;
    code += `  ${this.toCamelCase(inspection.dataSource)}: {\n`;

    Object.entries(inspection.tables).forEach(([tableName, table]) => {
      const tableKey = this.toCamelCase(tableName.replace('.csv', ''));
      code += `    ${tableKey}: {\n`;

      Object.entries(table.columns).forEach(([columnName, column]) => {
        const columnKey = this.toPascalCase(columnName);
        code += `      ${columnKey}: createAttribute({\n`;
        code += `        name: '${column.name}',\n`;
        code += `        type: '${column.type}',\n`;
        code += `        expression: '${column.expression}',\n`;
        code += `      }),\n`;
      });

      code += `    },\n`;
    });

    code += `  },\n`;
    code += `};\n`;

    return code;
  }

  /**
   * Generates filter configuration based on discovered attributes
   */
  static generateFilterConfig(inspection: DataModelInspectionResult): string {
    let code = `// Generated filter configuration\n`;
    code += `const availableFilters = [\n`;

    // Look for common filter attributes
    Object.entries(inspection.tables).forEach(([tableName, table]) => {
      Object.entries(table.columns).forEach(([columnName, column]) => {
        if (this.isFilterableColumn(columnName, column)) {
          const filterId = `${columnName.toLowerCase()}-filter`;
          const filterTitle = column.name;
          
          code += `  {\n`;
          code += `    id: '${filterId}',\n`;
          code += `    title: '${filterTitle}',\n`;
          code += `    type: '${this.getFilterType(column.type)}',\n`;
          
          if (column.type === 'text') {
            code += `    options: ['All ${filterTitle}s', /* Add specific options */],\n`;
            code += `    defaultValue: 'All ${filterTitle}s',\n`;
          }
          
          code += `  },\n`;
        }
      });
    });

    code += `];\n`;
    return code;
  }

  /**
   * Determines if a column is suitable for filtering
   */
  private static isFilterableColumn(columnName: string, column: DataModelAttribute): boolean {
    const filterableNames = ['mode', 'state', 'year', 'category', 'type', 'status', 'region'];
    const lowerColumnName = columnName.toLowerCase();
    
    return filterableNames.some(name => lowerColumnName.includes(name)) ||
           column.type === 'text' && !lowerColumnName.includes('id');
  }

  /**
   * Maps data types to filter types
   */
  private static getFilterType(dataType: string): string {
    switch (dataType) {
      case 'datetime':
        return 'dateRange';
      case 'numeric':
        return 'criteria';
      case 'text':
      default:
        return 'select';
    }
  }

  /**
   * Converts string to camelCase
   */
  private static toCamelCase(str: string): string {
    return str.replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
              .replace(/^[A-Z]/, (chr) => chr.toLowerCase());
  }

  /**
   * Converts string to PascalCase
   */
  private static toPascalCase(str: string): string {
    return str.replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
              .replace(/^[a-z]/, (chr) => chr.toUpperCase());
  }
}

/**
 * Usage example:
 * 
 * import dashData from './path/to/dashboard.dash';
 * const inspection = DataModelInspector.analyzeDashFile(dashData);
 * const dataModelCode = DataModelInspector.generateDataModelCode(inspection);
 * const filterConfig = DataModelInspector.generateFilterConfig(inspection);
 * 
 * console.log('Data Model Code:', dataModelCode);
 * console.log('Filter Config:', filterConfig);
 */
