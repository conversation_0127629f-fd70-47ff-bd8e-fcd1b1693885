import type { Theme } from '../types';

/**
 * Widget styling utilities for consistent Sisense widget appearance
 * that integrates with the liquid glass theme
 */

export interface WidgetStyleOptions {
  height?: number;
  width?: number | string;
  minHeight?: number;
  backgroundColor?: string;
  border?: string;
  borderRadius?: string;
  chart?: {
    backgroundColor?: string;
    textColor?: string;
    gridLineColor?: string;
    plotBackgroundColor?: string;
  };
  legend?: {
    enabled?: boolean;
    position?: string;
    backgroundColor?: string;
    itemStyle?: {
      color?: string;
      fontSize?: string;
      fontWeight?: string;
    };
  };
  xAxis?: {
    enabled?: boolean;
    gridLines?: boolean;
    lineColor?: string;
    labels?: {
      color?: string;
      fontSize?: string;
      fontWeight?: string;
    };
  };
  yAxis?: {
    enabled?: boolean;
    gridLines?: boolean;
    gridLineColor?: string;
    lineColor?: string;
    labels?: {
      color?: string;
      fontSize?: string;
      fontWeight?: string;
    };
  };
  dataLabels?: {
    enabled?: boolean;
  };
  tooltip?: {
    backgroundColor?: string;
    borderRadius?: string;
    borderColor?: string;
    borderWidth?: number;
    shadow?: boolean;
    style?: {
      color?: string;
      fontSize?: string;
      fontWeight?: string;
    };
  };
  seriesColors?: string[];
  [key: string]: any;
}

/**
 * Creates consistent widget styling that integrates with the liquid glass theme
 */
export const createWidgetStyleOptions = (
  theme: Theme,
  overrides: Partial<WidgetStyleOptions> = {}
): WidgetStyleOptions => {
  const baseStyles: WidgetStyleOptions = {
    // Size & Layout
    width: '100%',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '16px',

    // Hide widget container borders and frames
    widget: {
      border: 'none',
      borderRadius: '0px',
      boxShadow: 'none',
      outline: 'none',
    },

    // Additional border removal
    container: {
      border: 'none',
      borderWidth: 0,
      borderStyle: 'none',
      outline: 'none',
      boxShadow: 'none',
    },
    
    // Chart styling
    chart: {
      backgroundColor: 'rgba(255, 255, 255, 0.6)',
      textColor: theme.colors.text.primary,
      gridLineColor: `${theme.colors.primary}20`,
      plotBackgroundColor: 'transparent',
    },
    
    // Legend styling
    legend: {
      enabled: true,
      position: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: theme.colors.text.secondary,
        fontSize: '12px',
        fontWeight: '500',
      }
    },
    
    // X-Axis styling
    xAxis: {
      enabled: true,
      gridLines: false,
      lineColor: `${theme.colors.primary}30`,
      labels: {
        color: theme.colors.text.secondary,
        fontSize: '12px',
        fontWeight: '500',
      }
    },
    
    // Y-Axis styling
    yAxis: {
      enabled: true,
      gridLines: true,
      gridLineColor: `${theme.colors.primary}15`,
      lineColor: `${theme.colors.primary}30`,
      labels: {
        color: theme.colors.text.secondary,
        fontSize: '12px',
        fontWeight: '500',
      }
    },
    
    // Data labels
    dataLabels: {
      enabled: false,
    },
    
    // Tooltip styling
    tooltip: {
      backgroundColor: theme.colors.surfaceElevated,
      borderRadius: theme.borderRadius.lg,
      borderColor: `${theme.colors.primary}30`,
      borderWidth: 1,
      shadow: true,
      style: {
        color: theme.colors.text.primary,
        fontSize: '13px',
        fontWeight: '500',
      }
    },
    
    // Series colors for consistency with theme
    seriesColors: [
      theme.colors.primary,      // Primary orange
      theme.colors.secondary,    // Secondary orange
      theme.colors.accent,       // Accent red
      '#00cee6',                // Cyan
      '#6EDA55',                // Green
      '#9b9bd7',                // Purple
      theme.colors.secondaryLight, // Light orange
      theme.colors.primaryLight,   // Light primary
      '#ff8c5a',                // Additional orange
      '#ffbc6e',                // Additional light orange
    ],
  };

  // Deep merge overrides with base styles
  return deepMerge(baseStyles, overrides);
};

/**
 * Creates widget styles optimized for specific widget types
 */
export const createWidgetStylesByType = (
  theme: Theme,
  widgetType: 'chart' | 'kpi' | 'table' | 'pivot',
  height: number,
  overrides: Partial<WidgetStyleOptions> = {}
): WidgetStyleOptions => {
  const baseStyles = createWidgetStyleOptions(theme);
  
  let typeSpecificStyles: Partial<WidgetStyleOptions> = {};
  
  switch (widgetType) {
    case 'kpi':
      typeSpecificStyles = {
        height: Math.max(height, 200),
        chart: {
          ...baseStyles.chart,
          backgroundColor: 'transparent',
        },
        legend: {
          enabled: false,
        },
      };
      break;
      
    case 'table':
    case 'pivot':
      typeSpecificStyles = {
        height: Math.max(height, 400),
        minHeight: 400,
        chart: {
          ...baseStyles.chart,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
        },
      };
      break;
      
    case 'chart':
    default:
      typeSpecificStyles = {
        height: Math.max(height, 300),
        minHeight: 300,
      };
      break;
  }
  
  return deepMerge(baseStyles, typeSpecificStyles, overrides);
};

/**
 * Deep merge utility function
 */
function deepMerge(...objects: any[]): any {
  const result: any = {};
  
  for (const obj of objects) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          result[key] = deepMerge(result[key] || {}, obj[key]);
        } else {
          result[key] = obj[key];
        }
      }
    }
  }
  
  return result;
}

/**
 * Predefined style presets for common use cases
 */
export const widgetStylePresets = {
  /**
   * Minimal style with just basic theming
   */
  minimal: (theme: Theme): WidgetStyleOptions => ({
    backgroundColor: 'transparent',
    border: 'none',
    seriesColors: [theme.colors.primary, theme.colors.secondary, theme.colors.accent],
  }),
  
  /**
   * Enhanced glass effect with more pronounced styling
   */
  glassEnhanced: (theme: Theme): WidgetStyleOptions => ({
    ...createWidgetStyleOptions(theme),
    chart: {
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      textColor: theme.colors.text.primary,
      gridLineColor: `${theme.colors.primary}25`,
      plotBackgroundColor: 'rgba(255, 255, 255, 0.4)',
    },
  }),
  
  /**
   * Dark mode compatible styling
   */
  darkMode: (theme: Theme): WidgetStyleOptions => ({
    ...createWidgetStyleOptions(theme),
    chart: {
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      textColor: '#ffffff',
      gridLineColor: 'rgba(255, 255, 255, 0.2)',
      plotBackgroundColor: 'transparent',
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      borderColor: `${theme.colors.primary}50`,
      style: {
        color: '#ffffff',
        fontSize: '13px',
        fontWeight: '500',
      }
    },
  }),
};
