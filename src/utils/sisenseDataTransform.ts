import type { QueryResultData } from '@sisense/sdk-ui';

export interface TransformedDataPoint {
  [key: string]: string | number | Date;
}

export interface ChartDataConfig {
  xAxisKey?: string;
  yAxisKey?: string;
  categoryKey?: string;
  valueKey?: string;
  nameKey?: string;
}

/**
 * Transform Sisense query result data to format suitable for Recharts
 * @param data - Raw data from useExecuteQueryByWidgetId
 * @param config - Configuration for data transformation
 * @returns Transformed data array suitable for Recharts
 */
export const transformSisenseData = (
  data: QueryResultData | undefined,
  config: ChartDataConfig = {}
): TransformedDataPoint[] => {
  if (!data || !data.rows || data.rows.length === 0) {
    return [];
  }

  const {
    xAxisKey = 'x',
    yAxisKey = 'y',
    categoryKey = 'category',
    valueKey = 'value',
    nameKey = 'name'
  } = config;

  return data.rows.map((row, index) => {
    const transformedPoint: TransformedDataPoint = {};

    row.forEach((cell, cellIndex) => {
      const column = data.columns?.[cellIndex];
      const columnName = column?.name || `column_${cellIndex}`;
      
      // Handle different data types
      let value: string | number | Date = cell.data;
      
      // Convert dates
      if (column?.type === 'datetime' || column?.type === 'date') {
        value = new Date(cell.data);
      }
      
      // Convert numbers
      if (column?.type === 'numeric' && typeof cell.data === 'string') {
        const numValue = parseFloat(cell.data);
        value = isNaN(numValue) ? 0 : numValue;
      }

      // Use configured keys or default column names
      if (cellIndex === 0) {
        transformedPoint[categoryKey] = value;
        transformedPoint[nameKey] = value;
        transformedPoint[xAxisKey] = value;
      } else if (cellIndex === 1) {
        transformedPoint[valueKey] = value;
        transformedPoint[yAxisKey] = value;
      } else {
        transformedPoint[columnName] = value;
      }
    });

    return transformedPoint;
  });
};

/**
 * Transform data specifically for time series charts
 */
export const transformTimeSeriesData = (
  data: QueryResultData | undefined,
  dateKey: string = 'date',
  valueKey: string = 'value'
): TransformedDataPoint[] => {
  if (!data || !data.rows || data.rows.length === 0) {
    return [];
  }

  return data.rows.map((row) => {
    const point: TransformedDataPoint = {};
    
    row.forEach((cell, index) => {
      const column = data.columns?.[index];
      
      if (index === 0) {
        // Assume first column is date
        point[dateKey] = new Date(cell.data);
      } else {
        // Subsequent columns are values
        const columnName = column?.name || valueKey;
        const numValue = typeof cell.data === 'string' ? parseFloat(cell.data) : cell.data;
        point[columnName] = isNaN(numValue) ? 0 : numValue;
      }
    });

    return point;
  });
};

/**
 * Transform data for pie charts
 */
export const transformPieChartData = (
  data: QueryResultData | undefined,
  nameKey: string = 'name',
  valueKey: string = 'value'
): TransformedDataPoint[] => {
  const transformed = transformSisenseData(data, { nameKey, valueKey });
  
  // Calculate percentages for pie chart
  const total = transformed.reduce((sum, item) => sum + (Number(item[valueKey]) || 0), 0);
  
  return transformed.map((item) => ({
    ...item,
    percentage: total > 0 ? ((Number(item[valueKey]) || 0) / total * 100).toFixed(1) : 0
  }));
};

/**
 * Format large numbers for display
 */
export const formatValue = (value: number, type: 'currency' | 'number' | 'percentage' = 'number'): string => {
  if (type === 'currency') {
    if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(1)}B`;
    } else if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(1)}M`;
    } else if (value >= 1e3) {
      return `$${(value / 1e3).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  }
  
  if (type === 'percentage') {
    return `${value.toFixed(1)}%`;
  }
  
  if (value >= 1e9) {
    return `${(value / 1e9).toFixed(1)}B`;
  } else if (value >= 1e6) {
    return `${(value / 1e6).toFixed(1)}M`;
  } else if (value >= 1e3) {
    return `${(value / 1e3).toFixed(1)}K`;
  }
  
  return value.toLocaleString();
};
