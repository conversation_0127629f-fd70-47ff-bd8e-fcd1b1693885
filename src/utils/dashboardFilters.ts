/**
 * Dashboard Filter Integration Utilities
 * 
 * This file provides utilities to easily integrate filters into any dashboard
 */

import type { FilterValue } from '../components/filters/UniversalDashboardFilters';
import type { DashboardFilterConfig, FilterConfig } from '../config/filters.config';
import { createCustomFilterConfig } from '../config/filters.config';

/**
 * Hook-like function to manage dashboard filters
 * Use this in your dashboard components for consistent filter management
 */
export const useDashboardFilters = (initialFilters: FilterValue[] = []) => {
  // This would typically use React hooks, but for now we'll provide the structure
  // In a real implementation, this would use useState and useEffect
  
  return {
    filters: initialFilters,
    setFilters: (filters: FilterValue[]) => {
      // Implementation would go here
      console.log('Filters updated:', filters);
    },
    clearFilters: () => {
      // Implementation would go here
      console.log('Filters cleared');
    },
    getActiveFilters: () => {
      return initialFilters.filter(f => f.value && !String(f.value).startsWith('All'));
    }
  };
};

/**
 * Convert filter values to Sisense-compatible format
 */
export const convertFiltersToSisense = (filters: FilterValue[]) => {
  return filters.map(filter => ({
    id: filter.id,
    value: filter.value,
    // Add Sisense-specific properties as needed
  }));
};

/**
 * Dashboard filter presets for quick setup
 */
export const DASHBOARD_FILTER_PRESETS = {
  /**
   * Basic state and mode filters (most common)
   */
  STATE_AND_MODE: createCustomFilterConfig(
    'state-and-mode',
    'State and Transportation Mode Filters',
    [
      {
        id: 'state-filter',
        title: 'State',
        type: 'select',
        options: [
          'All States',
          'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
          'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
          'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
          'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
          'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
          'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
          'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
          'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
          'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
          'West Virginia', 'Wisconsin', 'Wyoming'
        ],
        defaultValue: 'All States',
        width: 'medium'
      },
      {
        id: 'mode-filter',
        title: 'Transportation Mode',
        type: 'select',
        options: [
          'All Modes',
          'Airport',
          'Bridge & Tunnel',
          'Highway & Pavement',
          'Rail & Transit',
          'Waterway'
        ],
        defaultValue: 'All Modes',
        width: 'medium'
      }
    ]
  ),

  /**
   * Time-based filters
   */
  TIME_BASED: createCustomFilterConfig(
    'time-based',
    'Time Period Filters',
    [
      {
        id: 'year-filter',
        title: 'Year',
        type: 'select',
        options: [
          'All Years',
          '2024', '2023', '2022', '2021', '2020', '2019', '2018'
        ],
        defaultValue: 'All Years',
        width: 'small'
      },
      {
        id: 'period-filter',
        title: 'Time Period',
        type: 'select',
        options: [
          'All Time',
          'Last 30 Days',
          'Last 90 Days',
          'Last 6 Months',
          'Last Year',
          'Year to Date'
        ],
        defaultValue: 'All Time',
        width: 'medium'
      }
    ]
  ),

  /**
   * Budget/Financial filters
   */
  FINANCIAL: createCustomFilterConfig(
    'financial',
    'Financial Filters',
    [
      {
        id: 'amount-filter',
        title: 'Amount Range',
        type: 'select',
        options: [
          'All Amounts',
          'Under $1M',
          '$1M - $10M',
          '$10M - $50M',
          '$50M - $100M',
          'Over $100M'
        ],
        defaultValue: 'All Amounts',
        width: 'medium'
      },
      {
        id: 'funding-filter',
        title: 'Funding Source',
        type: 'select',
        options: [
          'All Sources',
          'Federal',
          'State',
          'Local',
          'Private',
          'Mixed'
        ],
        defaultValue: 'All Sources',
        width: 'medium'
      }
    ]
  ),

  /**
   * Search and text filters
   */
  SEARCH_BASED: createCustomFilterConfig(
    'search-based',
    'Search Filters',
    [
      {
        id: 'search-filter',
        title: 'Search',
        type: 'text',
        placeholder: 'Search projects, contractors, etc...',
        width: 'large'
      },
      {
        id: 'contractor-filter',
        title: 'Contractor',
        type: 'text',
        placeholder: 'Enter contractor name...',
        width: 'medium'
      }
    ]
  )
};

/**
 * Quick setup functions for common dashboard types
 */
export const setupDashboardFilters = {
  /**
   * Setup for Contract Awards type dashboards
   */
  contractAwards: () => ({
    dashboardType: 'CONTRACT_AWARDS' as const,
    description: 'Filter contract awards by state and transportation mode'
  }),

  /**
   * Setup for Federal Aid type dashboards
   */
  federalAid: () => ({
    dashboardType: 'FEDERAL_AID' as const,
    description: 'Filter federal aid data by state, program, and year'
  }),

  /**
   * Setup for Material Prices type dashboards
   */
  materialPrices: () => ({
    dashboardType: 'MATERIAL_PRICES' as const,
    description: 'Filter material prices by region, material type, and time period'
  }),

  /**
   * Setup for State DOT Budgets type dashboards
   */
  stateDOTBudgets: () => ({
    dashboardType: 'STATE_DOT_BUDGETS' as const,
    description: 'Filter state DOT budget data by state, category, and amount'
  }),

  /**
   * Setup for Summary type dashboards
   */
  summary: () => ({
    dashboardType: 'SUMMARY' as const,
    description: 'High-level filters for summary dashboard'
  }),

  /**
   * Setup for Value Put in Place type dashboards
   */
  valuePutInPlace: () => ({
    dashboardType: 'VALUE_PUT_IN_PLACE' as const,
    description: 'Filter VPIP data by state, mode, and time period'
  }),

  /**
   * Setup with custom configuration
   */
  custom: (config: DashboardFilterConfig) => ({
    customConfig: config,
    description: config.description || 'Custom dashboard filters'
  })
};

/**
 * Helper to create a minimal filter setup
 */
export const createQuickFilters = (
  filters: Array<{
    id: string;
    title: string;
    type: 'select' | 'text';
    options?: string[];
    placeholder?: string;
  }>
): DashboardFilterConfig => {
  return createCustomFilterConfig(
    'quick-filters',
    'Quick Filters',
    filters.map(f => ({
      ...f,
      defaultValue: f.type === 'select' ? (f.options?.[0] || '') : '',
      width: 'medium' as const
    }))
  );
};

/**
 * Validation helpers
 */
export const validateFilterConfig = (config: DashboardFilterConfig): boolean => {
  if (!config.id || !config.name || !config.filters) {
    console.error('Invalid filter configuration: missing required fields');
    return false;
  }

  for (const filter of config.filters) {
    if (!filter.id || !filter.title || !filter.type) {
      console.error(`Invalid filter configuration: filter ${filter.id} missing required fields`);
      return false;
    }

    if (filter.type === 'select' && (!filter.options || filter.options.length === 0)) {
      console.error(`Invalid filter configuration: select filter ${filter.id} has no options`);
      return false;
    }
  }

  return true;
};

/**
 * Debug helper to log filter state
 */
export const debugFilters = (filters: FilterValue[], context: string = 'Dashboard') => {
  console.group(`🔍 ${context} Filters Debug`);
  console.log('Active filters:', filters.length);
  filters.forEach(filter => {
    console.log(`  ${filter.id}: ${filter.value} (${filter.label})`);
  });
  console.groupEnd();
};
