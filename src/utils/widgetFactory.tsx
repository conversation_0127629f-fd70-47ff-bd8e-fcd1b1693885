import React from "react";
import { SisenseWidget } from "../components/widgets/SisenseWidget/SisenseWidget";
import { FilteredSisenseWidget } from "../components/widgets/FilteredSisenseWidget/FilteredSisenseWidget";
import { PlaceholderWidget } from "../components/widgets/PlaceholderWidget/PlaceholderWidget";
import { WidgetContainer } from "../components/layout/WidgetContainer";
import { ErrorBoundary } from "../components/ErrorBoundary/ErrorBoundary";
import type { WidgetConfig } from "../types/dashboard.types";

// Check if we're in development mode without Sisense connection
// When VITE_INSTANCE_URL is set, we'll use real Sisense widgets with the official WidgetById component
// When not set (development), we'll use placeholder widgets for development purposes
const isDevelopmentMode =
  import.meta.env.DEV && !import.meta.env.VITE_INSTANCE_URL;

/**
 * Creates a widget component using the official Sisense WidgetById component
 *
 * This factory function creates widgets that use the official Sisense SDK WidgetById component
 * as documented at: https://sisense.dev/guides/sdk/modules/sdk-ui/fusion-assets/function.WidgetById.html
 *
 * @param widget - Widget configuration containing ID, position, and other properties
 * @param dashboardId - The Sisense dashboard ID that contains the widget
 * @param activeTab - The currently active tab to determine widget visibility
 * @param filters - Optional array of filters to apply to the widget
 * @returns React element with either a Sisense widget or placeholder widget, or null if not visible
 */
export const createWidget = (
  widget: WidgetConfig,
  dashboardId: string,
  activeTab?: string,
  filters?: any[],
): React.ReactElement | null => {
  // Check if widget should be visible based on active tab
  if (widget.visibleOn && activeTab && !widget.visibleOn.includes(activeTab)) {
    return null;
  }
  // Use placeholder widgets in development mode without Sisense connection
  if (isDevelopmentMode) {
    return (
      <WidgetContainer
        key={widget.id}
        title={widget.title}
        position={widget.position}
        widgetType={widget.type as 'chart' | 'kpi' | 'table' | 'pivot'}
        data-oid="xvxfzm-"
      >
        <PlaceholderWidget
          title={widget.title}
          position={widget.position}
          type={
            widget.type as
              | "chart"
              | "kpi"
              | "table"
              | "filter"
              | "header"
              | "info"
              | "text"
          }
          widgetId={widget.id}
          config={widget.config}
        />
      </WidgetContainer>
    );
  }

  // Use the official Sisense WidgetById component to load actual Sisense widgets
  // This component is a thin wrapper on the ChartWidget component and renders widgets
  // created in a Sisense Fusion instance

  // Choose between regular and filtered widget based on whether filters are provided
  const WidgetComponent = filters && filters.length > 0 ? FilteredSisenseWidget : SisenseWidget;

  return (
    <ErrorBoundary key={widget.id} data-oid="tg4yktl">
      <WidgetComponent
        widgetId={widget.id}
        dashboardId={dashboardId}
        title={widget.title}
        position={widget.position}
        widgetType={widget.type as 'chart' | 'kpi' | 'table' | 'pivot'}
        includeDashboardFilters={true}
        styleOptions={{
          height: widget.position.h * 120, // Dynamic height based on grid position
          width: '100%', // Ensure full width utilization
          // Additional styling to prevent compression
          minHeight: widget.type === 'table' || widget.type === 'pivot' ? 400 : widget.position.h * 120,
        }}
        filters={filters}
        data-oid="149a52j"
      />
    </ErrorBoundary>
  );
};
