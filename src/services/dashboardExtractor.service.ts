import type { DashFile } from '../types/dash.types';
import type { DashboardConfig } from '../types/dashboard.types';

interface ExtractedDashboardInfo {
  id: string;
  title: string;
  description: string;
  widgetCount: number;
  dataSource: string;
}

export class DashboardExtractorService {
  /**
   * Extract basic dashboard information from .dash files
   */
  static extractDashboardInfo(dashContent: string): ExtractedDashboardInfo {
    const dash: DashFile = JSON.parse(dashContent);
    
    return {
      id: dash.source,
      title: dash.title,
      description: dash.desc || '',
      widgetCount: dash.widgets.length,
      dataSource: dash.datasource?.title || 'Unknown',
    };
  }

  /**
   * Extract dashboard configurations from all .dash files
   */
  static async extractAllDashboards(): Promise<DashboardConfig[]> {
    // These are the dashboard files we found
    const dashboardFiles = [
      { file: 'SummaryDashboard.dash', id: 'summary' },
      { file: '2ContractAwards.dash', id: 'contract-awards' },
      { file: '3ValuePutinPlace.dash', id: 'value-put-in-place' },
      { file: '4Federal-AidObligations.dash', id: 'federal-aid' },
      { file: '6StateLegislativeInitiatives.dash', id: 'state-legislative' },
      { file: '7StateDOTBudgets_2025.dash', id: 'state-dot-budgets' },
      { file: '9MaterialPrices.dash', id: 'material-prices' },
    ];

    // Since we can't read files directly in the browser, 
    // we'll create the dashboard configs based on the information we know
    return [
      {
        id: 'summary',
        title: '1. Summary Dashboard',
        description: 'Executive summary of key transportation metrics',
        path: '/dashboard/summary',
        icon: '📊',
        widgets: this.getSummaryDashboardWidgets(),
      },
      {
        id: 'contract-awards',
        title: '2. Contract Awards',
        description: 'State and local contract awards tracking',
        path: '/dashboard/contract-awards',
        icon: '📄',
        widgets: this.getContractAwardsWidgets(),
      },
      {
        id: 'value-put-in-place',
        title: '3. Value Put in Place',
        description: 'Construction value put in place analysis',
        path: '/dashboard/value-put-in-place',
        icon: '🏗️',
        widgets: this.getValuePutInPlaceWidgets(),
      },
      {
        id: 'federal-aid',
        title: '4. Federal-Aid Obligations',
        description: 'Federal highway aid obligations tracking',
        path: '/dashboard/federal-aid',
        icon: '🏛️',
        widgets: this.getFederalAidWidgets(),
      },
      {
        id: 'state-legislative',
        title: '6. State Legislative Initiatives',
        description: 'State transportation funding initiatives',
        path: '/dashboard/state-legislative',
        icon: '📜',
        widgets: this.getStateLegislativeWidgets(),
      },
      {
        id: 'state-dot-budgets',
        title: '7. State DOT Budgets 2025',
        description: 'State department of transportation budgets',
        path: '/dashboard/state-dot-budgets',
        icon: '💰',
        widgets: this.getStateDOTBudgetsWidgets(),
      },
      {
        id: 'material-prices',
        title: '9. Material Prices',
        description: 'Construction material price tracking',
        path: '/dashboard/material-prices',
        icon: '📈',
        widgets: this.getMaterialPricesWidgets(),
      },
    ];
  }

  /**
   * Get widgets for Summary Dashboard
   */
  private static getSummaryDashboardWidgets(): any[] {
    return [
      {
        id: 'ttm-contract-awards',
        type: 'kpi',
        title: 'TTM State & Local Contract Awards',
        position: { x: 0, y: 0, w: 6, h: 2 },
        config: {
          value: 248500000000,
          previousValue: 225000000000,
          format: 'currency',
          trend: 'up',
        },
      },
      {
        id: 'ytd-contract-awards',
        type: 'kpi',
        title: 'YTD Contract Awards',
        position: { x: 6, y: 0, w: 6, h: 2 },
        config: {
          value: 189500000000,
          previousValue: 175000000000,
          format: 'currency',
          trend: 'up',
        },
      },
      {
        id: 'awards-by-state',
        type: 'chart',
        title: 'Contract Awards by State',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'column',
          dataOptions: {
            category: [{ name: 'State' }],
            value: [{ name: 'Total Awards', aggregation: 'sum' }],
          },
        },
      },
      {
        id: 'awards-trend',
        type: 'chart',
        title: 'Contract Awards Trend',
        position: { x: 0, y: 6, w: 8, h: 4 },
        config: {
          chartType: 'line',
          dataOptions: {
            category: [{ name: 'Month' }],
            value: [{ name: 'Awards', aggregation: 'sum' }],
          },
        },
      },
      {
        id: 'top-contractors',
        type: 'table',
        title: 'Top Contractors',
        position: { x: 8, y: 6, w: 4, h: 4 },
        config: {
          dataOptions: {
            columns: [
              { column: { name: 'Contractor' } },
              { column: { name: 'Total Awards' } },
              { column: { name: 'Projects' } },
            ],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for Contract Awards Dashboard
   */
  private static getContractAwardsWidgets(): any[] {
    return [
      {
        id: 'total-awards',
        type: 'kpi',
        title: 'Total Contract Awards',
        position: { x: 0, y: 0, w: 3, h: 2 },
        config: {
          value: 248500000000,
          format: 'currency',
        },
      },
      {
        id: 'avg-contract-size',
        type: 'kpi',
        title: 'Average Contract Size',
        position: { x: 3, y: 0, w: 3, h: 2 },
        config: {
          value: 2450000,
          format: 'currency',
        },
      },
      {
        id: 'active-projects',
        type: 'kpi',
        title: 'Active Projects',
        position: { x: 6, y: 0, w: 3, h: 2 },
        config: {
          value: 101420,
          format: 'number',
        },
      },
      {
        id: 'yoy-growth',
        type: 'kpi',
        title: 'YoY Growth',
        position: { x: 9, y: 0, w: 3, h: 2 },
        config: {
          value: 10.4,
          format: 'percentage',
          trend: 'up',
        },
      },
      {
        id: 'awards-by-category',
        type: 'chart',
        title: 'Awards by Project Category',
        position: { x: 0, y: 2, w: 6, h: 4 },
        config: {
          chartType: 'pie',
          dataOptions: {
            category: [{ name: 'Category' }],
            value: [{ name: 'Awards', aggregation: 'sum' }],
          },
        },
      },
      {
        id: 'monthly-trend',
        type: 'chart',
        title: 'Monthly Awards Trend',
        position: { x: 6, y: 2, w: 6, h: 4 },
        config: {
          chartType: 'area',
          dataOptions: {
            category: [{ name: 'Month' }],
            value: [{ name: 'Awards', aggregation: 'sum' }],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for Value Put in Place Dashboard
   */
  private static getValuePutInPlaceWidgets(): any[] {
    return [
      {
        id: 'total-vpip',
        type: 'kpi',
        title: 'Total Value Put in Place',
        position: { x: 0, y: 0, w: 4, h: 2 },
        config: {
          value: 189700000000,
          format: 'currency',
        },
      },
      {
        id: 'highway-vpip',
        type: 'kpi',
        title: 'Highway & Street VPIP',
        position: { x: 4, y: 0, w: 4, h: 2 },
        config: {
          value: 112300000000,
          format: 'currency',
        },
      },
      {
        id: 'bridge-vpip',
        type: 'kpi',
        title: 'Bridge Construction VPIP',
        position: { x: 8, y: 0, w: 4, h: 2 },
        config: {
          value: 77400000000,
          format: 'currency',
        },
      },
      {
        id: 'vpip-by-type',
        type: 'chart',
        title: 'VPIP by Construction Type',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'column',
          dataOptions: {
            category: [{ name: 'Type' }],
            value: [{ name: 'Value', aggregation: 'sum' }],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for Federal Aid Dashboard
   */
  private static getFederalAidWidgets(): any[] {
    return [
      {
        id: 'total-obligations',
        type: 'kpi',
        title: 'Total Federal Obligations',
        position: { x: 0, y: 0, w: 6, h: 2 },
        config: {
          value: 52300000000,
          format: 'currency',
        },
      },
      {
        id: 'states-funded',
        type: 'kpi',
        title: 'States Funded',
        position: { x: 6, y: 0, w: 6, h: 2 },
        config: {
          value: 50,
          format: 'number',
        },
      },
      {
        id: 'obligations-by-state',
        type: 'chart',
        title: 'Federal Obligations by State',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'bar',
          dataOptions: {
            category: [{ name: 'State' }],
            value: [{ name: 'Obligations', aggregation: 'sum' }],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for State Legislative Dashboard
   */
  private static getStateLegislativeWidgets(): any[] {
    return [
      {
        id: 'active-measures',
        type: 'kpi',
        title: 'Active Ballot Measures',
        position: { x: 0, y: 0, w: 3, h: 2 },
        config: {
          value: 23,
          format: 'number',
        },
      },
      {
        id: 'passed-measures',
        type: 'kpi',
        title: 'Passed Measures',
        position: { x: 3, y: 0, w: 3, h: 2 },
        config: {
          value: 18,
          format: 'number',
        },
      },
      {
        id: 'funding-approved',
        type: 'kpi',
        title: 'Funding Approved',
        position: { x: 6, y: 0, w: 3, h: 2 },
        config: {
          value: 12500000000,
          format: 'currency',
        },
      },
      {
        id: 'states-with-measures',
        type: 'kpi',
        title: 'States with Measures',
        position: { x: 9, y: 0, w: 3, h: 2 },
        config: {
          value: 31,
          format: 'number',
        },
      },
      {
        id: 'measures-by-state',
        type: 'chart',
        title: 'Legislative Measures by State',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'column',
          dataOptions: {
            category: [{ name: 'State' }],
            value: [{ name: 'Measures', aggregation: 'count' }],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for State DOT Budgets Dashboard
   */
  private static getStateDOTBudgetsWidgets(): any[] {
    return [
      {
        id: 'total-budgets',
        type: 'kpi',
        title: 'Total State DOT Budgets',
        position: { x: 0, y: 0, w: 4, h: 2 },
        config: {
          value: 147800000000,
          format: 'currency',
        },
      },
      {
        id: 'avg-budget',
        type: 'kpi',
        title: 'Average State Budget',
        position: { x: 4, y: 0, w: 4, h: 2 },
        config: {
          value: 2956000000,
          format: 'currency',
        },
      },
      {
        id: 'budget-growth',
        type: 'kpi',
        title: 'YoY Budget Growth',
        position: { x: 8, y: 0, w: 4, h: 2 },
        config: {
          value: 5.2,
          format: 'percentage',
          trend: 'up',
        },
      },
      {
        id: 'budgets-by-state',
        type: 'chart',
        title: 'DOT Budgets by State',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'bar',
          dataOptions: {
            category: [{ name: 'State' }],
            value: [{ name: 'Budget', aggregation: 'sum' }],
          },
        },
      },
    ];
  }

  /**
   * Get widgets for Material Prices Dashboard
   */
  private static getMaterialPricesWidgets(): any[] {
    return [
      {
        id: 'asphalt-price',
        type: 'kpi',
        title: 'Asphalt Price Index',
        position: { x: 0, y: 0, w: 3, h: 2 },
        config: {
          value: 142.3,
          previousValue: 138.7,
          format: 'number',
          trend: 'up',
        },
      },
      {
        id: 'concrete-price',
        type: 'kpi',
        title: 'Concrete Price Index',
        position: { x: 3, y: 0, w: 3, h: 2 },
        config: {
          value: 156.8,
          previousValue: 151.2,
          format: 'number',
          trend: 'up',
        },
      },
      {
        id: 'steel-price',
        type: 'kpi',
        title: 'Steel Price Index',
        position: { x: 6, y: 0, w: 3, h: 2 },
        config: {
          value: 189.4,
          previousValue: 195.3,
          format: 'number',
          trend: 'down',
        },
      },
      {
        id: 'aggregate-price',
        type: 'kpi',
        title: 'Aggregate Price Index',
        position: { x: 9, y: 0, w: 3, h: 2 },
        config: {
          value: 134.6,
          previousValue: 132.1,
          format: 'number',
          trend: 'up',
        },
      },
      {
        id: 'price-trends',
        type: 'chart',
        title: 'Material Price Trends',
        position: { x: 0, y: 2, w: 12, h: 4 },
        config: {
          chartType: 'line',
          dataOptions: {
            category: [{ name: 'Month' }],
            value: [
              { name: 'Asphalt', aggregation: 'avg' },
              { name: 'Concrete', aggregation: 'avg' },
              { name: 'Steel', aggregation: 'avg' },
              { name: 'Aggregate', aggregation: 'avg' },
            ],
          },
        },
      },
    ];
  }
}