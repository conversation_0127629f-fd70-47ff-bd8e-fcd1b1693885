/**
 * Custom React hooks for Sisense integration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { sisenseService } from './SisenseService';
import type { 
  SisenseWidgetConfig, 
  SisenseQueryResult, 
  SisenseDataOptions,
  SisenseWidgetError,
  SisenseWidgetLoadState 
} from './types';

/**
 * Hook to track widget loading state and errors
 */
export function useSisenseWidget(config: SisenseWidgetConfig) {
  const [loadState, setLoadState] = useState<SisenseWidgetLoadState>('loading');
  const [error, setError] = useState<SisenseWidgetError | null>(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const handleLoad = useCallback(() => {
    if (mountedRef.current) {
      setLoadState('loaded');
      setError(null);
    }
  }, []);

  const handleError = useCallback((error: SisenseWidgetError) => {
    if (mountedRef.current) {
      setLoadState('error');
      setError(error);
    }
  }, []);

  // Subscribe to global Sisense errors
  useEffect(() => {
    const unsubscribe = sisenseService.onError((error) => {
      handleError(error);
    });

    return unsubscribe;
  }, [handleError]);

  return {
    loadState,
    error,
    isLoading: loadState === 'loading',
    isError: loadState === 'error',
    handleLoad,
    handleError,
  };
}

/**
 * Hook to execute Sisense queries with caching and error handling
 */
export function useSisenseQuery<T = any>(
  dataOptions: SisenseDataOptions,
  options?: {
    dataSource?: string;
    enabled?: boolean;
    cacheTime?: number;
  }
) {
  const [result, setResult] = useState<SisenseQueryResult<T>>({
    data: null as any,
    status: 'loading',
  });
  
  const cacheRef = useRef<{
    data: T;
    timestamp: number;
  } | null>(null);

  const { 
    dataSource = 'ARTBA Economics', 
    enabled = true,
    cacheTime = 5 * 60 * 1000 // 5 minutes default
  } = options || {};

  const executeQueryCallback = useCallback(async () => {
    if (!enabled || !sisenseService.isConfigured()) {
      return;
    }

    // Check cache
    if (cacheRef.current && cacheTime > 0) {
      const now = Date.now();
      if (now - cacheRef.current.timestamp < cacheTime) {
        setResult({
          data: cacheRef.current.data,
          status: 'success',
        });
        return;
      }
    }

    setResult(prev => ({ ...prev, status: 'loading' }));

    const queryResult = await sisenseService.executeQuery<T>(dataOptions, dataSource);
    
    if (queryResult.status === 'success' && queryResult.data) {
      // Update cache
      cacheRef.current = {
        data: queryResult.data,
        timestamp: Date.now(),
      };
    }

    setResult(queryResult);
  }, [dataOptions, dataSource, enabled, cacheTime]);

  useEffect(() => {
    executeQueryCallback();
  }, [executeQueryCallback]);

  const refetch = useCallback(() => {
    // Clear cache and refetch
    cacheRef.current = null;
    executeQueryCallback();
  }, [executeQueryCallback]);

  return {
    ...result,
    refetch,
    isLoading: result.status === 'loading',
    isError: result.status === 'error',
    isSuccess: result.status === 'success',
  };
}

/**
 * Hook to handle Sisense configuration status
 */
export function useSisenseStatus() {
  const [isConfigured, setIsConfigured] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkConfiguration = async () => {
      setIsChecking(true);
      try {
        const configured = sisenseService.isConfigured();
        setIsConfigured(configured);
      } catch (error) {
        setIsConfigured(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkConfiguration();
  }, []);

  return {
    isConfigured,
    isChecking,
    isReady: isConfigured && !isChecking,
  };
}