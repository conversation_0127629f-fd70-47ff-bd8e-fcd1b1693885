/**
 * Sisense service exports
 * 
 * This module provides a clean abstraction layer over the Sisense SDK,
 * offering better error handling, loading states, and development experience.
 */

// Main service
export { sisenseService, SisenseService } from './SisenseService';

// Types
export type {
  SisenseWidgetConfig,
  SisenseChartConfig,
  SisenseFilter,
  SisenseQueryResult,
  SisenseDataOptions,
  SisenseWidgetLoadState,
  SisenseWidgetError,
} from './types';

// Hooks
export {
  useSisenseWidget,
  useSisenseQuery,
  useSisenseStatus,
} from './hooks';

// Components
export { SafeSisenseWidget } from './components/SafeSisenseWidget';

// Re-export commonly used Sisense SDK items for convenience
export {
  Chart,
  Table,
  PivotTable,
  DashboardById,
  WidgetById,
  executeQuery,
  type ChartType,
  type WidgetStyleOptions,
} from '@sisense/sdk-ui';

export {
  createAttribute,
  createMeasure,
  createDimension,
  type Attribute,
  type Measure,
  type Dimension,
} from '@sisense/sdk-data';