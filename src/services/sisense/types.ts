/**
 * Sisense service types and interfaces
 */

import type { ChartType, WidgetStyleOptions } from '@sisense/sdk-ui';

export interface SisenseWidgetConfig {
  widgetId: string;
  dashboardId: string;
  title?: string;
  widgetType?: 'chart' | 'kpi' | 'table' | 'pivot';
  includeDashboardFilters?: boolean;
  styleOptions?: WidgetStyleOptions;
}

export interface SisenseChartConfig {
  dataOptions: {
    category?: any[];
    value?: any[];
    breakBy?: any[];
  };
  chartType: ChartType;
  title?: string;
  styleOptions?: WidgetStyleOptions;
}

export interface SisenseFilter {
  attribute: any;
  filter: any;
}

export interface SisenseQueryResult<T = any> {
  data: T;
  status: 'success' | 'error' | 'loading';
  error?: Error;
}

export interface SisenseDataOptions {
  dimensions?: any[];
  measures?: any[];
  filters?: SisenseFilter[];
  highlights?: any[];
}

export type SisenseWidgetLoadState = 'loading' | 'loaded' | 'error';

export interface SisenseWidgetError {
  message: string;
  code?: string;
  details?: any;
}