/**
 * Sisense Service - Abstraction layer for Sisense SDK
 * Provides a clean interface for interacting with Sisense, handling errors,
 * and managing widget lifecycle.
 */

import { executeQuery, type QueryResultData } from '@sisense/sdk-ui';
import { env } from '../../config/env.config';
import { logger } from '../logger';
import type { 
  SisenseWidgetConfig, 
  SisenseChartConfig, 
  SisenseQueryResult,
  SisenseDataOptions,
  SisenseWidgetError 
} from './types';

export class SisenseService {
  private static instance: SisenseService;
  private isInitialized = false;
  private errorCallbacks: ((error: SisenseWidgetError) => void)[] = [];

  private constructor() {
    this.initialize();
  }

  static getInstance(): SisenseService {
    if (!SisenseService.instance) {
      SisenseService.instance = new SisenseService();
    }
    return SisenseService.instance;
  }

  private initialize(): void {
    try {
      // Validate environment configuration
      const config = env.getConfig();
      if (!config.INSTANCE_URL || !config.TOKEN) {
        throw new Error('Sisense configuration is incomplete');
      }
      this.isInitialized = true;
    } catch (error) {
      this.handleError({
        message: 'Failed to initialize Sisense service',
        details: error,
      });
    }
  }

  /**
   * Register an error callback to be notified of Sisense errors
   */
  onError(callback: (error: SisenseWidgetError) => void): () => void {
    this.errorCallbacks.push(callback);
    // Return unsubscribe function
    return () => {
      this.errorCallbacks = this.errorCallbacks.filter(cb => cb !== callback);
    };
  }

  /**
   * Execute a query against Sisense
   */
  async executeQuery<T = any>(
    dataOptions: SisenseDataOptions,
    dataSource?: string
  ): Promise<SisenseQueryResult<T>> {
    if (!this.isInitialized) {
      return {
        data: null as any,
        status: 'error',
        error: new Error('Sisense service not initialized'),
      };
    }

    try {
      const result = await executeQuery({
        dataSource: dataSource || 'ARTBA Economics',
        ...dataOptions,
      });

      return {
        data: result as T,
        status: 'success',
      };
    } catch (error) {
      this.handleError({
        message: 'Query execution failed',
        details: error,
      });

      return {
        data: null as any,
        status: 'error',
        error: error instanceof Error ? error : new Error('Unknown error'),
      };
    }
  }

  /**
   * Get widget configuration with defaults
   */
  getWidgetConfig(config: SisenseWidgetConfig): Required<SisenseWidgetConfig> {
    return {
      widgetId: config.widgetId,
      dashboardId: config.dashboardId,
      title: config.title || '',
      widgetType: config.widgetType || 'chart',
      includeDashboardFilters: config.includeDashboardFilters ?? true,
      styleOptions: {
        height: 400,
        width: '100%',
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        // Remove widget container background and border
        widget: {
          backgroundColor: 'transparent',
          borderWidth: 0,
          borderColor: 'transparent',
          boxShadow: 'none',
        },
        // Remove header background
        header: {
          backgroundColor: 'transparent',
        },
        ...config.styleOptions,
      },
    };
  }

  /**
   * Get chart configuration with defaults
   */
  getChartConfig(config: SisenseChartConfig): Required<SisenseChartConfig> {
    return {
      dataOptions: config.dataOptions,
      chartType: config.chartType,
      title: config.title || '',
      styleOptions: {
        height: 400,
        width: '100%',
        ...config.styleOptions,
      },
    };
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return this.isInitialized && env.isDevelopment 
      ? true // Allow development without full config
      : Boolean(env.instanceUrl && env.token);
  }

  /**
   * Handle and propagate errors
   */
  private handleError(error: SisenseWidgetError): void {
    // Notify all registered error callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        // Prevent callback errors from breaking the error handling
        logger.error('Error in error callback', err as Error);
      }
    });

    // Log the error
    logger.error('[SisenseService] ' + error.message, { details: error.details });
  }

  /**
   * Create a safe widget wrapper that handles common errors
   */
  createSafeWidget<T extends React.ComponentType<any>>(
    WidgetComponent: T,
    fallbackComponent?: React.ComponentType<any>
  ): T {
    // This would be implemented as a HOC in a separate file
    // For now, return the component as-is
    return WidgetComponent;
  }
}

// Export singleton instance
export const sisenseService = SisenseService.getInstance();