/**
 * Safe wrapper component for Sisense widgets with error boundaries
 * and loading states
 */

import React, { ReactNode } from 'react';
import { WidgetById } from '@sisense/sdk-ui';
import { useSisenseWidget } from '../hooks';
import { sisenseService } from '../SisenseService';
import type { SisenseWidgetConfig } from '../types';
import { LoadingSpinner } from '../../../components/layout/LoadingSpinner';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';
import { theme } from '../../../config/theme.config';
import { createTransparentWidgetStyle } from '../../../config/sisense.config';

interface SafeSisenseWidgetProps extends SisenseWidgetConfig {
  onLoad?: () => void;
  onError?: (error: Error) => void;
  fallback?: ReactNode;
  showErrorDetails?: boolean;
}

export const SafeSisenseWidget: React.FC<SafeSisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  widgetType = 'chart',
  includeDashboardFilters = true,
  styleOptions,
  onLoad,
  onError,
  fallback,
  showErrorDetails = import.meta.env.DEV,
}) => {
  const { loadState, error, handleLoad, handleError } = useSisenseWidget({
    widgetId,
    dashboardId,
    title,
    widgetType,
    includeDashboardFilters,
    styleOptions,
  });

  const widgetConfig = sisenseService.getWidgetConfig({
    widgetId,
    dashboardId,
    title,
    widgetType,
    includeDashboardFilters,
    styleOptions,
  });

  // Create comprehensive styling using the official WidgetByIdStyleOptions
  const transparentStyle = createTransparentWidgetStyle(
    widgetConfig.styleOptions.height,
    widgetConfig.styleOptions.width
  );

  // Merge with existing widget config style options
  const finalStyleOptions = {
    ...transparentStyle,
    ...widgetConfig.styleOptions,
    // Ensure transparency is always maintained
    backgroundColor: 'transparent',
    widget: {
      ...transparentStyle.widget,
      backgroundColor: 'transparent',
    },
    header: {
      ...transparentStyle.header,
      backgroundColor: 'transparent',
    },
  };

  // Show loading state
  if (loadState === 'loading') {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: widgetConfig.styleOptions.height,
          width: widgetConfig.styleOptions.width,
          background: 'transparent',
        }}
      >
        <LoadingSpinner />
      </div>
    );
  }

  // Show error state
  if (loadState === 'error' && error) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: widgetConfig.styleOptions.height,
          width: widgetConfig.styleOptions.width,
          padding: theme.spacing.lg,
          background: 'transparent',
          borderRadius: theme.borderRadius.lg,
          border: `1px solid ${theme.colors.border.light}`,
        }}
      >
        <div style={{ fontSize: '32px', marginBottom: theme.spacing.md }}>⚠️</div>
        <div style={{ 
          fontSize: theme.typography.fontSize.md,
          color: theme.colors.text.primary,
          fontWeight: theme.typography.fontWeight.medium,
        }}>
          Unable to load widget
        </div>
        {showErrorDetails && (
          <div style={{
            fontSize: theme.typography.fontSize.sm,
            color: theme.colors.text.secondary,
            marginTop: theme.spacing.sm,
          }}>
            {error.message}
          </div>
        )}
      </div>
    );
  }

  return (
    <ErrorBoundary
      context="sisense-widget"
      onError={(error, errorInfo) => {
        handleError({ message: error.message, details: errorInfo });
        onError?.(error);
      }}
      fallback={fallback}
      showDetails={showErrorDetails}
    >
      <div
        style={{
          width: '100%',
          height: '100%',
          background: 'transparent',
        }}
      >
        <WidgetById
          widgetOid={widgetConfig.widgetId}
          dashboardOid={widgetConfig.dashboardId}
          includeDashboardFilters={widgetConfig.includeDashboardFilters}
          styleOptions={finalStyleOptions}
          onLoaded={() => {
            handleLoad();
            onLoad?.();
          }}
        />
      </div>
    </ErrorBoundary>
  );
};