/**
 * Centralized logging service for the application
 * Replaces console.log/error with structured logging
 */

import { env } from '../../config/env.config';
import type { Logger, LogLevel, LogContext, LogEntry, LoggerConfig } from './types';

export class LoggerService implements Logger {
  private static instance: LoggerService;
  private config: LoggerConfig;
  private logQueue: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;

  private readonly LOG_LEVELS: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4,
  };

  private constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: env.isDevelopment ? 'debug' : 'info',
      enableConsole: env.isDevelopment,
      enableRemote: env.isProduction,
      maxQueueSize: 100,
      flushInterval: 5000, // 5 seconds
      ...config,
    };

    this.startFlushTimer();
  }

  static getInstance(config?: Partial<LoggerConfig>): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService(config);
    }
    return LoggerService.instance;
  }

  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  error(message: string, errorOrContext?: Error | LogContext, context?: LogContext): void {
    if (errorOrContext instanceof Error) {
      this.log('error', message, context, errorOrContext);
    } else {
      this.log('error', message, errorOrContext);
    }
  }

  fatal(message: string, errorOrContext?: Error | LogContext, context?: LogContext): void {
    if (errorOrContext instanceof Error) {
      this.log('fatal', message, context, errorOrContext);
    } else {
      this.log('fatal', message, errorOrContext);
    }
    // Fatal errors should flush immediately
    this.flush();
  }

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  async flush(): Promise<void> {
    if (this.logQueue.length === 0) return;

    const logsToSend = [...this.logQueue];
    this.logQueue = [];

    if (this.config.enableRemote && this.config.remoteEndpoint) {
      try {
        await this.sendLogsToRemote(logsToSend);
      } catch (error) {
        // If remote logging fails, at least log to console
        if (this.config.enableConsole) {
          console.error('Failed to send logs to remote:', error);
          logsToSend.forEach(log => this.logToConsole(log));
        }
      }
    }
  }

  private log(level: LogLevel, message: string, context?: LogContext, error?: Error): void {
    // Check if this log level should be recorded
    if (this.LOG_LEVELS[level] < this.LOG_LEVELS[this.config.level]) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
      stack: error?.stack,
    };

    // Add environment context
    logEntry.context = {
      ...logEntry.context,
      env: env.isDevelopment ? 'development' : 'production',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    };

    // Log to console in development or if explicitly enabled
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // Add to queue for remote logging
    if (this.config.enableRemote) {
      this.logQueue.push(logEntry);

      // Flush if queue is full
      if (this.logQueue.length >= (this.config.maxQueueSize || 100)) {
        this.flush();
      }
    }
  }

  private logToConsole(entry: LogEntry): void {
    const { level, message, context, error } = entry;
    const timestamp = entry.timestamp.toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
      case 'debug':
        console.debug(prefix, message, context);
        break;
      case 'info':
        console.info(prefix, message, context);
        break;
      case 'warn':
        console.warn(prefix, message, context);
        break;
      case 'error':
      case 'fatal':
        if (error) {
          console.error(prefix, message, error, context);
        } else {
          console.error(prefix, message, context);
        }
        break;
    }
  }

  private async sendLogsToRemote(logs: LogEntry[]): Promise<void> {
    if (!this.config.remoteEndpoint) return;

    // This is a placeholder for actual remote logging implementation
    // You would integrate with services like Sentry, LogRocket, DataDog, etc.
    const response = await fetch(this.config.remoteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        logs,
        sessionId: this.getSessionId(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send logs: ${response.statusText}`);
    }
  }

  private getSessionId(): string {
    // Simple session ID implementation
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('logger_session_id');
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        sessionStorage.setItem('logger_session_id', sessionId);
      }
      return sessionId;
    }
    return 'no_session';
  }

  private startFlushTimer(): void {
    if (this.config.enableRemote && this.config.flushInterval) {
      this.flushTimer = setInterval(() => {
        this.flush();
      }, this.config.flushInterval);
    }
  }

  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// Create and export singleton instance
export const logger = LoggerService.getInstance();

// Global error handlers
if (typeof window !== 'undefined') {
  // Handle unhandled errors
  window.addEventListener('error', (event) => {
    logger.error('Unhandled error', event.error || new Error(event.message), {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    logger.error('Unhandled promise rejection', 
      event.reason instanceof Error ? event.reason : new Error(String(event.reason))
    );
  });

  // Flush logs before page unload
  window.addEventListener('beforeunload', () => {
    logger.flush();
  });
}