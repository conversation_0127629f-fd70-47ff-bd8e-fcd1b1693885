import type { Dash<PERSON><PERSON>, DashWidget, DashWidgetType } from '../types/dash.types';
import type { DashboardConfig, WidgetConfig } from '../types/dashboard.types';
import type { ChartType } from '@sisense/sdk-ui';

export class DashParserService {
  /**
   * Parse a .dash file content
   */
  static parseDashFile(content: string): DashFile {
    try {
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Failed to parse .dash file: ${error}`);
    }
  }

  /**
   * Convert a parsed .dash file to our dashboard config format
   */
  static convertToDashboardConfig(dash: DashFile): DashboardConfig {
    return {
      id: dash.source || `dash-${Date.now()}`,
      title: dash.title,
      description: dash.desc || '',
      path: `/dashboard/${this.slugify(dash.title)}`,
      widgets: this.convertWidgets(dash),
    };
  }

  /**
   * Convert dash widgets to our widget config format
   */
  private static convertWidgets(dash: DashFile): WidgetConfig[] {
    const widgets: WidgetConfig[] = [];
    
    // Map layout elements to widgets
    const widgetMap = new Map(dash.widgets.map(w => [w.oid, w]));
    
    let gridY = 0;
    
    dash.layout.columns.forEach((column, colIndex) => {
      column.cells.forEach((cell) => {
        cell.subcells.forEach((subcell, subcellIndex) => {
          subcell.elements.forEach((element) => {
            const dashWidget = widgetMap.get(element.widgetid);
            if (dashWidget) {
              const widget = this.convertWidget(
                dashWidget,
                element,
                colIndex,
                gridY,
                column.width,
                subcell.width
              );
              if (widget) {
                widgets.push(widget);
              }
            }
          });
        });
        gridY += 2; // Move down for next row
      });
    });
    
    return widgets;
  }

  /**
   * Convert a single dash widget to our widget config
   */
  private static convertWidget(
    dashWidget: DashWidget,
    element: any,
    colIndex: number,
    gridY: number,
    columnWidth: number,
    subcellWidth: number
  ): WidgetConfig | null {
    const widgetType = this.mapWidgetType(dashWidget.type, dashWidget.subtype);
    if (!widgetType) return null;

    // Calculate grid position
    const gridWidth = Math.round((columnWidth * subcellWidth) / 100 * 12 / 100);
    const gridX = colIndex * 12 * (columnWidth / 100);
    const gridHeight = this.calculateGridHeight(element.height);

    const baseConfig: WidgetConfig = {
      id: dashWidget.oid,
      type: widgetType,
      title: dashWidget.title || '',
      position: {
        x: Math.round(gridX),
        y: gridY,
        w: Math.max(1, gridWidth),
        h: gridHeight,
      },
      config: {},
    };

    // Convert widget-specific config
    switch (widgetType) {
      case 'kpi':
        return this.convertKPIWidget(dashWidget, baseConfig);
      case 'chart':
        return this.convertChartWidget(dashWidget, baseConfig);
      case 'table':
        return this.convertTableWidget(dashWidget, baseConfig);
      default:
        return baseConfig;
    }
  }

  /**
   * Convert KPI/Indicator widget
   */
  private static convertKPIWidget(dashWidget: DashWidget, baseConfig: WidgetConfig): WidgetConfig {
    const valuePanel = dashWidget.metadata.panels.find(p => p.name === 'value');
    const secondaryPanel = dashWidget.metadata.panels.find(p => p.name === 'secondary');
    
    return {
      ...baseConfig,
      config: {
        value: 0, // Will be populated from data
        format: this.detectNumberFormat(valuePanel?.items[0]?.format),
        // Add secondary value if exists
        ...(secondaryPanel?.items[0] && {
          previousValue: 0,
          trend: 'neutral',
        }),
      },
    };
  }

  /**
   * Convert chart widget
   */
  private static convertChartWidget(dashWidget: DashWidget, baseConfig: WidgetConfig): WidgetConfig {
    const chartType = this.mapChartType(dashWidget.type, dashWidget.subtype);
    
    return {
      ...baseConfig,
      config: {
        chartType,
        dataOptions: this.convertDataOptions(dashWidget.metadata.panels),
        // TODO: Add data source configuration
      },
    };
  }

  /**
   * Convert table/pivot widget
   */
  private static convertTableWidget(dashWidget: DashWidget, baseConfig: WidgetConfig): WidgetConfig {
    return {
      ...baseConfig,
      type: 'table',
      config: {
        dataOptions: this.convertTableDataOptions(dashWidget.metadata.panels),
      },
    };
  }

  /**
   * Map dash widget type to our widget type
   */
  private static mapWidgetType(type: string, subtype?: string): 'chart' | 'kpi' | 'table' | null {
    if (type === 'indicator' || subtype?.includes('indicator')) {
      return 'kpi';
    }
    if (type.startsWith('chart/') || type === 'chart') {
      return 'chart';
    }
    if (type === 'pivot2' || type === 'table') {
      return 'table';
    }
    // TODO: Handle BloX and other custom widgets
    return null;
  }

  /**
   * Map dash chart type to Sisense chart type
   */
  private static mapChartType(type: string, subtype?: string): ChartType {
    const typeMap: Record<string, ChartType> = {
      'chart/column': 'column',
      'chart/bar': 'bar',
      'chart/line': 'line',
      'chart/area': 'area',
      'chart/pie': 'pie',
      'chart/scatter': 'scatter',
      'chart/polar': 'polar',
      'treemap': 'treemap',
      'sunburst': 'sunburst',
    };
    
    return typeMap[type] || 'column';
  }

  /**
   * Convert JAQL panels to data options
   */
  private static convertDataOptions(panels: any[]): any {
    // TODO: Implement JAQL to DataOptions conversion
    // This is a complex mapping that needs to handle:
    // - Categories (x-axis)
    // - Values (y-axis/measures)
    // - Break by (series)
    // - Filters
    return {
      category: [],
      value: [],
      breakBy: [],
    };
  }

  /**
   * Convert table data options
   */
  private static convertTableDataOptions(panels: any[]): any {
    // TODO: Implement table data options conversion
    return {
      columns: [],
    };
  }

  /**
   * Detect number format from dash format config
   */
  private static detectNumberFormat(format?: any): 'number' | 'currency' | 'percentage' {
    if (format?.mask?.percent) return 'percentage';
    if (format?.mask?.currency) return 'currency';
    return 'number';
  }

  /**
   * Calculate grid height from element height
   */
  private static calculateGridHeight(height: number | string): number {
    if (typeof height === 'string') {
      const match = height.match(/(\d+)px/);
      if (match) {
        const px = parseInt(match[1]);
        return Math.max(2, Math.round(px / 100));
      }
    }
    return typeof height === 'number' ? Math.max(2, Math.round(height / 100)) : 4;
  }

  /**
   * Create URL-friendly slug from title
   */
  private static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}