import React, { createContext, useContext, useState, ReactNode } from 'react';

interface FilterState {
  [dashboardId: string]: {
    [filterId: string]: any;
  };
}

interface FilterContextType {
  filters: FilterState;
  setDashboardFilter: (dashboardId: string, filterId: string, value: any) => void;
  clearDashboardFilters: (dashboardId: string) => void;
  getDashboardFilters: (dashboardId: string) => any[];
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

export const useFilters = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};

interface FilterProviderProps {
  children: ReactNode;
}

export const FilterProvider: React.FC<FilterProviderProps> = ({ children }) => {
  const [filters, setFilters] = useState<FilterState>({});

  const setDashboardFilter = (dashboardId: string, filterId: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [dashboardId]: {
        ...prev[dashboardId],
        [filterId]: value,
      },
    }));
  };

  const clearDashboardFilters = (dashboardId: string) => {
    setFilters(prev => ({
      ...prev,
      [dashboardId]: {},
    }));
  };

  const getDashboardFilters = (dashboardId: string): any[] => {
    const dashboardFilters = filters[dashboardId] || {};
    return Object.entries(dashboardFilters)
      .filter(([_, value]) => value !== null && value !== undefined)
      .map(([filterId, value]) => ({ id: filterId, value }));
  };

  return (
    <FilterContext.Provider
      value={{
        filters,
        setDashboardFilter,
        clearDashboardFilters,
        getDashboardFilters,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
};
