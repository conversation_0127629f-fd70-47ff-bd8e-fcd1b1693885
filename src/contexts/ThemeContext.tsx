import React, { createContext, useState, useEffect, useCallback } from 'react';
import type { ReactNode } from 'react';
import type { Theme, ThemeContextValue } from '../types';
import { createBaseTheme, mergeThemes } from '../config/themes/baseTheme';
import { getThemePreset } from '../config/themes/presets';

export const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

const THEME_STORAGE_KEY = 'econ_theme';
const DARK_MODE_KEY = 'econ_dark_mode';

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(createBaseTheme());
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isCustomTheme, setIsCustomTheme] = useState(false);

  // Load theme from storage on mount
  useEffect(() => {
    loadThemeFromStorage();
  }, []);

  // Apply theme to CSS variables
  useEffect(() => {
    applyThemeToCSSVariables(theme);
  }, [theme]);

  // Listen for dark mode changes
  useEffect(() => {
    const darkModeStored = localStorage.getItem(DARK_MODE_KEY);
    if (darkModeStored) {
      setIsDarkMode(darkModeStored === 'true');
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(prefersDark);
    }
  }, []);

  const loadThemeFromStorage = () => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY);
      if (stored) {
        const parsedTheme = JSON.parse(stored) as Theme;
        setThemeState(parsedTheme);
        setIsCustomTheme(true);
      }
    } catch (error) {
      console.error('Failed to load theme from storage:', error);
    }
  };

  const saveThemeToStorage = (theme: Theme) => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(theme));
    } catch (error) {
      console.error('Failed to save theme to storage:', error);
    }
  };

  const applyThemeToCSSVariables = (theme: Theme) => {
    const root = document.documentElement;
    
    // Colors
    root.style.setProperty('--color-primary', theme.colors.primary);
    root.style.setProperty('--color-primary-light', theme.colors.primaryLight);
    root.style.setProperty('--color-primary-dark', theme.colors.primaryDark);
    root.style.setProperty('--color-secondary', theme.colors.secondary);
    root.style.setProperty('--color-secondary-light', theme.colors.secondaryLight);
    root.style.setProperty('--color-secondary-dark', theme.colors.secondaryDark);
    root.style.setProperty('--color-accent', theme.colors.accent);
    root.style.setProperty('--color-accent-light', theme.colors.accentLight);
    root.style.setProperty('--color-bg-primary', theme.colors.background.primary);
    root.style.setProperty('--color-bg-secondary', theme.colors.background.secondary);
    root.style.setProperty('--color-bg-tertiary', theme.colors.background.tertiary);
    root.style.setProperty('--color-surface', theme.colors.surface);
    root.style.setProperty('--color-text-primary', theme.colors.text.primary);
    root.style.setProperty('--color-text-secondary', theme.colors.text.secondary);
    root.style.setProperty('--color-text-tertiary', theme.colors.text.tertiary);
    root.style.setProperty('--color-border', theme.colors.border.DEFAULT);
    
    // Spacing
    root.style.setProperty('--spacing-xs', `${theme.spacing.xs}px`);
    root.style.setProperty('--spacing-sm', `${theme.spacing.sm}px`);
    root.style.setProperty('--spacing-md', `${theme.spacing.md}px`);
    root.style.setProperty('--spacing-lg', `${theme.spacing.lg}px`);
    root.style.setProperty('--spacing-xl', `${theme.spacing.xl}px`);
    root.style.setProperty('--spacing-xxl', `${theme.spacing.xxl}px`);
    
    // Border radius
    root.style.setProperty('--radius-sm', theme.borderRadius.sm);
    root.style.setProperty('--radius-md', theme.borderRadius.md);
    root.style.setProperty('--radius-lg', theme.borderRadius.lg);
    root.style.setProperty('--radius-xl', theme.borderRadius.xl);
    
    // Shadows
    root.style.setProperty('--shadow-sm', theme.shadows.sm);
    root.style.setProperty('--shadow-md', theme.shadows.md);
    root.style.setProperty('--shadow-lg', theme.shadows.lg);
    
    // Typography
    root.style.setProperty('--font-sans', theme.typography.fontFamily.sans);
    root.style.setProperty('--font-mono', theme.typography.fontFamily.mono);
  };

  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    saveThemeToStorage(newTheme);
    setIsCustomTheme(true);
  }, []);

  const toggleDarkMode = useCallback(() => {
    setIsDarkMode(prev => {
      const newValue = !prev;
      localStorage.setItem(DARK_MODE_KEY, String(newValue));
      
      // Apply dark theme if enabled
      if (newValue) {
        const darkTheme = getThemePreset('dark');
        setTheme(darkTheme);
      } else {
        const defaultTheme = getThemePreset('default');
        setTheme(defaultTheme);
      }
      
      return newValue;
    });
  }, [setTheme]);

  const updateTheme = useCallback((updates: Partial<Theme>) => {
    const updatedTheme = mergeThemes(theme, updates);
    setTheme(updatedTheme);
  }, [theme, setTheme]);

  const resetTheme = useCallback(() => {
    const defaultTheme = createBaseTheme();
    setThemeState(defaultTheme);
    setIsCustomTheme(false);
    localStorage.removeItem(THEME_STORAGE_KEY);
  }, []);

  const exportTheme = useCallback((): string => {
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      theme: theme,
      metadata: {
        isDarkMode,
        isCustomTheme
      }
    };
    return JSON.stringify(exportData, null, 2);
  }, [theme, isDarkMode, isCustomTheme]);

  const importTheme = useCallback((themeData: string) => {
    try {
      const parsed = JSON.parse(themeData);
      if (!parsed.theme || !parsed.version) {
        throw new Error('Invalid theme format');
      }
      setTheme(parsed.theme);
    } catch (error) {
      throw new Error(`Failed to import theme: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [setTheme]);

  const value: ThemeContextValue = {
    theme,
    isDarkMode,
    isCustomTheme,
    setTheme,
    toggleDarkMode,
    updateTheme,
    resetTheme,
    exportTheme,
    importTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};