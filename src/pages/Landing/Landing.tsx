import React from "react";
import { useNavigate } from "react-router-dom";
import { PageLayout } from "../../components/layout/PageLayout";
import { DashboardCard } from "./DashboardCard";
import { StatCard } from "../../components/dashboard/StatCard";
import { theme } from "../../config/theme.config";

// These dashboards are extracted from the .dash files
const dashboards = [
  {
    id: "summary",
    title: "1. Summary Dashboard",
    description:
      "Executive summary of key transportation metrics including contract awards, federal obligations, and material prices.",
    path: "/dashboard/summary",
    icon: "📊",
    color: theme.colors.primary,
  },
  {
    id: "contract-awards",
    title: "2. Contract Awards",
    description:
      "Track state and local contract awards with TTM and YTD metrics, geographic distribution, and trending analysis.",
    path: "/dashboard/contract-awards",
    icon: "📄",
    color: "#00cee6",
  },
  {
    id: "value-put-in-place",
    title: "3. Value Put in Place",
    description:
      "Monitor construction value put in place across highways, bridges, and other infrastructure projects.",
    path: "/dashboard/value-put-in-place",
    icon: "🏗️",
    color: "#9b9bd7",
  },
  {
    id: "federal-aid",
    title: "4. Federal-Aid Obligations",
    description:
      "Track federal highway aid obligations by state and program, with historical trends and distributions.",
    path: "/dashboard/federal-aid",
    icon: "🏛️",
    color: "#6EDA55",
  },
  {
    id: "state-legislative",
    title: "6. State Legislative Initiatives",
    description:
      "Monitor state transportation funding initiatives, ballot measures, and legislative actions.",
    path: "/dashboard/state-legislative-initiatives",
    icon: "📜",
    color: "#fc7570",
  },
  {
    id: "state-dot-budgets",
    title: "7. State DOT Budgets 2025",
    description:
      "Analyze state department of transportation budgets, funding sources, and year-over-year changes.",
    path: "/dashboard/state-dot-budgets",
    icon: "💰",
    color: "#fbb755",
  },
  {
    id: "material-prices",
    title: "9. Material Prices",
    description:
      "Track construction material price indices for asphalt, concrete, steel, and aggregates with trend analysis.",
    path: "/dashboard/material-prices",
    icon: "📈",
    color: "#218A8C",
  },
];

export const Landing: React.FC = () => {
  const navigate = useNavigate();

  return (
    <PageLayout title="Analytics Dashboard" data-oid="kh16l7m">
      <div className="animate-fade-in" data-oid="h848w1s">
        {/* Stat Cards */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
            gap: theme.spacing.lg,
            marginBottom: theme.spacing.xl,
          }}
          data-oid="oaq:h.q"
        >
          <StatCard
            title="New Cases"
            value="104"
            change={14.88}
            trend={[45, 52, 48, 65, 72, 68, 85, 92, 88, 104]}
            icon="📊"
            color={theme.colors.success}
            subtitle="Trends last month"
            data-oid="8y5qiuv"
          />

          <StatCard
            title="New Tasks"
            value="34"
            change={-5.67}
            trend={[42, 38, 45, 40, 36, 41, 37, 35, 38, 34]}
            icon="📋"
            color={theme.colors.error}
            subtitle="Trends last month"
            data-oid="qvu:1ma"
          />

          <StatCard
            title="Active Projects"
            value="7"
            change={16.7}
            trend={[5, 5, 6, 5, 6, 6, 7, 6, 7, 7]}
            icon="🚀"
            color={theme.colors.primary}
            subtitle="Currently active"
            data-oid="_0bxmcl"
          />

          <StatCard
            title="Completion Rate"
            value="92%"
            change={3.23}
            trend={[85, 87, 86, 88, 89, 88, 90, 91, 90, 92]}
            icon="✅"
            color={theme.colors.secondary}
            subtitle="Last 30 days"
            data-oid="n0.yapf"
          />
        </div>

        {/* Hero Section */}
        <div
          style={{
            textAlign: "center",
            padding: `${theme.spacing.xxl}px 0`,
            background: theme.colors.gradients.surface,
            borderRadius: theme.borderRadius.xxl,
            marginBottom: theme.spacing.xl,
            position: "relative",
            overflow: "hidden",
          }}
          data-oid="2b1k5gq"
        >
          {/* Background decoration */}
          <div
            style={{
              position: "absolute",
              top: "-50%",
              left: "-50%",
              width: "200%",
              height: "200%",
              background:
                "radial-gradient(circle, rgba(37, 99, 235, 0.05) 0%, transparent 70%)",
              pointerEvents: "none",
            }}
            data-oid="hwuob85"
          />

          <div style={{ position: "relative", zIndex: 1 }} data-oid="08m02po">
            <h1
              className="gradient-text"
              style={{
                fontSize: "48px",
                fontWeight: theme.typography.fontWeight.bold,
                margin: 0,
                marginBottom: theme.spacing.lg,
                lineHeight: theme.typography.lineHeight.tight,
              }}
              data-oid="ohfq1zf"
            >
              ARTBA Economics Dashboard
            </h1>
            <p
              style={{
                fontSize: theme.typography.fontSize.xl,
                color: theme.colors.text.secondary,
                maxWidth: "700px",
                margin: "0 auto",
                lineHeight: theme.typography.lineHeight.relaxed,
                fontWeight: theme.typography.fontWeight.medium,
              }}
              data-oid="6t2c85f"
            >
              Transportation construction industry analytics and economic
              indicators
            </p>

            {/* Stats or badges */}
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                gap: theme.spacing.xl,
                marginTop: theme.spacing.xl,
                flexWrap: "wrap",
              }}
              data-oid=":jp2rmb"
            >
              {[
                { label: "Dashboards", value: "7" },
                { label: "Data Sources", value: "15+" },
                { label: "Real-time", value: "24/7" },
              ].map((stat, index) => (
                <div
                  key={index}
                  style={{
                    textAlign: "center",
                    padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                    background: "rgba(255, 255, 255, 0.8)",
                    borderRadius: theme.borderRadius.lg,
                    backdropFilter: "blur(10px)",
                    border: `1px solid ${theme.colors.border.light}`,
                  }}
                  data-oid=":dlocfz"
                >
                  <div
                    style={{
                      fontSize: theme.typography.fontSize.xxl,
                      fontWeight: theme.typography.fontWeight.bold,
                      color: theme.colors.primary,
                      marginBottom: theme.spacing.xs,
                    }}
                    data-oid="srr-3c."
                  >
                    {stat.value}
                  </div>
                  <div
                    style={{
                      fontSize: theme.typography.fontSize.sm,
                      color: theme.colors.text.secondary,
                      fontWeight: theme.typography.fontWeight.medium,
                    }}
                    data-oid="0ytbo2s"
                  >
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Dashboard Grid */}
        <div style={{ marginBottom: theme.spacing.section }} data-oid="unrf6qb">
          <div
            style={{
              textAlign: "center",
              marginBottom: theme.spacing.xl,
            }}
            data-oid="kd-z-bo"
          >
            <h2
              style={{
                fontSize: "32px",
                fontWeight: theme.typography.fontWeight.semibold,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.md,
              }}
              data-oid="k4ot6e9"
            >
              Explore Dashboards
            </h2>
            <p
              style={{
                fontSize: theme.typography.fontSize.lg,
                color: theme.colors.text.secondary,
                maxWidth: "600px",
                margin: "0 auto",
              }}
              data-oid="epn_d:b"
            >
              Dive into comprehensive analytics across different aspects of
              transportation construction
            </p>
          </div>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
              gap: theme.spacing.xl,
              marginBottom: theme.spacing.xl,
            }}
            data-oid=".purzz3"
          >
            {dashboards.map((dashboard, index) => (
              <div
                key={dashboard.id}
                className="animate-slide-up"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
                data-oid="f39bdt6"
              >
                <DashboardCard
                  title={dashboard.title}
                  description={dashboard.description}
                  path={dashboard.path}
                  icon={dashboard.icon}
                  color={dashboard.color}
                  data-oid="8nkv4:f"
                />
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div
          style={{
            background: theme.colors.gradients.primary,
            borderRadius: theme.borderRadius.xxl,
            padding: theme.spacing.xxl,
            textAlign: "center",
            marginTop: theme.spacing.section,
            position: "relative",
            overflow: "hidden",
          }}
          data-oid="qplxyp1"
        >
          {/* Background decoration */}
          <div
            style={{
              position: "absolute",
              top: "-20%",
              right: "-20%",
              width: "40%",
              height: "140%",
              background: "rgba(255, 255, 255, 0.1)",
              borderRadius: "50%",
              pointerEvents: "none",
            }}
            data-oid="j7c20eh"
          />

          <div style={{ position: "relative", zIndex: 1 }} data-oid="s.85cri">
            <h2
              style={{
                fontSize: "32px",
                fontWeight: theme.typography.fontWeight.bold,
                color: theme.colors.text.inverse,
                marginBottom: theme.spacing.lg,
              }}
              data-oid="7ugfg-k"
            >
              Ready to Get Started?
            </h2>
            <p
              style={{
                color: "rgba(255, 255, 255, 0.9)",
                marginBottom: theme.spacing.xl,
                fontSize: theme.typography.fontSize.lg,
                maxWidth: "500px",
                margin: `0 auto ${theme.spacing.xl}px auto`,
              }}
              data-oid="tsqcm6r"
            >
              Import your own dashboards or explore our comprehensive
              documentation
            </p>
            <div
              style={{
                display: "flex",
                gap: theme.spacing.lg,
                justifyContent: "center",
                flexWrap: "wrap",
              }}
              data-oid="qven5ad"
            >
              <button
                onClick={() => navigate("/import")}
                style={{
                  background: theme.colors.surface,
                  color: theme.colors.primary,
                  border: "none",
                  borderRadius: theme.borderRadius.lg,
                  padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                  fontSize: theme.typography.fontSize.md,
                  fontWeight: theme.typography.fontWeight.semibold,
                  cursor: "pointer",
                  transition: theme.transitions.normal,
                  boxShadow: theme.shadows.md,
                }}
                data-oid="u-a15f5"
              >
                Import Dashboard
              </button>
              <button
                style={{
                  background: "rgba(255, 255, 255, 0.2)",
                  color: theme.colors.text.inverse,
                  border: `2px solid rgba(255, 255, 255, 0.3)`,
                  borderRadius: theme.borderRadius.lg,
                  padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                  fontSize: theme.typography.fontSize.md,
                  fontWeight: theme.typography.fontWeight.semibold,
                  cursor: "pointer",
                  transition: theme.transitions.normal,
                  backdropFilter: "blur(10px)",
                }}
                data-oid="_zqtbqg"
              >
                View Documentation
              </button>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};
