import React, { useState } from 'react';
import { PageLayout } from '../components/layout/PageLayout';
import { theme } from '../config/theme.config';
import { DataModelInspector } from '../utils/dataModelInspector';

export const DataModelInspectorPage: React.FC = () => {
  const [dashFileContent, setDashFileContent] = useState<string>('');
  const [inspectionResult, setInspectionResult] = useState<any>(null);
  const [generatedCode, setGeneratedCode] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleInspect = () => {
    try {
      setError('');
      const dashData = JSON.parse(dashFileContent);
      const result = DataModelInspector.analyzeDashFile(dashData);
      setInspectionResult(result);

      const dataModelCode = DataModelInspector.generateDataModelCode(result);
      const filterConfig = DataModelInspector.generateFilterConfig(result);

      setGeneratedCode(`${dataModelCode}\n\n${filterConfig}`);
    } catch (err) {
      setError('Invalid JSON format. Please check your .dash file content.');
      console.error('Error parsing dash file:', err);
    }
  };

  const handleLoadSampleData = () => {
    // Load sample data model information based on what we know from the codebase
    const sampleResult = {
      dataSource: 'ARTBA Economics',
      tables: {
        'executive_summary.csv': {
          columns: {
            'mode': {
              name: 'Mode',
              type: 'text',
              expression: '[executive_summary.csv.mode]',
              table: 'executive_summary.csv',
              column: 'mode',
            },
            'MonthStart': {
              name: 'MonthStart',
              type: 'datetime',
              expression: '[executive_summary.csv.MonthStart (Calendar)]',
              table: 'executive_summary.csv',
              column: 'MonthStart',
            },
            'month_current_awards': {
              name: 'Month Current Awards',
              type: 'numeric',
              expression: '[executive_summary.csv.month_current_awards]',
              table: 'executive_summary.csv',
              column: 'month_current_awards',
            },
          },
        },
        'vpip.csv': {
          columns: {
            'year': {
              name: 'Year',
              type: 'datetime',
              expression: '[vpip.csv.year]',
              table: 'vpip.csv',
              column: 'year',
            },
            'major_mode': {
              name: 'Major Mode',
              type: 'text',
              expression: '[vpip.csv.major_mode]',
              table: 'vpip.csv',
              column: 'major_mode',
            },
          },
        },
      },
    };

    setInspectionResult(sampleResult);
    const dataModelCode = DataModelInspector.generateDataModelCode(sampleResult);
    const filterConfig = DataModelInspector.generateFilterConfig(sampleResult);
    setGeneratedCode(`${dataModelCode}\n\n${filterConfig}`);
  };

  return (
    <PageLayout title="Data Model Inspector">
      <div
        style={{
          padding: theme.spacing.xl,
          maxWidth: '1200px',
          margin: '0 auto',
        }}
      >
        <div
          style={{
            background: theme.colors.surface,
            backdropFilter: `blur(${theme.blur.md})`,
            WebkitBackdropFilter: `blur(${theme.blur.md})`,
            border: `1px solid ${theme.colors.border.glass}`,
            borderRadius: theme.borderRadius.lg,
            padding: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
          }}
        >
          <h2
            style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.bold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.lg,
            }}
          >
            Sisense Data Model Inspector
          </h2>
          
          <p
            style={{
              fontSize: theme.typography.fontSize.md,
              color: theme.colors.text.secondary,
              marginBottom: theme.spacing.lg,
              lineHeight: theme.typography.lineHeight.relaxed,
            }}
          >
            This tool analyzes your .dash files to identify the correct data model attributes
            for implementing dashboard filters. You can either load sample data or paste your
            .dash file content below.
          </p>

          <div
            style={{
              display: 'flex',
              gap: theme.spacing.md,
              marginBottom: theme.spacing.lg,
              flexWrap: 'wrap',
            }}
          >
            <button
              onClick={handleLoadSampleData}
              style={{
                padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                background: theme.colors.secondary,
                color: 'white',
                border: 'none',
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.medium,
                cursor: 'pointer',
                transition: theme.transitions.fast,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1';
              }}
            >
              Load Sample Data
            </button>

            <button
              onClick={handleInspect}
              disabled={!dashFileContent.trim()}
              style={{
                padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                background: dashFileContent.trim() ? theme.colors.primary : theme.colors.border.light,
                color: 'white',
                border: 'none',
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.medium,
                cursor: dashFileContent.trim() ? 'pointer' : 'not-allowed',
                transition: theme.transitions.fast,
              }}
              onMouseEnter={(e) => {
                if (dashFileContent.trim()) {
                  e.currentTarget.style.opacity = '0.9';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1';
              }}
            >
              Analyze .dash File
            </button>
          </div>

          <div
            style={{
              marginBottom: theme.spacing.lg,
            }}
          >
            <label
              style={{
                display: 'block',
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.medium,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.sm,
              }}
            >
              Paste .dash file content (JSON):
            </label>

            <textarea
              value={dashFileContent}
              onChange={(e) => setDashFileContent(e.target.value)}
              placeholder="Paste the content of your .dash file here..."
              style={{
                width: '100%',
                height: '200px',
                padding: theme.spacing.md,
                background: theme.colors.surfaceElevated,
                border: `1px solid ${theme.colors.border.glass}`,
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.primary,
                outline: 'none',
                fontFamily: 'monospace',
                resize: 'vertical',
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = theme.colors.primary;
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = theme.colors.border.glass;
              }}
            />
          </div>

          {error && (
            <div
              style={{
                background: '#fee',
                border: '1px solid #fcc',
                borderRadius: theme.borderRadius.md,
                padding: theme.spacing.md,
                marginBottom: theme.spacing.lg,
                color: '#c33',
                fontSize: theme.typography.fontSize.sm,
              }}
            >
              {error}
            </div>
          )}
        </div>

        {inspectionResult && (
          <div
            style={{
              background: theme.colors.surface,
              backdropFilter: `blur(${theme.blur.md})`,
              WebkitBackdropFilter: `blur(${theme.blur.md})`,
              border: `1px solid ${theme.colors.border.glass}`,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.xl,
              marginBottom: theme.spacing.xl,
            }}
          >
            <h3
              style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.semibold,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.md,
              }}
            >
              Inspection Results
            </h3>

            <div
              style={{
                marginBottom: theme.spacing.lg,
              }}
            >
              <p
                style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: theme.colors.text.secondary,
                  marginBottom: theme.spacing.sm,
                }}
              >
                <strong>Data Source:</strong> {inspectionResult.dataSource}
              </p>
              
              <p
                style={{
                  fontSize: theme.typography.fontSize.sm,
                  color: theme.colors.text.secondary,
                }}
              >
                <strong>Tables Found:</strong> {Object.keys(inspectionResult.tables).join(', ')}
              </p>
            </div>

            <h4
              style={{
                fontSize: theme.typography.fontSize.md,
                fontWeight: theme.typography.fontWeight.medium,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.md,
              }}
            >
              Available Attributes:
            </h4>

            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: theme.spacing.md,
                marginBottom: theme.spacing.lg,
              }}
            >
              {Object.entries(inspectionResult.tables).map(([tableName, table]: [string, any]) => (
                <div
                  key={tableName}
                  style={{
                    background: theme.colors.surfaceElevated,
                    border: `1px solid ${theme.colors.border.light}`,
                    borderRadius: theme.borderRadius.md,
                    padding: theme.spacing.md,
                  }}
                >
                  <h5
                    style={{
                      fontSize: theme.typography.fontSize.sm,
                      fontWeight: theme.typography.fontWeight.semibold,
                      color: theme.colors.text.primary,
                      marginBottom: theme.spacing.sm,
                    }}
                  >
                    {tableName}
                  </h5>
                  
                  {Object.entries(table.columns).map(([columnName, column]: [string, any]) => (
                    <div
                      key={columnName}
                      style={{
                        fontSize: theme.typography.fontSize.xs,
                        color: theme.colors.text.secondary,
                        marginBottom: theme.spacing.xs,
                        fontFamily: 'monospace',
                      }}
                    >
                      <strong>{column.name}</strong> ({column.type})<br />
                      <code style={{ fontSize: '10px' }}>{column.expression}</code>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        )}

        {generatedCode && (
          <div
            style={{
              background: theme.colors.surface,
              backdropFilter: `blur(${theme.blur.md})`,
              WebkitBackdropFilter: `blur(${theme.blur.md})`,
              border: `1px solid ${theme.colors.border.glass}`,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.xl,
            }}
          >
            <h3
              style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.semibold,
                color: theme.colors.text.primary,
                marginBottom: theme.spacing.md,
              }}
            >
              Generated Code
            </h3>
            
            <p
              style={{
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.secondary,
                marginBottom: theme.spacing.md,
              }}
            >
              Copy this code to update your data model configuration:
            </p>

            <pre
              style={{
                background: theme.colors.surfaceElevated,
                border: `1px solid ${theme.colors.border.light}`,
                borderRadius: theme.borderRadius.md,
                padding: theme.spacing.md,
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.primary,
                overflow: 'auto',
                maxHeight: '400px',
                fontFamily: 'monospace',
                lineHeight: theme.typography.lineHeight.relaxed,
              }}
            >
              {generatedCode}
            </pre>

            <button
              onClick={() => navigator.clipboard.writeText(generatedCode)}
              style={{
                marginTop: theme.spacing.md,
                padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                background: theme.colors.secondary,
                color: 'white',
                border: 'none',
                borderRadius: theme.borderRadius.md,
                fontSize: theme.typography.fontSize.sm,
                cursor: 'pointer',
                transition: theme.transitions.fast,
              }}
            >
              Copy to Clipboard
            </button>
          </div>
        )}
      </div>
    </PageLayout>
  );
};
