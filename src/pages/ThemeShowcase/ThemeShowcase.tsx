import React from 'react';
import { PageLayout } from '../../components/layout/PageLayout';
import { useTheme } from '../../hooks/useTheme';
import { themePresets } from '../../config/themes/presets';

export const ThemeShowcase: React.FC = () => {
  const { theme, setTheme, toggleDarkMode, isDarkMode, resetTheme } = useTheme();

  return (
    <PageLayout title="Theme Showcase">
      <div style={{
        minHeight: '100vh',
        background: theme.colors.backgroundGradient,
        padding: theme.spacing.xl,
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
        }}>
          {/* Header */}
          <div style={{
            marginBottom: theme.spacing.xxl,
            textAlign: 'center',
          }}>
            <h1 style={{
              fontSize: theme.typography.fontSize.xxxl,
              fontWeight: theme.typography.fontWeight.bold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}>
              Theme Management System
            </h1>
            <p style={{
              fontSize: theme.typography.fontSize.lg,
              color: theme.colors.text.secondary,
              maxWidth: '600px',
              margin: '0 auto',
            }}>
              Manage your visual identity with our dynamic theming system.
            </p>
          </div>

          {/* Theme Controls */}
          <div style={{
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.xl,
            padding: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
            backdropFilter: `blur(${theme.blur.md})`,
            border: `1px solid ${theme.colors.border.glass}`,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.lg,
            }}>
              Theme Controls
            </h2>
            
            <div style={{
              display: 'flex',
              gap: theme.spacing.md,
              flexWrap: 'wrap',
            }}>
              <button
                onClick={toggleDarkMode}
                style={{
                  padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                  background: theme.colors.primary,
                  color: theme.colors.text.inverse,
                  border: 'none',
                  borderRadius: theme.borderRadius.md,
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.medium,
                  cursor: 'pointer',
                  transition: theme.transitions.fast,
                }}
              >
                Toggle {isDarkMode ? 'Light' : 'Dark'} Mode
              </button>
              
              <button
                onClick={resetTheme}
                style={{
                  padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                  background: theme.colors.secondary,
                  color: theme.colors.text.inverse,
                  border: 'none',
                  borderRadius: theme.borderRadius.md,
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.medium,
                  cursor: 'pointer',
                  transition: theme.transitions.fast,
                }}
              >
                Reset to Default
              </button>
            </div>
          </div>

          {/* Theme Presets */}
          <div style={{
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.xl,
            padding: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
            backdropFilter: `blur(${theme.blur.md})`,
            border: `1px solid ${theme.colors.border.glass}`,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.lg,
            }}>
              Theme Presets
            </h2>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: theme.spacing.md,
            }}>
              {themePresets.map((preset) => (
                <button
                  key={preset.id}
                  onClick={() => setTheme(preset.theme)}
                  style={{
                    padding: theme.spacing.md,
                    background: theme.id === preset.id ? theme.colors.surfaceElevated : theme.colors.surface,
                    border: `2px solid ${theme.id === preset.id ? theme.colors.primary : theme.colors.border.DEFAULT}`,
                    borderRadius: theme.borderRadius.lg,
                    cursor: 'pointer',
                    transition: theme.transitions.fast,
                    textAlign: 'left',
                  }}
                >
                  <div style={{
                    fontSize: theme.typography.fontSize.md,
                    fontWeight: theme.typography.fontWeight.semibold,
                    color: theme.colors.text.primary,
                    marginBottom: theme.spacing.xs,
                  }}>
                    {preset.name}
                  </div>
                  <div style={{
                    fontSize: theme.typography.fontSize.sm,
                    color: theme.colors.text.secondary,
                  }}>
                    {preset.description}
                  </div>
                  <div style={{
                    display: 'flex',
                    gap: 4,
                    marginTop: theme.spacing.sm,
                  }}>
                    <div style={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      background: preset.theme.colors.primary,
                    }} />
                    <div style={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      background: preset.theme.colors.secondary,
                    }} />
                    <div style={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      background: preset.theme.colors.accent,
                    }} />
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Color Palette */}
          <div style={{
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.xl,
            padding: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
            backdropFilter: `blur(${theme.blur.md})`,
            border: `1px solid ${theme.colors.border.glass}`,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.lg,
            }}>
              Current Color Palette
            </h2>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
              gap: theme.spacing.md,
            }}>
              {Object.entries({
                Primary: theme.colors.primary,
                Secondary: theme.colors.secondary,
                Accent: theme.colors.accent,
                Background: theme.colors.background.primary,
                Surface: theme.colors.surface,
                Text: theme.colors.text.primary,
                Border: theme.colors.border.DEFAULT,
                Error: theme.colors.error,
                Warning: theme.colors.warning,
                Success: theme.colors.success,
                Info: theme.colors.info,
              }).map(([name, color]) => (
                <div key={name} style={{
                  textAlign: 'center',
                }}>
                  <div style={{
                    width: '100%',
                    height: 80,
                    background: color,
                    borderRadius: theme.borderRadius.md,
                    marginBottom: theme.spacing.sm,
                    border: `1px solid ${theme.colors.border.DEFAULT}`,
                  }} />
                  <div style={{
                    fontSize: theme.typography.fontSize.sm,
                    fontWeight: theme.typography.fontWeight.medium,
                    color: theme.colors.text.primary,
                  }}>
                    {name}
                  </div>
                  <div style={{
                    fontSize: theme.typography.fontSize.xs,
                    color: theme.colors.text.secondary,
                    fontFamily: theme.typography.fontFamily.mono,
                  }}>
                    {color}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Typography */}
          <div style={{
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.xl,
            padding: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
            backdropFilter: `blur(${theme.blur.md})`,
            border: `1px solid ${theme.colors.border.glass}`,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.lg,
            }}>
              Typography
            </h2>
            
            <div style={{ marginBottom: theme.spacing.lg }}>
              <h1 style={{ fontSize: theme.typography.fontSize.xxxl, margin: 0 }}>Heading 1</h1>
              <h2 style={{ fontSize: theme.typography.fontSize.xxl, margin: 0 }}>Heading 2</h2>
              <h3 style={{ fontSize: theme.typography.fontSize.xl, margin: 0 }}>Heading 3</h3>
              <p style={{ fontSize: theme.typography.fontSize.lg, margin: 0 }}>Large text</p>
              <p style={{ fontSize: theme.typography.fontSize.md, margin: 0 }}>Body text</p>
              <p style={{ fontSize: theme.typography.fontSize.sm, margin: 0 }}>Small text</p>
              <p style={{ fontSize: theme.typography.fontSize.xs, margin: 0 }}>Extra small text</p>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};