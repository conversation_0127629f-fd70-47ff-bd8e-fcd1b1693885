import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { PageLayout } from "../../components/layout/PageLayout";
import { theme } from "../../config/theme.config";
import { DashParserService } from "../../services/dashParser.service";
import type { DashFile } from "../../types/dash.types";
import type { DashboardConfig } from "../../types/dashboard.types";

export const DashboardImporter: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parsedDash, setParsedDash] = useState<DashFile | null>(null);
  const [dashboardConfig, setDashboardConfig] =
    useState<DashboardConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.name.endsWith(".dash")) {
      setSelectedFile(file);
      setError(null);
      parseDashFile(file);
    } else {
      setError("Please select a valid .dash file");
    }
  };

  const parseDashFile = async (file: File) => {
    try {
      const content = await file.text();
      const dash = DashParserService.parseDashFile(content);
      setParsedDash(dash);

      const config = DashParserService.convertToDashboardConfig(dash);
      setDashboardConfig(config);
    } catch (err) {
      setError(`Failed to parse file: ${err}`);
    }
  };

  const handleImport = () => {
    if (dashboardConfig) {
      // TODO: Save the dashboard config and navigate to it
      // Dashboard import functionality
      // For now, just navigate to home
      navigate("/");
    }
  };

  return (
    <PageLayout title="Import Dashboard" data-oid="213v.u1">
      <div style={{ maxWidth: "800px", margin: "0 auto" }} data-oid="lbwcuu.">
        <div
          style={{
            background: theme.colors.surface,
            backdropFilter: `blur(${theme.blur.lg})`,
            WebkitBackdropFilter: `blur(${theme.blur.lg})`,
            borderRadius: theme.borderRadius.xl,
            padding: theme.spacing.xxl,
            boxShadow: theme.shadows.glass,
            border: `1px solid ${theme.colors.border.glass}`,
          }}
          data-oid="ns::o70"
        >
          <h2
            style={{
              margin: `0 0 ${theme.spacing.lg}px 0`,
              color: theme.colors.text.primary,
            }}
            data-oid="-0.d_8d"
          >
            Import Sisense Dashboard
          </h2>

          <div
            style={{
              border: `2px dashed ${theme.colors.border.glass}`,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.xxl,
              textAlign: "center",
              background: theme.colors.surface,
              backdropFilter: `blur(${theme.blur.sm})`,
              WebkitBackdropFilter: `blur(${theme.blur.sm})`,
              transition: `all ${theme.transitions.normal}`,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = theme.colors.primary;
              e.currentTarget.style.background = theme.colors.surfaceElevated;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = theme.colors.border.glass;
              e.currentTarget.style.background = theme.colors.surface;
            }}
            data-oid="7i-4yws"
          >
            <input
              type="file"
              accept=".dash"
              onChange={handleFileSelect}
              style={{ display: "none" }}
              id="dash-file-input"
              data-oid="fic7w4m"
            />

            <label
              htmlFor="dash-file-input"
              style={{
                display: "inline-block",
                padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                background: theme.colors.gradients.primary,
                color: "white",
                borderRadius: theme.borderRadius.lg,
                cursor: "pointer",
                fontWeight: theme.typography.fontWeight.semibold,
                transition: `all ${theme.transitions.normal}`,
                boxShadow: theme.shadows.md,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = "translateY(-2px)";
                e.currentTarget.style.boxShadow = theme.shadows.lg;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = "translateY(0)";
                e.currentTarget.style.boxShadow = theme.shadows.md;
              }}
              data-oid=".p49qan"
            >
              Select .dash File
            </label>
            <p
              style={{
                margin: `${theme.spacing.md}px 0 0 0`,
                color: theme.colors.text.secondary,
              }}
              data-oid="wcfy6ei"
            >
              Choose a Sisense dashboard export file (.dash)
            </p>
          </div>

          {error && (
            <div
              style={{
                marginTop: theme.spacing.md,
                padding: theme.spacing.lg,
                background: "rgba(239, 68, 68, 0.1)",
                backdropFilter: `blur(${theme.blur.sm})`,
                WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                borderRadius: theme.borderRadius.md,
                color: theme.colors.error,
                border: `1px solid rgba(239, 68, 68, 0.2)`,
              }}
              data-oid="307.89g"
            >
              {error}
            </div>
          )}

          {parsedDash && (
            <div style={{ marginTop: theme.spacing.xl }} data-oid="4z310gq">
              <h3
                style={{
                  margin: `0 0 ${theme.spacing.md}px 0`,
                  color: theme.colors.text.primary,
                }}
                data-oid="j-fi_tk"
              >
                Dashboard Preview
              </h3>

              <div
                style={{
                  background: theme.colors.surfaceElevated,
                  backdropFilter: `blur(${theme.blur.md})`,
                  WebkitBackdropFilter: `blur(${theme.blur.md})`,
                  borderRadius: theme.borderRadius.lg,
                  padding: theme.spacing.lg,
                  border: `1px solid ${theme.colors.border.glass}`,
                }}
                data-oid="iiu1786"
              >
                <p data-oid="32tjih3">
                  <strong data-oid="w1r4yr3">Title:</strong> {parsedDash.title}
                </p>
                <p data-oid="0k3sslq">
                  <strong data-oid="-c7dbzd">Description:</strong>{" "}
                  {parsedDash.desc || "No description"}
                </p>
                <p data-oid="ck0lfaz">
                  <strong data-oid="0u45t.8">Data Source:</strong>{" "}
                  {parsedDash.datasource?.title || "Unknown"}
                </p>
                <p data-oid="a-839lj">
                  <strong data-oid=".n.g2b4">Widgets:</strong>{" "}
                  {parsedDash.widgets.length}
                </p>
              </div>

              {dashboardConfig && (
                <div style={{ marginTop: theme.spacing.md }} data-oid="im91g99">
                  <h4 data-oid="v5:592s">Converted Configuration:</h4>
                  <div
                    style={{
                      background: theme.colors.surfaceHover,
                      backdropFilter: `blur(${theme.blur.sm})`,
                      WebkitBackdropFilter: `blur(${theme.blur.sm})`,
                      borderRadius: theme.borderRadius.md,
                      padding: theme.spacing.lg,
                      fontSize: "14px",
                      border: `1px solid ${theme.colors.border.glass}`,
                    }}
                    data-oid="ouxtc7e"
                  >
                    <p data-oid="ngpim-g">
                      <strong data-oid="hqdnl8b">Dashboard ID:</strong>{" "}
                      {dashboardConfig.id}
                    </p>
                    <p data-oid="h-aj7eu">
                      <strong data-oid="z0wv.__">Path:</strong>{" "}
                      {dashboardConfig.path}
                    </p>
                    <p data-oid="sk6lxew">
                      <strong data-oid="0c9x.:7">Widgets Converted:</strong>{" "}
                      {dashboardConfig.widgets.length}
                    </p>

                    <details
                      style={{ marginTop: theme.spacing.sm }}
                      data-oid="nvtgacw"
                    >
                      <summary
                        style={{
                          cursor: "pointer",
                          color: theme.colors.primary,
                        }}
                        data-oid="rl_a75p"
                      >
                        Widget Details
                      </summary>
                      <ul
                        style={{
                          margin: `${theme.spacing.sm}px 0`,
                          paddingLeft: theme.spacing.lg,
                        }}
                        data-oid="x6r0hpk"
                      >
                        {dashboardConfig.widgets.map((widget) => (
                          <li key={widget.id} data-oid="i-86udt">
                            {widget.title || "Untitled"} - Type: {widget.type}
                          </li>
                        ))}
                      </ul>
                    </details>
                  </div>
                </div>
              )}

              <div
                style={{
                  marginTop: theme.spacing.xl,
                  display: "flex",
                  gap: theme.spacing.md,
                  justifyContent: "flex-end",
                }}
                data-oid="awh7ooz"
              >
                <button
                  onClick={() => navigate("/")}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    backgroundColor: "transparent",
                    color: theme.colors.text.secondary,
                    border: `1px solid ${theme.colors.text.secondary}`,
                    borderRadius: theme.borderRadius.md,
                    cursor: "pointer",
                    fontWeight: 500,
                  }}
                  data-oid="tbqxde3"
                >
                  Cancel
                </button>
                <button
                  onClick={handleImport}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    backgroundColor: theme.colors.primary,
                    color: "white",
                    border: "none",
                    borderRadius: theme.borderRadius.md,
                    cursor: "pointer",
                    fontWeight: 500,
                  }}
                  data-oid="9mqpz4b"
                >
                  Import Dashboard
                </button>
              </div>
            </div>
          )}
        </div>

        <div
          style={{
            marginTop: theme.spacing.xl,
            backgroundColor: theme.colors.surface,
            borderRadius: theme.borderRadius.card,
            padding: theme.spacing.xl,
            boxShadow: theme.shadows.widget,
          }}
          data-oid=".y6u5.-"
        >
          <h3
            style={{
              margin: `0 0 ${theme.spacing.md}px 0`,
              color: theme.colors.text.primary,
            }}
            data-oid="kk:1cc3"
          >
            Available Dashboards
          </h3>
          <p
            style={{
              color: theme.colors.text.secondary,
              marginBottom: theme.spacing.md,
            }}
            data-oid="amy.kmj"
          >
            The following .dash files are available in your project:
          </p>
          <ul
            style={{
              margin: 0,
              paddingLeft: theme.spacing.lg,
              color: theme.colors.text.secondary,
            }}
            data-oid="x3ge3o6"
          >
            <li data-oid="ya9144l">SummaryDashboard.dash</li>
            <li data-oid="hnrdi_s">2ContractAwards.dash</li>
            <li data-oid=":yg377o">3ValuePutinPlace.dash</li>
            <li data-oid="47e60lb">4Federal-AidObligations.dash</li>
            <li data-oid="0_g_zcr">6StateLegislativeInitiatives.dash</li>
            <li data-oid="8f:n9d5">7StateDOTBudgets_2025.dash</li>
            <li data-oid="25srf5e">9MaterialPrices.dash</li>
          </ul>
        </div>
      </div>
    </PageLayout>
  );
};
