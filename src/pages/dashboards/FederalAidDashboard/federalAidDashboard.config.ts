import type { DashboardConfig } from '../../../types/dashboard.types';

export const federalAidDashboardConfig: DashboardConfig = {
  id: '5d7fb4112469173fb4e2b1c9',
  title: '4. Federal-Aid Obligations',
  description: 'Federal-aid highway obligations and commitments tracking',
  path: '/dashboard/federal-aid',
  widgets: [
    // Row 1 - State obligations chart
    {
      id: '5d7fb4112469173fb4e2b1d3',
      type: 'chart',
      title: 'State Obligations of Federal-Aid Highway and Highway Safety Construction Funds',
      position: { x: 0, y: 0, w: 12, h: 5 },
      config: {
        chartType: 'scatter', // Map visualization in actual implementation
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Obligations', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 2 - YTD and annual obligations
    {
      id: '5d7fb4112469173fb4e2b1d4',
      type: 'chart',
      title: 'Fiscal Year 2025 YTD Obligations',
      position: { x: 0, y: 5, w: 6, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Obligations', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '65778843c11a4a0033e59fa5',
      type: 'kpi',
      title: 'Fiscal Year 2025 Obligations',
      position: { x: 6, y: 5, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5d7fb4112469173fb4e2b1d5',
      type: 'chart',
      title: 'Fiscal Year Obligation of Federal-Aid Highway & Highway Safety Construction Funds',
      position: { x: 9, y: 5, w: 3, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Fiscal Year' }],
          value: [{ name: 'Obligations', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - Commitments by type and program
    {
      id: '65774b2cc11a4a0033e59d70',
      type: 'chart',
      title: 'Federal-Aid Commitments by Major Type, FY 2022 to Date',
      position: { x: 0, y: 9, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Type' }],
          value: [{ name: 'Commitments', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '65774b3ec11a4a0033e59d72',
      type: 'chart',
      title: 'Federal-Aid Commitments by Program, FY 2022 to Date',
      position: { x: 6, y: 9, w: 6, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Program' }],
          value: [{ name: 'Commitments', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 4 - Major projects and spending breakdown
    {
      id: '65775d8ac11a4a0033e59d96',
      type: 'table',
      title: 'Details for Major Projects Receiving IIJA Funds, FY 2022 to Date',
      position: { x: 0, y: 13, w: 8, h: 4 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'Project' } },
            { column: { name: 'State' } },
            { column: { name: 'Funding Amount' } },
            { column: { name: 'Program' } },
            { column: { name: 'Status' } },
          ],
        },
      },
    },
    {
      id: '6634eb68ea9830003f89612e',
      type: 'chart',
      title: 'Breakdown of Federal Aid Highway Projects by Type of Spending, FY 2022- FY 2024',
      position: { x: 8, y: 13, w: 4, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'Spending Type' }],
          value: [{ name: 'Amount', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - Total value trend
    {
      id: '65777f64c11a4a0033e59f69',
      type: 'chart',
      title: 'Total Value of Federal-Aid Highway Obligations (Lagged)',
      position: { x: 0, y: 17, w: 12, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Obligations', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
  ],
};