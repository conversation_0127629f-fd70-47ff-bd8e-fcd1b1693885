import React from "react";
import { useNavigate } from "react-router-dom";
import { PageLayout } from "../../../components/layout/PageLayout";
import { DashboardLayout } from "../../../components/layout/DashboardLayout";
import { theme } from "../../../config/theme.config";
import { createWidget } from "../../../utils/widgetFactory";
import { federalAidDashboardConfig } from "./federalAidDashboard.config";

export const FederalAidDashboard: React.FC = () => {
  const navigate = useNavigate();
  const {
    id: dashboardId,
    widgets,
    title,
    description,
  } = federalAidDashboardConfig;

  const dashboardActions = (
    <>
      <button
        onClick={() => navigate("/")}
        style={{
          backgroundColor: "transparent",
          color: theme.colors.primary,
          border: `1px solid ${theme.colors.primary}`,
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `all ${theme.transitions.fast}`,
        }}
        data-oid="c9fb:o0"
      >
        ← Back to Home
      </button>
      <button
        style={{
          backgroundColor: theme.colors.primary,
          color: "white",
          border: "none",
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `background-color ${theme.transitions.fast}`,
        }}
        data-oid="es8y4vd"
      >
        Export Report
      </button>
    </>
  );

  return (
    <PageLayout title="Federal-Aid Obligations" data-oid="s0qf2.6">
      <DashboardLayout
        title={title}
        description={description}
        actions={dashboardActions}
        data-oid="0:cdfyz"
      >
        {widgets.map((widget) => createWidget(widget, dashboardId))}
      </DashboardLayout>
    </PageLayout>
  );
};
