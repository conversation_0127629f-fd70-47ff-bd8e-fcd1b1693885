import type { DashboardConfig } from '../../../types/dashboard.types';

export const stateDOTBudgetsDashboardConfig: DashboardConfig = {
  id: '667ac76d6cb17e003386f82a',
  title: '7. State DOT Budgets 2025',
  description: 'State Department of Transportation budgets and spending analysis for FY 2025',
  path: '/dashboard/state-dot-budgets',
  widgets: [
    // Row 1 - Budget overview
    {
      id: '667ac76d6cb17e003386f82c',
      type: 'kpi',
      title: 'FY 2025 Enacted Budget Data (as Available)',
      position: { x: 0, y: 0, w: 12, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    // Row 2 - Spending authority map
    {
      id: '667ac76d6cb17e003386f82d',
      type: 'chart',
      title: 'FY 2025 Default Budget Plan Spending Authority (Missing States Estimated in National Totals)',
      position: { x: 0, y: 2, w: 12, h: 5 },
      config: {
        chartType: 'scatter', // Map visualization in actual implementation
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Spending Authority', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - Budget details and capital spending
    {
      id: '667ac76d6cb17e003386f82e',
      type: 'table',
      title: 'Transportation Budget or Work Program Details',
      position: { x: 0, y: 7, w: 6, h: 4 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'State' } },
            { column: { name: 'Total Budget' } },
            { column: { name: 'Capital' } },
            { column: { name: 'Operations' } },
            { column: { name: 'Debt Service' } },
          ],
        },
      },
    },
    {
      id: '667ac76d6cb17e003386f832',
      type: 'chart',
      title: 'Select Items - Highway & Bridge Capital Spending',
      position: { x: 6, y: 7, w: 6, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Capital Spending', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 4 - Revenues
    {
      id: '667ac76d6cb17e003386f833',
      type: 'chart',
      title: 'FY 2025 Default Budget Plan Revenues (Missing States Estimated in National Totals)',
      position: { x: 0, y: 11, w: 12, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Revenue', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - Revenue details and federal reimbursements
    {
      id: '667ac76d6cb17e003386f834',
      type: 'table',
      title: 'Revenue Details',
      position: { x: 0, y: 15, w: 6, h: 4 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'State' } },
            { column: { name: 'State Revenue' } },
            { column: { name: 'Federal Aid' } },
            { column: { name: 'Bonds' } },
            { column: { name: 'Other' } },
            { column: { name: 'Total' } },
          ],
        },
      },
    },
    {
      id: '667ac76d6cb17e003386f835',
      type: 'chart',
      title: 'Federal Reimbursements',
      position: { x: 6, y: 15, w: 6, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Federal Reimbursements', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 6 - Spending and revenue by type
    {
      id: '667ac76d6cb17e003386f838',
      type: 'chart',
      title: 'FY 2025 YTD Default Budget Plan Spending Authority, by Type',
      position: { x: 0, y: 19, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Spending Type' }],
          value: [{ name: 'Amount', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '667ac76d6cb17e003386f839',
      type: 'chart',
      title: 'FY 2025 Default Budget Plan Revenues, by Type (Federal Funds Include GARVEE Bonds)',
      position: { x: 6, y: 19, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Revenue Type' }],
          value: [{ name: 'Amount', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 7 - Detailed budget information
    {
      id: '667ac76d6cb17e003386f831',
      type: 'table',
      title: 'Details of State Budgets, Workplans and Revenues',
      position: { x: 0, y: 23, w: 12, h: 6 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'State' } },
            { column: { name: 'FY 2025 Budget' } },
            { column: { name: 'Capital Program' } },
            { column: { name: 'State Revenues' } },
            { column: { name: 'Federal Aid' } },
            { column: { name: 'Bond Proceeds' } },
            { column: { name: 'YoY Change %' } },
          ],
        },
      },
    },
  ],
};