/**
 * Unified Summary Dashboard component
 * Supports both Sisense widgets and custom charts via prop
 */

import React from 'react';
import { DashboardLayout } from '../../../components/layout/DashboardLayout';
import { SisenseWidget } from '../../../components/widgets/SisenseWidget/SisenseWidget';
import { CustomBarChart } from '../../../components/charts/CustomBarChart';
import { CustomLineChart } from '../../../components/charts/CustomLineChart';
import { CustomPieChart } from '../../../components/charts/CustomPieChart';
import { KPIWidget } from '../../../components/widgets/KPIWidget/KPIWidget';
import { summaryDashboardConfig } from './summaryDashboard.config';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';

interface SummaryDashboardProps {
  useCustomCharts?: boolean;
}

export const SummaryDashboard: React.FC<SummaryDashboardProps> = ({ 
  useCustomCharts = false 
}) => {
  const dashboardId = '66ae2a13ff29530033a84fd3';

  // Mock data for custom charts (in production, this would come from your data source)
  const mockKPIData = {
    currentAwards: 285.3,
    awardsTrend: 12.5,
    vpip: 142.7,
    vpipTrend: -3.2,
    employment: 425000,
    employmentTrend: 8.1,
    materials: 118.5,
    materialsTrend: 15.3,
  };

  const mockChartData = {
    monthlyAwards: [
      { month: 'Jan', awards: 245.2, vpip: 138.5 },
      { month: 'Feb', awards: 252.8, vpip: 141.2 },
      { month: 'Mar', awards: 268.4, vpip: 139.8 },
      { month: 'Apr', awards: 275.1, vpip: 142.3 },
      { month: 'May', awards: 282.6, vpip: 140.9 },
      { month: 'Jun', awards: 285.3, vpip: 142.7 },
    ],
    modeDistribution: [
      { name: 'Highway', value: 45, color: '#0088FE' },
      { name: 'Bridge', value: 25, color: '#00C49F' },
      { name: 'Transit', value: 20, color: '#FFBB28' },
      { name: 'Other', value: 10, color: '#FF8042' },
    ],
    employmentTrend: [
      { month: 'Jan', employment: 392000 },
      { month: 'Feb', employment: 398000 },
      { month: 'Mar', employment: 405000 },
      { month: 'Apr', employment: 412000 },
      { month: 'May', employment: 419000 },
      { month: 'Jun', employment: 425000 },
    ],
  };

  const renderWidget = (widgetId: string, position: any, title?: string) => {
    if (useCustomCharts) {
      // Render custom charts based on widget type
      switch (widgetId) {
        case '66ad1c88dc9c8100348c9cf0': // KPI - Current Month Awards
          return (
            <KPIWidget
              key={widgetId}
              title="Current Month Awards"
              value={`$${mockKPIData.currentAwards}B`}
              trend={mockKPIData.awardsTrend}
              position={position}
            />
          );
        case '66ad1fbfff29530033a8341f': // KPI - VPIP
          return (
            <KPIWidget
              key={widgetId}
              title="Value Put in Place"
              value={`$${mockKPIData.vpip}B`}
              trend={mockKPIData.vpipTrend}
              position={position}
            />
          );
        case '66ad1a29ff29530033a82fa0': // Line Chart - Monthly Trends
          return (
            <CustomLineChart
              key={widgetId}
              data={mockChartData.monthlyAwards}
              xKey="month"
              yKeys={['awards', 'vpip']}
              title="Monthly Awards & VPIP Trend"
              position={position}
              colors={['#8884d8', '#82ca9d']}
            />
          );
        case '66ad1df4dc9c8100348c9d87': // Pie Chart - Mode Distribution
          return (
            <CustomPieChart
              key={widgetId}
              data={mockChartData.modeDistribution}
              title="Construction by Mode"
              position={position}
            />
          );
        case '66ad208eff29530033a8349f': // Bar Chart - Employment
          return (
            <CustomBarChart
              key={widgetId}
              data={mockChartData.employmentTrend}
              xKey="month"
              yKey="employment"
              title="Employment Trend"
              position={position}
              color="#8884d8"
            />
          );
        default:
          // Fallback to Sisense widget
          return (
            <SisenseWidget
              key={widgetId}
              widgetId={widgetId}
              dashboardId={dashboardId}
              title={title}
              position={position}
            />
          );
      }
    }

    // Default: render Sisense widget
    return (
      <SisenseWidget
        key={widgetId}
        widgetId={widgetId}
        dashboardId={dashboardId}
        title={title}
        position={position}
      />
    );
  };

  return (
    <ErrorBoundary context="summary-dashboard">
      <DashboardLayout
        title={summaryDashboardConfig.title}
        subtitle={useCustomCharts ? 'Custom Charts View' : undefined}
        config={summaryDashboardConfig}
      >
        {summaryDashboardConfig.widgets.map((widget) => 
          renderWidget(widget.id, widget.position, widget.title)
        )}
      </DashboardLayout>
    </ErrorBoundary>
  );
};

// Export variations for backward compatibility
export const SummaryDashboardWithCustomCharts: React.FC = () => (
  <SummaryDashboard useCustomCharts={true} />
);