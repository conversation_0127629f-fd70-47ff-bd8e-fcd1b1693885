import React from "react";
import { useNavigate } from "react-router-dom";
import { PageLayout } from "../../../components/layout/PageLayout";
import { DashboardLayout } from "../../../components/layout/DashboardLayout";
import { theme } from "../../../config/theme.config";
import { createWidget } from "../../../utils/widgetFactory";
import { summaryDashboardConfig } from "./summaryDashboard.config";
import { 
  CustomLine<PERSON>hart, 
  CustomBarChart, 
  CustomPieChart,
  CustomDonutChart 
} from "../../../components/charts";

export const SummaryDashboardWithCustomCharts: React.FC = () => {
  const navigate = useNavigate();
  const {
    id: dashboardId,
    widgets,
    title,
    description,
  } = summaryDashboardConfig;

  const dashboardActions = (
    <>
      <button
        onClick={() => navigate("/")}
        style={{
          backgroundColor: "transparent",
          color: theme.colors.primary,
          border: `1px solid ${theme.colors.primary}`,
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `all ${theme.transitions.fast}`,
        }}
        data-oid="e8a:qif"
      >
        ← Back to Home
      </button>
      <button
        style={{
          backgroundColor: theme.colors.primary,
          color: "white",
          border: "none",
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `background-color ${theme.transitions.fast}`,
        }}
        data-oid="ig8llfn"
      >
        Export Report
      </button>
    </>
  );

  return (
    <PageLayout title="ARTBA Economics Dashboard" data-oid="u07tw7s">
      <DashboardLayout
        title={`${title} (Custom Charts)`}
        description={description}
        actions={dashboardActions}
        data-oid="zonch3w"
      >
        {/* Keep the first two KPI widgets as-is */}
        {widgets.slice(0, 2).map((widget) => createWidget(widget, dashboardId))}
        
        {/* Replace the ARTBA 2025 Transportation Construction Market Outlook with custom bar chart */}
        <CustomBarChart
          widgetId="5defaedc4faf0a20b09a20f0"
          dashboardId={dashboardId}
          title="ARTBA 2025 Transportation Construction Market Outlook"
          position={{ x: 0, y: 2, w: 6, h: 4 }}
          formatType="currency"
          barColor={theme.colors.primary}
          gradient={true}
        />
        
        {/* Replace the U.S. Transp. Const. Work by Mode with custom donut chart */}
        <CustomDonutChart
          widgetId="5defaedc4faf0a20b09a20f1"
          dashboardId={dashboardId}
          title="U.S. Transp. Const. Work by Mode"
          position={{ x: 6, y: 2, w: 6, h: 4 }}
          formatType="percentage"
          colors={[
            theme.colors.primary,
            theme.colors.secondary,
            theme.colors.accent,
            '#6EDA55',
            '#9b9bd7',
          ]}
        />
        
        {/* Replace the TTM U.S. Transportation Const. Work with custom line chart */}
        <CustomLineChart
          widgetId="5defaedc4faf0a20b09a20ec"
          dashboardId={dashboardId}
          title="TTM U.S. Transportation Const. Work"
          position={{ x: 0, y: 6, w: 6, h: 4 }}
          formatType="currency"
          lineColor={theme.colors.primary}
          strokeWidth={3}
          showGrid={true}
        />
        
        {/* Replace the TTM State & Local Govt. Transp. Contract Awards with custom line chart */}
        <CustomLineChart
          widgetId="658452cfc11a4a0033e5b1a6"
          dashboardId={dashboardId}
          title="TTM State & Local Govt. Transp. Contract Awards"
          position={{ x: 6, y: 6, w: 6, h: 4 }}
          formatType="currency"
          lineColor={theme.colors.secondary}
          strokeWidth={3}
          showGrid={true}
        />
        
        {/* Replace the General Election Transportation Funding Ballot Measures with custom bar chart */}
        <CustomBarChart
          widgetId="672e31cba541a000326034f4"
          dashboardId={dashboardId}
          title="General Election Transportation Funding Ballot Measures, 2014 - 2024"
          position={{ x: 0, y: 10, w: 12, h: 4 }}
          formatType="number"
          barColor={theme.colors.accent}
          layout="vertical"
          gradient={true}
        />
        
        {/* Keep the last widget as-is for comparison */}
        {widgets.slice(-1).map((widget) => createWidget(widget, dashboardId))}
      </DashboardLayout>
    </PageLayout>
  );
};
