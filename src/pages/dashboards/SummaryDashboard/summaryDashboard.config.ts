import type { DashboardConfig } from '../../../types/dashboard.types';

// Dashboard configuration based on actual screenshot
export const summaryDashboardConfig: DashboardConfig = {
  id: '5defaedc4faf0a20b09a20e6',
  title: '1. Summary Dashboard',
  description: 'Executive summary of key transportation industry metrics',
  path: '/dashboard/summary',
  widgets: [
    // Row 1 - Key Metrics from screenshot
    {
      id: '5e4464696189c43b98996ed4', // RICHTEXT_MAIN.TITLE
      type: 'kpi',
      title: 'TTM State & Local Contract Aw...',
      position: { x: 0, y: 0, w: 3, h: 2 },
      config: {
        value: 136000000000, // $136.0B from screenshot
        format: 'currency',
        prefix: '$',
        suffix: 'B',
      },
    },
    {
      id: '5e4464836189c43b98996ed6', // RICHTEXT_MAIN.TITLE
      type: 'kpi',
      title: 'TTM Value of U.S. Transp. Const...',
      position: { x: 3, y: 0, w: 3, h: 2 },
      config: {
        value: 212600000000, // $212.6B from screenshot
        format: 'currency',
        prefix: '$',
        suffix: 'B',
      },
    },
    // Row 2 - ARTBA 2025 Transportation Market Outlook
    {
      id: '5defaedc4faf0a20b09a20f0', // ARTBA 2025 Transportation Construction Market Outlook
      type: 'chart',
      title: 'ARTBA 2025 Transportation Construction Market Outlook',
      position: { x: 0, y: 2, w: 6, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Market Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defaedc4faf0a20b09a20f1', // U.S. Transp. Const. Work by Mode
      type: 'chart',
      title: 'U.S. Transp. Const. Work by Mode',
      position: { x: 6, y: 2, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Percentage', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - TTM Transportation Construction Work charts
    {
      id: '5defaedc4faf0a20b09a20ec', // TTM U.S. Transportation Const. Work
      type: 'chart',
      title: 'TTM U.S. Transportation Const. Work',
      position: { x: 0, y: 6, w: 6, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '658452cfc11a4a0033e5b1a6', // TTM State & Local Govt. Transp. Contract Awards
      type: 'chart',
      title: 'TTM State & Local Govt. Transp. Contract Awards',
      position: { x: 6, y: 6, w: 6, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Awards', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 4 - Election Ballot Measures
    {
      id: '672e31cba541a000326034f4', // General Election Transportation Funding Ballot Measures, 2014 - 2024
      type: 'chart',
      title: 'General Election Transportation Funding Ballot Measures, 2014 - 2024',
      position: { x: 0, y: 10, w: 12, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [
            { name: 'Number of Finalized Measures', aggregation: 'sum' },
            { name: 'Percent of Measures Approved', aggregation: 'avg' },
          ],
          breakBy: [],
        },
      },
    },
    // Row 5 - State Legislative Map
    {
      id: '601c11d206e5562f8828ef8b', // Number of State Legislative Funding Measures Introduced in 2025
      type: 'chart',
      title: 'Number of State Legislative Funding Measures Introduced in 2025',
      position: { x: 0, y: 14, w: 12, h: 5 },
      config: {
        chartType: 'scatter', // Would be a map in real implementation
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Measures', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
  ],
};