import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { PageLayout } from "../../../components/layout/PageLayout";
import { DashboardLayout } from "../../../components/layout/DashboardLayout";
import { theme } from "../../../config/theme.config";
import { contractAwardsDashboardConfig } from "./contractAwardsDashboard.config";
import {
  Custom<PERSON>ine<PERSON><PERSON>,
  CustomBar<PERSON><PERSON>,
  Custom<PERSON>ie<PERSON>hart,
  CustomStackedBarChart,
  CustomMultiLineChart
} from "../../../components/charts";
import { DemoChart } from "../../../components/charts/DemoChart";

type TabType = 'MONTH' | 'YTD' | 'TTM' | 'ANNUAL';

export const ContractAwardsDashboardWithCustomCharts: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>('MONTH');
  
  const {
    id: dashboardId,
    title,
    description,
  } = contractAwardsDashboardConfig;

  const dashboardActions = (
    <>
      <button
        onClick={() => navigate("/")}
        style={{
          backgroundColor: "transparent",
          color: theme.colors.primary,
          border: `1px solid ${theme.colors.primary}`,
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `all ${theme.transitions.fast}`,
        }}
      >
        ← Back to Home
      </button>
      <button
        style={{
          backgroundColor: theme.colors.primary,
          color: "white",
          border: "none",
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: "14px",
          fontWeight: 500,
          cursor: "pointer",
          transition: `background-color ${theme.transitions.fast}`,
        }}
      >
        📊 Export Report
      </button>
    </>
  );

  const tabs = [
    { id: 'MONTH', label: '📅 Monthly', icon: '📅' },
    { id: 'YTD', label: '📊 Year to Date', icon: '📊' },
    { id: 'TTM', label: '📈 Trailing 12M', icon: '📈' },
    { id: 'ANNUAL', label: '🗓️ Annual', icon: '🗓️' },
  ] as const;

  const renderTabNavigation = () => (
    <div
      style={{
        display: 'flex',
        gap: theme.spacing.sm,
        marginBottom: theme.spacing.xl,
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: `blur(${theme.blur.md})`,
        WebkitBackdropFilter: `blur(${theme.blur.md})`,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.sm,
        border: `1px solid ${theme.colors.border.glass}`,
      }}
    >
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id as TabType)}
          style={{
            background: activeTab === tab.id 
              ? 'rgba(255, 255, 255, 0.3)' 
              : 'transparent',
            color: activeTab === tab.id 
              ? theme.colors.text.primary 
              : theme.colors.text.secondary,
            border: 'none',
            borderRadius: theme.borderRadius.md,
            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
            fontSize: theme.typography.fontSize.sm,
            fontWeight: activeTab === tab.id 
              ? theme.typography.fontWeight.semibold 
              : theme.typography.fontWeight.normal,
            cursor: 'pointer',
            transition: theme.transitions.fast,
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.xs,
          }}
        >
          <span>{tab.icon}</span>
          {tab.label}
        </button>
      ))}
    </div>
  );

  const renderKPICards = () => (
    <>
      {/* Monthly Value KPI */}
      <div
        style={{
          gridColumn: 'span 3',
          gridRow: 'span 2',
          background: theme.colors.surface,
          backdropFilter: `blur(${theme.blur.lg})`,
          WebkitBackdropFilter: `blur(${theme.blur.lg})`,
          borderRadius: theme.borderRadius.lg,
          boxShadow: theme.shadows.glass,
          padding: theme.spacing.lg,
          border: `1px solid ${theme.colors.border.glass}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <h3 style={{ 
          margin: 0, 
          marginBottom: theme.spacing.sm,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.lg,
        }}>
          Monthly Value
        </h3>
        <div style={{
          fontSize: '2.5rem',
          fontWeight: theme.typography.fontWeight.bold,
          color: '#00cee6',
          marginBottom: theme.spacing.xs,
        }}>
          $10.9B
        </div>
        <div style={{
          fontSize: theme.typography.fontSize.sm,
          color: theme.colors.text.secondary,
        }}>
          Number of Projects: 4,136
        </div>
      </div>

      {/* YTD Value KPI */}
      <div
        style={{
          gridColumn: 'span 3',
          gridRow: 'span 2',
          background: theme.colors.surface,
          backdropFilter: `blur(${theme.blur.lg})`,
          WebkitBackdropFilter: `blur(${theme.blur.lg})`,
          borderRadius: theme.borderRadius.lg,
          boxShadow: theme.shadows.glass,
          padding: theme.spacing.lg,
          border: `1px solid ${theme.colors.border.glass}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <h3 style={{ 
          margin: 0, 
          marginBottom: theme.spacing.sm,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.lg,
        }}>
          YTD Value
        </h3>
        <div style={{
          fontSize: '2.5rem',
          fontWeight: theme.typography.fontWeight.bold,
          color: '#9b9bd7',
          marginBottom: theme.spacing.xs,
        }}>
          $54.8B
        </div>
        <div style={{
          fontSize: theme.typography.fontSize.sm,
          color: theme.colors.text.secondary,
        }}>
          Number of Projects: 15,191
        </div>
      </div>

      {/* TTM Value KPI */}
      <div
        style={{
          gridColumn: 'span 3',
          gridRow: 'span 2',
          background: theme.colors.surface,
          backdropFilter: `blur(${theme.blur.lg})`,
          WebkitBackdropFilter: `blur(${theme.blur.lg})`,
          borderRadius: theme.borderRadius.lg,
          boxShadow: theme.shadows.glass,
          padding: theme.spacing.lg,
          border: `1px solid ${theme.colors.border.glass}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <h3 style={{ 
          margin: 0, 
          marginBottom: theme.spacing.sm,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.lg,
        }}>
          TTM Value
        </h3>
        <div style={{
          fontSize: '2.5rem',
          fontWeight: theme.typography.fontWeight.bold,
          color: '#6EDA55',
          marginBottom: theme.spacing.xs,
        }}>
          $124.2B
        </div>
        <div style={{
          fontSize: theme.typography.fontSize.sm,
          color: theme.colors.text.secondary,
        }}>
          Number of Projects: 38,711
        </div>
      </div>
    </>
  );

  const renderMonthlyCharts = () => (
    <>
      <DemoChart
        title="Monthly Value Trend"
        position={{ x: 0, y: 4, w: 4, h: 4 }}
        type="line"
        color="#00cee6"
        formatType="currency"
      />

      <DemoChart
        title="Monthly Value by Mode"
        position={{ x: 4, y: 4, w: 4, h: 4 }}
        type="pie"
        color="#00cee6"
        formatType="percentage"
      />

      <DemoChart
        title="Monthly Value by State (Top 10)"
        position={{ x: 8, y: 4, w: 4, h: 4 }}
        type="bar"
        color="#00cee6"
        formatType="currency"
      />
    </>
  );

  const renderYTDCharts = () => (
    <>
      <DemoChart
        title="YTD Value Progression"
        position={{ x: 0, y: 4, w: 4, h: 4 }}
        type="line"
        color="#9b9bd7"
        formatType="currency"
      />

      <DemoChart
        title="YTD Value by Mode"
        position={{ x: 4, y: 4, w: 4, h: 4 }}
        type="pie"
        color="#9b9bd7"
        formatType="percentage"
      />

      <DemoChart
        title="YTD Value by State"
        position={{ x: 8, y: 4, w: 4, h: 4 }}
        type="bar"
        color="#9b9bd7"
        formatType="currency"
      />
    </>
  );

  const renderTTMCharts = () => (
    <>
      <DemoChart
        title="TTM Value Trend"
        position={{ x: 0, y: 4, w: 4, h: 4 }}
        type="line"
        color="#6EDA55"
        formatType="currency"
      />

      <DemoChart
        title="TTM Value by Mode"
        position={{ x: 4, y: 4, w: 4, h: 4 }}
        type="pie"
        color="#6EDA55"
        formatType="percentage"
      />

      <DemoChart
        title="TTM Value by State"
        position={{ x: 8, y: 4, w: 4, h: 4 }}
        type="bar"
        color="#6EDA55"
        formatType="currency"
      />
    </>
  );

  const renderAnnualCharts = () => (
    <>
      <DemoChart
        title="Annual Value by Year"
        position={{ x: 0, y: 4, w: 4, h: 4 }}
        type="bar"
        color="#fc7570"
        formatType="currency"
        data={[
          { state: '2020', value: 89200000000 },
          { state: '2021', value: 95600000000 },
          { state: '2022', value: 108300000000 },
          { state: '2023', value: 118700000000 },
          { state: '2024', value: 124200000000 },
        ]}
      />

      <DemoChart
        title="2024 Annual Value by Mode"
        position={{ x: 4, y: 4, w: 4, h: 4 }}
        type="pie"
        color="#fc7570"
        formatType="percentage"
      />

      <DemoChart
        title="2024 Value by State"
        position={{ x: 8, y: 4, w: 4, h: 4 }}
        type="bar"
        color="#fc7570"
        formatType="currency"
      />
    </>
  );

  const renderActiveTabContent = () => {
    switch (activeTab) {
      case 'MONTH':
        return renderMonthlyCharts();
      case 'YTD':
        return renderYTDCharts();
      case 'TTM':
        return renderTTMCharts();
      case 'ANNUAL':
        return renderAnnualCharts();
      default:
        return renderMonthlyCharts();
    }
  };

  return (
    <PageLayout title="ARTBA Economics Dashboard">
      <DashboardLayout
        title={`${title} (Custom Charts)`}
        description={description}
        actions={dashboardActions}
      >
        {/* KPI Cards Row */}
        {renderKPICards()}
        
        {/* Tab Navigation */}
        <div style={{ gridColumn: 'span 12', gridRow: 'span 1' }}>
          {renderTabNavigation()}
        </div>
        
        {/* Dynamic Chart Content Based on Active Tab */}
        {renderActiveTabContent()}
      </DashboardLayout>
    </PageLayout>
  );
};
