import type { DashboardConfig } from '../../../types/dashboard.types';

export const materialPricesDashboardConfig: DashboardConfig = {
  id: '622a674aca7c6135304beb5b',
  title: '9. Material Prices',
  description: 'Construction material price indices and trends',
  path: '/dashboard/material-prices',
  widgets: [
    // Row 1 - Overall indices
    {
      id: '622a674aca7c6135304beb5e',
      type: 'chart',
      title: 'Construction, Highway and Street Inputs',
      position: { x: 0, y: 0, w: 6, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb5f',
      type: 'chart',
      title: 'Highway & Street Construction Inputs',
      position: { x: 6, y: 0, w: 6, h: 4 },
      config: {
        chartType: 'area',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    // Row 2 - Major components
    {
      id: '622a674aca7c6135304beb60',
      type: 'chart',
      title: 'Major Components of the Highway & Street Construction Inputs',
      position: { x: 0, y: 4, w: 12, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Component' }],
          value: [{ name: 'Index Value', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - Inputs and commodities table
    {
      id: '622a674aca7c6135304beb61',
      type: 'table',
      title: 'Major Inputs and Commodities for Transportation Construction (base years may differ)',
      position: { x: 0, y: 8, w: 12, h: 4 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'Input/Commodity' } },
            { column: { name: 'Current Index' } },
            { column: { name: 'YoY Change %' } },
            { column: { name: 'Base Year' } },
            { column: { name: 'Weight' } },
          ],
        },
      },
    },
    // Row 4 - Producer price indices
    {
      id: '622a674aca7c6135304beb63',
      type: 'chart',
      title: 'Producer Price Indices for Transportation Construction and Commodities',
      position: { x: 0, y: 12, w: 6, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Index', aggregation: 'avg' }],
          breakBy: [{ name: 'Category' }],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb64',
      type: 'chart',
      title: 'Producer Price Indices for Major Commodities',
      position: { x: 6, y: 12, w: 6, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Commodity' }],
          value: [{ name: 'Current Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - Individual commodity charts
    {
      id: '622a674aca7c6135304beb65',
      type: 'chart',
      title: 'Asphalt',
      position: { x: 0, y: 16, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb66',
      type: 'chart',
      title: 'Construction sand and gravel (aggregates)',
      position: { x: 4, y: 16, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb67',
      type: 'chart',
      title: 'Machinery and equipment: mixers, pavers, and related equipment',
      position: { x: 8, y: 16, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    // Row 6 - More commodity charts
    {
      id: '622a674aca7c6135304beb68',
      type: 'chart',
      title: 'Diesel Fuel',
      position: { x: 0, y: 19, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb69',
      type: 'chart',
      title: 'Ready-Mix Concrete',
      position: { x: 4, y: 19, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '622a674aca7c6135304beb6a',
      type: 'chart',
      title: 'Concrete Block and Brick',
      position: { x: 8, y: 19, w: 4, h: 3 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Date' }],
          value: [{ name: 'Price Index', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
  ],
};