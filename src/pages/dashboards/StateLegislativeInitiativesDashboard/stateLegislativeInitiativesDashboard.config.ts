import type { DashboardConfig } from '../../../types/dashboard.types';

export const stateLegislativeInitiativesDashboardConfig: DashboardConfig = {
  id: '61e84f9771137213003b976f',
  title: '6. State Legislative Initiatives',
  description: 'State transportation funding legislative initiatives tracking',
  path: '/dashboard/state-legislative-initiatives',
  widgets: [
    // Row 1 - Revenue increases and measures count
    {
      id: '61e84f9771137213003b9772',
      type: 'kpi',
      title: 'Revenue Increases Proposed in Legislation',
      position: { x: 0, y: 0, w: 6, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '61e84f9771137213003b9778',
      type: 'kpi',
      title: 'Number of Measures Introduced in 2025',
      position: { x: 6, y: 0, w: 6, h: 2 },
      config: {
        value: 0,
        format: 'number',
      },
    },
    // Row 2 - Map of legislative initiatives by state
    {
      id: '61e84f9771137213003b9775',
      type: 'chart',
      title: 'State Legislative Initiatives Map',
      position: { x: 0, y: 2, w: 12, h: 6 },
      config: {
        chartType: 'scatter', // Map visualization in actual implementation
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Number of Initiatives', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - Legislation details table
    {
      id: '61e84f9771137213003b977c',
      type: 'table',
      title: 'Legislation Introduced',
      position: { x: 0, y: 8, w: 12, h: 8 },
      config: {
        dataOptions: {
          columns: [
            { column: { name: 'State' } },
            { column: { name: 'Bill Number' } },
            { column: { name: 'Description' } },
            { column: { name: 'Revenue Type' } },
            { column: { name: 'Estimated Revenue' } },
            { column: { name: 'Status' } },
            { column: { name: 'Last Action' } },
          ],
        },
      },
    },
  ],
};