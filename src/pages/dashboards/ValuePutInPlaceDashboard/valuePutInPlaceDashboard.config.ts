import type { DashboardConfig } from '../../../types/dashboard.types';

export const valuePutInPlaceDashboardConfig: DashboardConfig = {
  id: '5defbf594faf0a20b09a2176',
  title: '3. Value Put in Place',
  description: 'Transportation construction value put in place analysis',
  path: '/dashboard/value-put-in-place',
  widgets: [
    // Row 1 - Monthly metrics
    {
      id: '5defbf594faf0a20b09a217d',
      type: 'chart',
      title: 'Monthly Value',
      position: { x: 0, y: 0, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defbf594faf0a20b09a2186',
      type: 'kpi',
      title: 'Monthly Value',
      position: { x: 4, y: 0, w: 2, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defbf594faf0a20b09a2180',
      type: 'chart',
      title: 'Monthly Value Breakdown by Mode',
      position: { x: 6, y: 0, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 2 - YTD metrics
    {
      id: '5defbf594faf0a20b09a217e',
      type: 'kpi',
      title: 'YTD Value',
      position: { x: 0, y: 4, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defbf594faf0a20b09a2183',
      type: 'chart',
      title: 'YTD Value',
      position: { x: 3, y: 4, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e20bece836b0701b460500f',
      type: 'chart',
      title: 'YTD Value Breakdown by Mode',
      position: { x: 7, y: 4, w: 5, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - TTM metrics
    {
      id: '5defbf594faf0a20b09a2185',
      type: 'kpi',
      title: 'TTM Value',
      position: { x: 0, y: 8, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defbf594faf0a20b09a2187',
      type: 'chart',
      title: 'TTM Value',
      position: { x: 3, y: 8, w: 4, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e20c099836b0701b4605019',
      type: 'chart',
      title: 'TTM Value Breakdown by Mode',
      position: { x: 7, y: 8, w: 5, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 4 - Percentage changes
    {
      id: '5defbf594faf0a20b09a2181',
      type: 'chart',
      title: '% Change in Monthly Value by Mode',
      position: { x: 0, y: 12, w: 4, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Percentage Change', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defbf594faf0a20b09a2184',
      type: 'chart',
      title: '% Change in YTD Value by Mode',
      position: { x: 4, y: 12, w: 4, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Percentage Change', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defbf594faf0a20b09a2188',
      type: 'chart',
      title: '% Change in TTM Value by Mode',
      position: { x: 8, y: 12, w: 4, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Percentage Change', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - Annual metrics
    {
      id: '5e6124093073732b6c20f0f0',
      type: 'chart',
      title: 'Annual Value',
      position: { x: 0, y: 16, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e612ed53073732b6c20f1d4',
      type: 'chart',
      title: 'Annual Value',
      position: { x: 4, y: 16, w: 4, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e612e8d3073732b6c20f1ce',
      type: 'chart',
      title: '2024 Annual Value Breakdown by Mode',
      position: { x: 8, y: 16, w: 4, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 6 - Annual comparison
    {
      id: '5e6124973073732b6c20f0f6',
      type: 'chart',
      title: '% Change in Annual Value by Mode, 2024 vs. 2023',
      position: { x: 0, y: 20, w: 12, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Percentage Change', aggregation: 'avg' }],
          breakBy: [],
        },
      },
    },
  ],
};