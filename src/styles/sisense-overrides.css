/* Sisense Widget Border Removal and Styling Overrides */

/* Remove borders from all Sisense widget containers */
.sisense-widget,
.widget-container,
.widget-content,
.widget-wrapper,
.chart-container,
.highcharts-container,
.sisense-chart,
.sisense-table,
.sisense-pivot,
.sisense-kpi {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: transparent !important;
}

/* Remove specific Sisense border elements */
.widget-border,
.widget-frame,
.sisense-border,
.chart-border,
.table-border {
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Remove borders from Sisense chart elements */
.highcharts-container,
.highcharts-root,
.highcharts-background {
  border: none !important;
  outline: none !important;
}

/* Remove borders from table elements */
.sisense-table table,
.sisense-table thead,
.sisense-table tbody,
.sisense-table tr,
.sisense-table td,
.sisense-table th {
  border: none !important;
  outline: none !important;
}

/* Remove borders from pivot table elements */
.sisense-pivot .pivot-table,
.sisense-pivot .pivot-header,
.sisense-pivot .pivot-cell {
  border: none !important;
  outline: none !important;
}

/* Remove borders from KPI widgets */
.sisense-kpi .kpi-container,
.sisense-kpi .kpi-value,
.sisense-kpi .kpi-title {
  border: none !important;
  outline: none !important;
}

/* Remove any remaining widget chrome */
[class*="widget"] {
  border: none !important;
  box-shadow: none !important;
}

[class*="sisense"] {
  border: none !important;
  box-shadow: none !important;
}

/* Ensure transparent backgrounds for seamless integration */
.sisense-widget > div,
.widget-container > div,
.chart-container > div {
  background: transparent !important;
  border: none !important;
}

/* Remove focus outlines that might appear as borders */
.sisense-widget:focus,
.widget-container:focus,
.chart-container:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Remove any SVG borders in charts */
.sisense-widget svg,
.chart-container svg,
.highcharts-container svg {
  border: none !important;
  outline: none !important;
}

/* Remove borders from legend elements */
.highcharts-legend,
.highcharts-legend-item,
.chart-legend {
  border: none !important;
  outline: none !important;
}

/* Remove borders from tooltip elements */
.highcharts-tooltip,
.chart-tooltip,
.sisense-tooltip {
  border: none !important;
  outline: none !important;
}

/* Ensure no borders on data labels */
.highcharts-data-label,
.chart-data-label {
  border: none !important;
  outline: none !important;
}

/* Remove any iframe borders if Sisense uses them */
iframe[src*="sisense"] {
  border: none !important;
  outline: none !important;
}

/* Additional safety net for any remaining borders */
*[class*="sisense"]:not(input):not(button):not(select),
*[class*="widget"]:not(input):not(button):not(select),
*[class*="chart"]:not(input):not(button):not(select) {
  border: none !important;
  box-shadow: none !important;
}

/* Ensure proper integration with liquid glass theme */
.sisense-widget {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Remove any default margins that might create visual borders */
.sisense-widget,
.widget-container,
.chart-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure full width/height utilization */
.sisense-widget,
.widget-container,
.chart-container {
  width: 100% !important;
  height: 100% !important;
}
