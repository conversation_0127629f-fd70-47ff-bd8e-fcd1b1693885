/* Sisense Widget Border Removal and Styling Overrides */

/* Remove borders from all Sisense widget containers */
.sisense-widget,
.widget-container,
.widget-content,
.widget-wrapper,
.chart-container,
.highcharts-container,
.sisense-chart,
.sisense-table,
.sisense-pivot,
.sisense-kpi {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background: transparent !important;
}

/* Remove specific Sisense border elements */
.widget-border,
.widget-frame,
.sisense-border,
.chart-border,
.table-border {
  border: none !important;
  border-width: 0 !important;
  border-style: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Remove borders from Sisense chart elements */
.highcharts-container,
.highcharts-root,
.highcharts-background {
  border: none !important;
  outline: none !important;
}

/* Remove borders from table elements */
.sisense-table table,
.sisense-table thead,
.sisense-table tbody,
.sisense-table tr,
.sisense-table td,
.sisense-table th {
  border: none !important;
  outline: none !important;
}

/* Remove borders from pivot table elements */
.sisense-pivot .pivot-table,
.sisense-pivot .pivot-header,
.sisense-pivot .pivot-cell {
  border: none !important;
  outline: none !important;
}

/* Remove borders from KPI widgets */
.sisense-kpi .kpi-container,
.sisense-kpi .kpi-value,
.sisense-kpi .kpi-title {
  border: none !important;
  outline: none !important;
}

/* Remove any remaining widget chrome */
[class*="widget"] {
  border: none !important;
  box-shadow: none !important;
}

[class*="sisense"] {
  border: none !important;
  box-shadow: none !important;
}

/* Ensure transparent backgrounds for seamless integration */
.sisense-widget > div,
.widget-container > div,
.chart-container > div {
  background: transparent !important;
  border: none !important;
}

/* Remove focus outlines that might appear as borders */
.sisense-widget:focus,
.widget-container:focus,
.chart-container:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Remove any SVG borders in charts */
.sisense-widget svg,
.chart-container svg,
.highcharts-container svg {
  border: none !important;
  outline: none !important;
}

/* Remove borders from legend elements */
.highcharts-legend,
.highcharts-legend-item,
.chart-legend {
  border: none !important;
  outline: none !important;
}

/* Remove borders from tooltip elements */
.highcharts-tooltip,
.chart-tooltip,
.sisense-tooltip {
  border: none !important;
  outline: none !important;
}

/* Ensure no borders on data labels */
.highcharts-data-label,
.chart-data-label {
  border: none !important;
  outline: none !important;
}

/* Remove any iframe borders if Sisense uses them */
iframe[src*="sisense"] {
  border: none !important;
  outline: none !important;
}

/* Additional safety net for any remaining borders */
*[class*="sisense"]:not(input):not(button):not(select),
*[class*="widget"]:not(input):not(button):not(select),
*[class*="chart"]:not(input):not(button):not(select) {
  border: none !important;
  box-shadow: none !important;
}

/* Ensure proper integration with liquid glass theme */
.sisense-widget {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Remove any default margins that might create visual borders */
.sisense-widget,
.widget-container,
.chart-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure full width/height utilization */
.sisense-widget,
.widget-container,
.chart-container {
  width: 100% !important;
  height: 100% !important;
}

/* Hide info buttons and help icons */
.widget-info-button,
.widget-help-button,
.sisense-info-button,
.sisense-help-button,
.chart-info-button,
.info-icon,
.help-icon,
.widget-toolbar,
.widget-menu,
.widget-options,
.widget-settings {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Hide any circular info buttons */
[class*="info"]:not(input):not(textarea),
[class*="help"]:not(input):not(textarea),
[class*="tooltip"]:not(.custom-tooltip) {
  display: none !important;
}

/* Hide specific Sisense UI elements */
.sisense-widget .widget-header-actions,
.sisense-widget .widget-actions,
.sisense-widget .widget-menu-button,
.sisense-widget .widget-info,
.sisense-widget .widget-help {
  display: none !important;
}

/* Hide any SVG info icons */
svg[class*="info"],
svg[class*="help"],
.sisense-widget svg[data-testid*="info"],
.sisense-widget svg[data-testid*="help"] {
  display: none !important;
}

/* Hide buttons with info/help content */
button[title*="info" i],
button[title*="help" i],
button[aria-label*="info" i],
button[aria-label*="help" i] {
  display: none !important;
}

/* Hide any remaining widget chrome buttons */
.widget-chrome button,
.widget-header button,
.chart-header button {
  display: none !important;
}

/* Target specific Sisense info button patterns */
[class*="widget"] button[class*="info"],
[class*="widget"] button[class*="help"],
[class*="sisense"] button[class*="info"],
[class*="sisense"] button[class*="help"],
[class*="chart"] button[class*="info"],
[class*="chart"] button[class*="help"] {
  display: none !important;
}

/* Hide buttons with specific data attributes */
button[data-testid*="info"],
button[data-testid*="help"],
button[data-qa*="info"],
button[data-qa*="help"] {
  display: none !important;
}

/* Hide any element with info/help in class name */
[class*="info-btn"],
[class*="help-btn"],
[class*="info-button"],
[class*="help-button"],
[class*="widget-info"],
[class*="widget-help"] {
  display: none !important;
}

/* Hide circular buttons that might be info buttons */
button[style*="border-radius: 50%"],
button[style*="border-radius:50%"],
.circular-button,
.round-button {
  display: none !important;
}

/* Additional safety net for any remaining UI chrome */
.sisense-widget > div > div > button:last-child,
.widget-container > div > div > button:last-child {
  display: none !important;
}

/* Target the specific circular info button with "i" icon */
button:has(svg),
button:has([class*="icon"]),
button[style*="position: absolute"][style*="top:"],
button[style*="position: absolute"][style*="right:"] {
  display: none !important;
}

/* Hide any button in the top-right corner of widgets */
.sisense-widget button[style*="position: absolute"],
.widget-container button[style*="position: absolute"],
.chart-container button[style*="position: absolute"] {
  display: none !important;
}

/* Hide buttons that contain only icons or single characters */
button:empty,
button:has(> svg:only-child),
button:has(> span:only-child:empty),
button[innerHTML="i"],
button[textContent="i"] {
  display: none !important;
}

/* Very specific targeting for Sisense info buttons */
.sisense-widget [role="button"],
.widget-container [role="button"],
.chart-container [role="button"] {
  display: none !important;
}

/* Hide any floating buttons over widgets */
.sisense-widget > div > button,
.widget-container > div > button,
.chart-container > div > button {
  display: none !important;
}
