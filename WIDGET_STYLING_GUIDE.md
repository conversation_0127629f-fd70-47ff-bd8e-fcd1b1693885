# 🎨 Sisense Widget Styling Guide

This guide explains how to style Sisense widgets in the ARTBA Economics Dashboard to match the liquid glass theme and create professional, visually appealing dashboards.

## 📋 Table of Contents

1. [Basic Widget Styling](#basic-widget-styling)
2. [Advanced Styling Options](#advanced-styling-options)
3. [Theme Integration](#theme-integration)
4. [Styling Utilities](#styling-utilities)
5. [Examples](#examples)
6. [Best Practices](#best-practices)

## 🎯 Basic Widget Styling

### Using styleOptions with WidgetById

```tsx
import { WidgetById } from '@sisense/sdk-ui';

<WidgetById
  widgetOid="your-widget-id"
  dashboardOid="your-dashboard-id"
  styleOptions={{
    height: 400,
    width: '100%',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '16px',
  }}
/>
```

### Common Style Properties

| Property | Type | Description | Example |
|----------|------|-------------|---------|
| `height` | number | Widget height in pixels | `400` |
| `width` | string/number | Widget width | `'100%'` or `500` |
| `backgroundColor` | string | Background color | `'transparent'` |
| `border` | string | Border styling | `'none'` or `'1px solid #ccc'` |
| `borderRadius` | string | Corner rounding | `'16px'` |

## 🚀 Advanced Styling Options

### Chart Styling

```tsx
styleOptions={{
  chart: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    textColor: '#1e293b',
    gridLineColor: 'rgba(255, 107, 53, 0.2)',
    plotBackgroundColor: 'transparent',
  }
}}
```

### Legend Styling

```tsx
styleOptions={{
  legend: {
    enabled: true,
    position: 'bottom', // 'top', 'bottom', 'left', 'right'
    backgroundColor: 'transparent',
    itemStyle: {
      color: '#64748b',
      fontSize: '12px',
      fontWeight: '500',
    }
  }
}}
```

### Axis Styling

```tsx
styleOptions={{
  xAxis: {
    enabled: true,
    gridLines: false,
    lineColor: 'rgba(255, 107, 53, 0.3)',
    labels: {
      color: '#64748b',
      fontSize: '12px',
      fontWeight: '500',
    }
  },
  yAxis: {
    enabled: true,
    gridLines: true,
    gridLineColor: 'rgba(255, 107, 53, 0.1)',
    lineColor: 'rgba(255, 107, 53, 0.3)',
    labels: {
      color: '#64748b',
      fontSize: '12px',
      fontWeight: '500',
    }
  }
}}
```

### Tooltip Styling

```tsx
styleOptions={{
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '12px',
    borderColor: 'rgba(255, 107, 53, 0.3)',
    borderWidth: 1,
    shadow: true,
    style: {
      color: '#1e293b',
      fontSize: '13px',
      fontWeight: '500',
    }
  }
}}
```

### Series Colors

```tsx
styleOptions={{
  seriesColors: [
    '#ff6b35', // Primary orange
    '#ffa947', // Secondary orange
    '#ff4757', // Accent red
    '#00cee6', // Cyan
    '#6EDA55', // Green
    '#9b9bd7', // Purple
  ]
}}
```

## 🎨 Theme Integration

### Using the Styling Utility

```tsx
import { createWidgetStyleOptions } from '../utils/widgetStyling';
import { useTheme } from '../hooks/useTheme';

const { theme } = useTheme();

const styleOptions = createWidgetStyleOptions(theme, {
  height: 400,
  // Custom overrides
  seriesColors: ['#ff6b35', '#00cee6', '#6EDA55'],
});
```

### Widget Type-Specific Styling

```tsx
import { createWidgetStylesByType } from '../utils/widgetStyling';

const styleOptions = createWidgetStylesByType(
  theme,
  'chart', // 'chart' | 'kpi' | 'table' | 'pivot'
  400 // height
);
```

## 🛠️ Styling Utilities

### Available Presets

```tsx
import { widgetStylePresets } from '../utils/widgetStyling';

// Minimal styling
const minimalStyles = widgetStylePresets.minimal(theme);

// Enhanced glass effect
const glassStyles = widgetStylePresets.glassEnhanced(theme);

// Dark mode compatible
const darkStyles = widgetStylePresets.darkMode(theme);
```

### Custom Wrapper Styling

```tsx
<div style={{
  background: 'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.6))',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  border: '1px solid rgba(255,255,255,0.3)',
  padding: '20px',
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
}}>
  <WidgetById
    widgetOid={widgetId}
    dashboardOid={dashboardId}
    styleOptions={styleOptions}
  />
</div>
```

## 📊 Examples

### 1. KPI Widget with Glass Effect

```tsx
<WidgetById
  widgetOid="kpi-widget-id"
  dashboardOid="dashboard-id"
  styleOptions={{
    height: 200,
    backgroundColor: 'transparent',
    chart: {
      backgroundColor: 'rgba(255, 255, 255, 0.6)',
    },
    legend: {
      enabled: false,
    },
    seriesColors: ['#ff6b35'],
  }}
/>
```

### 2. Table Widget with Enhanced Styling

```tsx
<WidgetById
  widgetOid="table-widget-id"
  dashboardOid="dashboard-id"
  styleOptions={{
    height: 500,
    minHeight: 400,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '16px',
    chart: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
    },
  }}
/>
```

### 3. Chart with Custom Colors and Grid

```tsx
<WidgetById
  widgetOid="chart-widget-id"
  dashboardOid="dashboard-id"
  styleOptions={{
    height: 400,
    seriesColors: ['#ff6b35', '#00cee6', '#6EDA55', '#9b9bd7'],
    xAxis: {
      gridLines: true,
      lineColor: '#ff6b35',
    },
    yAxis: {
      gridLines: true,
      gridLineColor: 'rgba(255, 107, 53, 0.1)',
    },
    tooltip: {
      backgroundColor: '#ff6b35',
      style: {
        color: 'white',
        fontWeight: 'bold',
      }
    }
  }}
/>
```

## ✅ Best Practices

### 1. **Consistency**
- Use the theme-based styling utilities for consistent appearance
- Stick to the established color palette
- Maintain consistent spacing and sizing

### 2. **Performance**
- Avoid overly complex styling that might impact rendering
- Use CSS-in-JS sparingly for dynamic styles
- Prefer theme-based colors over hardcoded values

### 3. **Accessibility**
- Ensure sufficient color contrast
- Use readable font sizes (minimum 12px)
- Provide alternative text for visual elements

### 4. **Responsive Design**
- Use percentage-based widths when possible
- Set appropriate minimum heights for different widget types
- Test on different screen sizes

### 5. **Theme Integration**
- Always use the theme object for colors and spacing
- Leverage the liquid glass design system
- Maintain visual hierarchy with proper typography

## 🔧 Troubleshooting

### Common Issues

1. **Styles not applying**: Ensure you're using the correct property names as per Sisense documentation
2. **Colors not showing**: Check that color values are valid CSS colors
3. **Layout issues**: Verify height and width values are appropriate for the container
4. **Theme conflicts**: Make sure custom styles don't override important theme properties

### Debug Tips

```tsx
// Add debug logging to see applied styles
console.log('Applied styleOptions:', styleOptions);

// Use browser dev tools to inspect the rendered widget
// Look for Sisense-specific CSS classes and properties
```

## 📚 Additional Resources

- [Sisense SDK Documentation](https://developer.sisense.com/guides/sdk/)
- [WidgetById StyleOptions Reference](https://developer.sisense.com/guides/sdk/modules/sdk-ui/interfaces/interface.WidgetByIdStyleOptions.html)
- [Theme Configuration Guide](./src/config/themes/README.md)
- [Widget Examples](./src/components/widgets/StyledWidgetExamples.tsx)
