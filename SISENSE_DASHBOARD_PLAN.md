# Sisense Dashboard Project Plan

## Project Overview
Build a scalable multi-dashboard application using Sisense Compose SDK with a landing page and consistent UI across all dashboards.

## Tech Stack
- React + TypeScript
- Vite (existing setup)
- Sisense Compose SDK (@sisense/sdk-ui, @sisense/sdk-data)
- React Router for navigation
- Tailwind CSS or styled-components for consistent styling

## Folder Structure

```
src/
├── components/
│   ├── common/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Footer.tsx
│   │   └── LoadingSpinner.tsx
│   ├── layout/
│   │   ├── DashboardLayout.tsx
│   │   ├── PageLayout.tsx
│   │   └── WidgetContainer.tsx
│   └── widgets/
│       ├── ChartWidget/
│       │   ├── ChartWidget.tsx
│       │   ├── ChartWidget.types.ts
│       │   └── ChartWidget.styles.ts
│       ├── KPIWidget/
│       │   ├── KPIWidget.tsx
│       │   └── KPIWidget.styles.ts
│       └── TableWidget/
│           ├── TableWidget.tsx
│           └── TableWidget.styles.ts
├── pages/
│   ├── Landing/
│   │   ├── Landing.tsx
│   │   ├── Landing.styles.ts
│   │   └── DashboardCard.tsx
│   └── dashboards/
│       ├── SalesDashboard/
│       │   ├── SalesDashboard.tsx
│       │   ├── SalesDashboard.config.ts
│       │   └── widgets/
│       │       ├── RevenueChart.tsx
│       │       ├── SalesKPI.tsx
│       │       └── ProductTable.tsx
│       ├── MarketingDashboard/
│       │   ├── MarketingDashboard.tsx
│       │   ├── MarketingDashboard.config.ts
│       │   └── widgets/
│       └── FinanceDashboard/
│           ├── FinanceDashboard.tsx
│           ├── FinanceDashboard.config.ts
│           └── widgets/
├── config/
│   ├── sisense.config.ts
│   ├── dataModels.ts
│   └── theme.config.ts
├── hooks/
│   ├── useSisenseAuth.ts
│   ├── useDataFetch.ts
│   └── useChartConfig.ts
├── services/
│   ├── sisense.service.ts
│   ├── auth.service.ts
│   └── data.service.ts
├── types/
│   ├── dashboard.types.ts
│   ├── widget.types.ts
│   └── sisense.types.ts
├── utils/
│   ├── chartHelpers.ts
│   ├── dataFormatters.ts
│   └── constants.ts
└── styles/
    ├── globals.css
    ├── variables.css
    └── mixins.css
```

## Implementation Steps

### Phase 1: Setup & Configuration
1. Install Sisense SDK packages
2. Configure Sisense connection and authentication
3. Set up routing structure
4. Create theme configuration for consistent UI

### Phase 2: Core Components
1. Build reusable widget wrapper components
2. Create dashboard layout template
3. Implement navigation components
4. Build loading and error states

### Phase 3: Widget Templates
1. Create ChartWidget template with configuration options
2. Build KPI widget template
3. Create table widget template
4. Implement filter components

### Phase 4: Dashboard Implementation
1. Create landing page with dashboard cards
2. Build first dashboard (Sales) as template
3. Implement remaining dashboards
4. Add interactivity and drill-down features

### Phase 5: Polish & Optimization
1. Implement responsive design
2. Add animations and transitions
3. Optimize performance
4. Add error handling and logging

## Key Design Patterns

### 1. Widget Factory Pattern
```typescript
// widgets/WidgetFactory.tsx
export const createWidget = (type: WidgetType, config: WidgetConfig) => {
  switch(type) {
    case 'chart':
      return <ChartWidgetTemplate {...config} />
    case 'kpi':
      return <KPIWidgetTemplate {...config} />
    // ...
  }
}
```

### 2. Dashboard Configuration
```typescript
// dashboards/SalesDashboard/SalesDashboard.config.ts
export const salesDashboardConfig = {
  title: 'Sales Dashboard',
  description: 'Key sales metrics and trends',
  widgets: [
    {
      id: 'revenue-chart',
      type: 'chart',
      position: { x: 0, y: 0, w: 6, h: 4 },
      config: {
        chartType: 'bar',
        dataOptions: {
          category: [DM.Commerce.Date],
          value: [measureFactory.sum(DM.Commerce.Revenue)],
        }
      }
    },
    // ... more widgets
  ]
}
```

### 3. Consistent UI Theme
```typescript
// config/theme.config.ts
export const theme = {
  colors: {
    primary: '#1976d2',
    secondary: '#dc004e',
    background: '#f5f5f5',
    surface: '#ffffff',
  },
  spacing: {
    widget: 16,
    dashboard: 24,
  },
  borderRadius: {
    widget: 8,
    card: 12,
  },
  shadows: {
    widget: '0 2px 8px rgba(0,0,0,0.1)',
  }
}
```

## Best Practices

1. **Component Composition**: Use small, reusable components
2. **Type Safety**: Leverage TypeScript for all Sisense SDK interactions
3. **Error Boundaries**: Wrap widgets in error boundaries
4. **Performance**: Use React.memo for widget optimization
5. **Accessibility**: Ensure all charts have proper ARIA labels
6. **Responsive Design**: Make dashboards work on all screen sizes

## Next Steps

1. Set up the project structure
2. Install required dependencies
3. Create base components and layouts
4. Build first dashboard as proof of concept
5. Iterate and expand based on requirements